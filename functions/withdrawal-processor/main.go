// Package withdrawalprocessor provides a Cloud Function for processing withdrawal payouts and refreshing balance views.
package withdrawalprocessor

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/GoogleCloudPlatform/functions-framework-go/functions"
)

// PayoutProcessorRequest represents the Cloud Scheduler payload
type PayoutProcessorRequest struct {
	Action string `json:"action"` // "update_payouts" or "refresh_balance_view"
}

// PayoutProcessorResponse represents the function response
type PayoutProcessorResponse struct {
	Success       bool   `json:"success"`
	UpdatedCount  int    `json:"updated_count,omitempty"`
	Message       string `json:"message"`
	ExecutionTime string `json:"execution_time"`
	Timestamp     string `json:"timestamp"`
}

func init() {
	functions.HTTP("ProcessWithdrawalPayouts", processWithdrawalPayouts)
}

// processWithdrawalPayouts is the Cloud Function entry point
func processWithdrawalPayouts(w http.ResponseWriter, r *http.Request) {
	start := time.Now()
	ctx := r.Context()

	// Set CORS headers for potential web access
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")

	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	if r.Method != "POST" {
		http.Error(w, "Only POST method is allowed", http.StatusMethodNotAllowed)
		return
	}

	// Parse request
	var req PayoutProcessorRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("Invalid request body: %v", err)
		http.Error(w, "Invalid request body", http.StatusBadRequest)
		return
	}

	// Validate action
	if req.Action != "update_payouts" && req.Action != "refresh_balance_view" {
		log.Printf("Invalid action: %s", req.Action)
		http.Error(w, "Invalid action. Must be 'update_payouts' or 'refresh_balance_view'", http.StatusBadRequest)
		return
	}

	// Connect to database
	db, err := connectToDatabase()
	if err != nil {
		log.Printf("Database connection failed: %v", err)
		http.Error(w, "Database connection failed", http.StatusInternalServerError)
		return
	}
	defer db.Close()

	var response PayoutProcessorResponse

	switch req.Action {
	case "update_payouts":
		response = updatePayoutStatuses(ctx, db)
	case "refresh_balance_view":
		response = refreshBalanceView(ctx, db)
	}

	// Add execution metadata
	response.ExecutionTime = time.Since(start).String()
	response.Timestamp = time.Now().UTC().Format(time.RFC3339)

	// Log the result
	if response.Success {
		log.Printf("Action '%s' completed successfully in %s", req.Action, response.ExecutionTime)
	} else {
		log.Printf("Action '%s' failed: %s", req.Action, response.Message)
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// connectToDatabase establishes database connection using environment variables
func connectToDatabase() (*sql.DB, error) {
	// Get database configuration from environment variables
	databaseURL := os.Getenv("DATABASE_URL")

	// Validate required environment variables
	if databaseURL == "" {
		return nil, fmt.Errorf("missing required database environment variables")
	}

	// Open database connection
	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to open database connection: %w", err)
	}

	// Configure connection pool for Cloud Function
	db.SetMaxOpenConns(5) // Limit connections for serverless
	db.SetMaxIdleConns(2) // Keep minimal idle connections
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return db, nil
}

// updatePayoutStatuses updates pending payouts to available status
func updatePayoutStatuses(ctx context.Context, db *sql.DB) PayoutProcessorResponse {
	query := `
		UPDATE invoice_payouts 
		SET status = 'available', updated_at = NOW()
		WHERE status = 'pending' 
		  AND available_after <= NOW()
	`

	result, err := db.ExecContext(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to update payout statuses: %v", err),
		}
	}

	rowsAffected, _ := result.RowsAffected()

	return PayoutProcessorResponse{
		Success:      true,
		UpdatedCount: int(rowsAffected),
		Message:      fmt.Sprintf("Successfully updated %d payouts from pending to available", rowsAffected),
	}
}

// refreshBalanceView refreshes the materialized view for fast balance calculations
func refreshBalanceView(ctx context.Context, db *sql.DB) PayoutProcessorResponse {
	query := `REFRESH MATERIALIZED VIEW company_available_balances`

	_, err := db.ExecContext(ctx, query)
	if err != nil {
		return PayoutProcessorResponse{
			Success: false,
			Message: fmt.Sprintf("Failed to refresh balance view: %v", err),
		}
	}

	return PayoutProcessorResponse{
		Success: true,
		Message: "Successfully refreshed company available balances materialized view",
	}
}
