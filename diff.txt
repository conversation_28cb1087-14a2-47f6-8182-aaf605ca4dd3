diff --git a/OWNER_MANAGEMENT_README.md b/OWNER_MANAGEMENT_README.md
new file mode 100644
index 0000000..638f529
--- /dev/null
+++ b/OWNER_MANAGEMENT_README.md
@@ -0,0 +1,105 @@
+# Owner Management Feature
+
+## Overview
+The Owner Management feature allows administrators to search for users and assign them as owners of companies. This feature is only available in the admin CompanyDetails page and is not accessible to partner users.
+
+## Features Implemented
+
+### 1. User Search Component
+- **Location**: `src/components/OwnerManagement.tsx`
+- **Search Functionality**: 
+  - Search by CPF, email, or name
+  - Debounced search with 500ms delay
+  - Minimum 3 characters required for search
+  - Real-time search results display
+
+### 2. API Integration
+- **Search Users**: `GET /v1/user/{query}` - Search users by CPF, email, or name
+- **Link Owner**: `PUT /v1/company/{externalID}/owner` - Assign user as company owner
+- **Services**: Added `userService` in `src/services/api.ts`
+
+### 3. UI Components
+- **Current Owner Display**: Shows current owner information with contact details
+- **Search Interface**: Autocomplete dropdown with user search results
+- **Confirmation Dialog**: Confirmation modal before linking a new owner
+- **Loading States**: Proper loading indicators during search and linking operations
+- **Error Handling**: Toast notifications for success/error states
+
+### 4. Access Control
+- **Admin Only**: Feature is only visible and accessible to admin users
+- **Role-based Rendering**: Uses `useAccessControl` hook to check admin permissions
+- **Tab Integration**: Added as a new "Proprietário" tab in CompanyDetails page
+
+## Technical Implementation
+
+### Components Structure
+```
+src/
+├── components/
+│   └── OwnerManagement.tsx          # Main owner management component
+├── pages/
+│   └── CompanyDetails.tsx           # Updated with owner management tab
+├── services/
+│   └── api.ts                       # Added userService with search and link methods
+├── types/
+│   └── api.ts                       # Added User types and API response types
+└── hooks/
+    └── useAccessControl.ts          # Role-based access control
+```
+
+### Key Features
+1. **Debounced Search**: 500ms delay to prevent excessive API calls
+2. **Validation**: Prevents linking the same user who is already the owner
+3. **Error Handling**: Comprehensive error handling with user-friendly messages
+4. **Loading States**: Visual feedback during API operations
+5. **Responsive Design**: Works on both desktop and mobile devices
+
+### API Endpoints Used
+- `GET /v1/user/{query}` - Search users by CPF, email, or name
+- `PUT /v1/company/{externalID}/owner` - Link user as company owner
+
+### Data Flow
+1. Admin navigates to CompanyDetails page
+2. Clicks on "Proprietário" tab (only visible to admins)
+3. Types in search field to find users
+4. Selects a user from search results
+5. Confirms the linking in the confirmation dialog
+6. System updates the company owner and refreshes the data
+
+## Usage Instructions
+
+### For Administrators
+1. Navigate to any company details page
+2. Click on the "Proprietário" tab
+3. Use the search field to find users by:
+   - CPF (with or without formatting)
+   - Email address
+   - Full or partial name
+4. Select a user from the search results
+5. Confirm the linking in the dialog
+6. The system will update the company owner
+
+### Search Tips
+- Type at least 3 characters to start searching
+- Search is case-insensitive
+- CPF can be searched with or without formatting
+- Partial name matches are supported
+
+## Error Handling
+- **User Not Found**: Shows "Nenhum usuário encontrado" message
+- **API Errors**: Displays toast notifications with error details
+- **Validation**: Prevents linking the same user who is already the owner
+- **Network Issues**: Graceful handling of connection problems
+
+## Security Considerations
+- Feature is only accessible to admin users
+- All API calls are authenticated
+- Proper validation on both frontend and backend
+- Confirmation dialog prevents accidental changes
+
+## Future Enhancements
+- Add ability to remove/unlink current owner
+- Bulk owner assignment for multiple companies
+- Owner history tracking
+- Email notifications when ownership changes
+- Advanced search filters (by role, status, etc.)
diff --git a/src/App.tsx b/src/App.tsx
index b11f998..a3ca028 100644
--- a/src/App.tsx
+++ b/src/App.tsx
@@ -9,6 +9,9 @@ import { AuthErrorHandler } from "@/components/AuthErrorHandler";
 
 // Componentes de rota
 import { PrivateRoute } from "@/components/PrivateRoute";
+import { AdminPrivateRoute } from "@/components/AdminPrivateRoute";
+import { PartnerPrivateRoute } from "@/components/PartnerPrivateRoute";
+import { DashboardRedirect } from "@/components/DashboardRedirect";
 import Login from "@/pages/Login";
 import Dashboard from "@/pages/Dashboard";
 import Companies from "@/pages/Companies";
@@ -20,6 +23,12 @@ import NotFound from "@/pages/NotFound";
 import Coupons from "@/pages/Coupons";
 import NewCoupon from "@/pages/NewCoupon";
 import CouponDetails from "@/pages/CouponDetails";
+import PartnerDashboard from "@/pages/PartnerDashboard";
+import PartnerCompanies from "@/pages/PartnerCompanies";
+import PartnerCompanyDetails from "@/pages/PartnerCompanyDetails";
+import PartnerOrders from "@/pages/PartnerOrders";
+
+import Users from "@/pages/Users";
 
 // Configuração do cliente de consulta
 const queryClient = new QueryClient({
@@ -36,8 +45,8 @@ const App = () => {
   return (
     <QueryClientProvider client={queryClient}>
       <BrowserRouter>
-        <DataProvider>
-          <AuthProvider>
+        <AuthProvider>
+          <DataProvider>
             <AuthErrorHandler>
               <TooltipProvider>
                 <Toaster />
@@ -47,20 +56,30 @@ const App = () => {
                 {/* Rota pública: Login */}
                 <Route path="/login" element={<Login />} />
 
-                {/* Rota raiz: Redireciona para dashboard se autenticado, ou para login se não */}
-                <Route path="/" element={<Navigate to="/dashboard" replace />} />
+                {/* Rota raiz: Redireciona para dashboard apropriado baseado no role */}
+                <Route path="/" element={<DashboardRedirect />} />
+                <Route path="/dashboard" element={<DashboardRedirect />} />
+
+                {/* Rotas de Admin: Protegidas por autenticação e role */}
+                <Route element={<AdminPrivateRoute />}>
+                  <Route path="/admin/dashboard" element={<Dashboard />} />
+                  <Route path="/admin/users" element={<Users />} />
+                  <Route path="/admin/companies" element={<Companies />} />
+                  <Route path="/admin/companies/new" element={<NewCompany />} />
+                  <Route path="/admin/companies/:id" element={<CompanyDetails />} />
+                  <Route path="/admin/products" element={<Products />} />
+                  <Route path="/admin/categories" element={<Categories />} />
+                  <Route path="/admin/coupons" element={<Coupons />} />
+                  <Route path="/admin/coupons/new" element={<NewCoupon />} />
+                  <Route path="/admin/coupons/:id" element={<CouponDetails />} />
+                </Route>
 
-                {/* Rotas privadas: Protegidas por autenticação */}
-                <Route element={<PrivateRoute />}>
-                  <Route path="/dashboard" element={<Dashboard />} />
-                  <Route path="/companies" element={<Companies />} />
-                  <Route path="/companies/new" element={<NewCompany />} />
-                  <Route path="/companies/:id" element={<CompanyDetails />} />
-                  <Route path="/products" element={<Products />} />
-                  <Route path="/categories" element={<Categories />} />
-                  <Route path="/coupons" element={<Coupons />} />
-                  <Route path="/coupons/new" element={<NewCoupon />} />
-                  <Route path="/coupons/:id" element={<CouponDetails />} />
+                {/* Rotas de Parceiro: Protegidas por autenticação e role */}
+                <Route element={<PartnerPrivateRoute />}>
+                  <Route path="/partner/dashboard" element={<PartnerDashboard />} />
+                  <Route path="/partner/companies" element={<PartnerCompanies />} />
+                  <Route path="/partner/companies/:id" element={<PartnerCompanyDetails />} />
+                  <Route path="/partner/orders" element={<PartnerOrders />} />
                 </Route>
 
                 {/* Rota 404: Para caminhos que não existem */}
@@ -68,8 +87,8 @@ const App = () => {
               </Routes>
             </TooltipProvider>
           </AuthErrorHandler>
-        </AuthProvider>
-      </DataProvider>
+        </DataProvider>
+      </AuthProvider>
       </BrowserRouter>
     </QueryClientProvider>
   );
diff --git a/src/components/AdminPrivateRoute.tsx b/src/components/AdminPrivateRoute.tsx
new file mode 100644
index 0000000..e1baca3
--- /dev/null
+++ b/src/components/AdminPrivateRoute.tsx
@@ -0,0 +1,27 @@
+import { Navigate, Outlet } from "react-router-dom";
+import { useAuth } from "@/contexts/AuthContext";
+import { Layout } from "@/components/Layout";
+
+export const AdminPrivateRoute = () => {
+  const { isAuthenticated, loading, userRole } = useAuth();
+
+  // Wait for auth check to finish
+  if (loading) return null;
+
+  // Se não estiver autenticado, redireciona para a página de login
+  if (!isAuthenticated) {
+    return <Navigate to="/login" replace />;
+  }
+
+  // Se for partner, redireciona para o dashboard de parceiro
+  if (userRole === 'partner') {
+    return <Navigate to="/partner/dashboard" replace />;
+  }
+
+  // Se for admin, renderiza o layout admin com o conteúdo da rota
+  return (
+    <Layout>
+      <Outlet />
+    </Layout>
+  );
+};
diff --git a/src/components/CompanyOrdersTab.tsx b/src/components/CompanyOrdersTab.tsx
index 2b93138..9d29c7d 100644
--- a/src/components/CompanyOrdersTab.tsx
+++ b/src/components/CompanyOrdersTab.tsx
@@ -108,6 +108,9 @@ const statusDisplayNames: Record<OrderStatus, string> = {
 interface CompanyOrdersTabProps {
   companyData?: {
     delivery_modes?: string[];
+    external_id?: string; // For filtering orders of a specific company
+    partnerCompanyIds?: string[]; // For filtering orders of multiple companies (partner view)
+    partnerCompanies?: Array<{ external_id: string; name: string }>; // Company names for partner view
   };
 }
 
@@ -270,10 +273,31 @@ const CompanyOrdersTab: React.FC<CompanyOrdersTabProps> = ({ companyData }) => {
     );
   }
 
-  // Filter out pending orders from the list
-  const orders = (ordersResponse?.data || []).filter(order => order.status !== "pending");
+  // Filter orders based on company context and exclude pending orders
+  const orders = (ordersResponse?.data || []).filter(order => {
+    const isNotPending = order.status !== "pending";
+
+    // If filtering for a specific company (company detail page)
+    if (companyData?.external_id) {
+      return isNotPending && order.company_external_id === companyData.external_id;
+    }
+
+    // If filtering for partner companies (partner orders page)
+    if (companyData?.partnerCompanyIds && companyData.partnerCompanyIds.length > 0) {
+      return isNotPending && companyData.partnerCompanyIds.includes(order.company_external_id);
+    }
+
+    // Default: show all non-pending orders (admin view)
+    return isNotPending;
+  });
   const totalPages = ordersResponse?.totalPages || 1;
 
+  // Helper function to get company name by external_id
+  const getCompanyName = (companyExternalId: string) => {
+    const company = companyData?.partnerCompanies?.find(c => c.external_id === companyExternalId);
+    return company?.name || companyExternalId;
+  };
+
   return (
     <>
       <Card>
@@ -287,6 +311,7 @@ const CompanyOrdersTab: React.FC<CompanyOrdersTabProps> = ({ companyData }) => {
                 <TableRow>
                   <TableHead>ID</TableHead>
                   <TableHead>Cliente</TableHead>
+                  {companyData?.partnerCompanyIds && <TableHead>Empresa</TableHead>}
                   <TableHead>Data</TableHead>
                   <TableHead>Itens</TableHead>
                   <TableHead>Total</TableHead>
@@ -302,6 +327,9 @@ const CompanyOrdersTab: React.FC<CompanyOrdersTabProps> = ({ companyData }) => {
                     <TableRow key={order.order_id}>
                       <TableCell className="font-medium">{order.order_id}</TableCell>
                       <TableCell>{order.user_name}</TableCell>
+                      {companyData?.partnerCompanyIds && (
+                        <TableCell>{getCompanyName(order.company_external_id)}</TableCell>
+                      )}
                       <TableCell>{formatDate(order.created_at)}</TableCell>
                       <TableCell>{getTotalItems(order.products)}</TableCell>
                       <TableCell>{formatCurrency(order.amount)}</TableCell>
diff --git a/src/components/DashboardRedirect.tsx b/src/components/DashboardRedirect.tsx
new file mode 100644
index 0000000..5ff3e85
--- /dev/null
+++ b/src/components/DashboardRedirect.tsx
@@ -0,0 +1,17 @@
+import { Navigate } from "react-router-dom";
+import { useAuth } from "@/contexts/AuthContext";
+
+export const DashboardRedirect = () => {
+  const { isAuthenticated, loading, dashboardUrl } = useAuth();
+
+  // Wait for auth check to finish
+  if (loading) return null;
+
+  // Se não estiver autenticado, redireciona para login
+  if (!isAuthenticated) {
+    return <Navigate to="/login" replace />;
+  }
+
+  // Redireciona para o dashboard apropriado baseado no role do usuário
+  return <Navigate to={dashboardUrl} replace />;
+};
diff --git a/src/components/Layout.tsx b/src/components/Layout.tsx
index 2946ce0..682c6d7 100644
--- a/src/components/Layout.tsx
+++ b/src/components/Layout.tsx
@@ -14,6 +14,7 @@ import {
   HelpCircle,
   Tag,
   Layers,
+  Users,
 } from "lucide-react";
 import { useIsMobile } from "@/hooks/use-mobile";
 import { cn } from "@/lib/utils";
@@ -33,11 +34,12 @@ export const Layout = ({ children }: LayoutProps) => {
 
   // Navegação do sidebar
   const navigation = [
-    { name: "Dashboard", href: "/dashboard", icon: BarChart3 },
-    { name: "Empresas", href: "/companies", icon: Building },
-    { name: "Produtos", href: "/products", icon: Package },
-    { name: "Categorias", href: "/categories", icon: Layers },
-    { name: "Cupons", href: "/coupons", icon: Tag },
+    { name: "Dashboard", href: "/admin/dashboard", icon: BarChart3 },
+    { name: "Usuários", href: "/admin/users", icon: Users },
+    { name: "Empresas", href: "/admin/companies", icon: Building },
+    { name: "Produtos", href: "/admin/products", icon: Package },
+    { name: "Categorias", href: "/admin/categories", icon: Layers },
+    { name: "Cupons", href: "/admin/coupons", icon: Tag },
   ];
 
   // Função para verificar se o link está ativo
diff --git a/src/components/OwnerManagement.tsx b/src/components/OwnerManagement.tsx
new file mode 100644
index 0000000..95d0e19
--- /dev/null
+++ b/src/components/OwnerManagement.tsx
@@ -0,0 +1,336 @@
+import React, { useState, useCallback, useEffect } from "react";
+import { useMutation, useQueryClient } from "@tanstack/react-query";
+import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { Button } from "@/components/ui/button";
+import { Input } from "@/components/ui/input";
+import { Badge } from "@/components/ui/badge";
+import {
+  Dialog,
+  DialogContent,
+  DialogDescription,
+  DialogHeader,
+  DialogTitle,
+  DialogFooter,
+} from "@/components/ui/dialog";
+
+import { userService } from "@/services/api";
+import { User, SearchUsersSuccessResponse } from "@/types/api";
+import { toast } from "sonner";
+import { Users, Search, Loader2, Check, UserPlus, UserX } from "lucide-react";
+import { cn } from "@/lib/utils";
+
+interface OwnerManagementProps {
+  companyExternalId: string;
+  currentOwner?: User | null;
+}
+
+const OwnerManagement: React.FC<OwnerManagementProps> = ({
+  companyExternalId,
+  currentOwner
+}) => {
+  const queryClient = useQueryClient();
+  const [searchQuery, setSearchQuery] = useState("");
+  const [searchResults, setSearchResults] = useState<User[]>([]);
+  const [isSearching, setIsSearching] = useState(false);
+  const [selectedUser, setSelectedUser] = useState<User | null>(null);
+  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
+  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);
+
+  // Current owner is passed as prop from parent component
+
+  // Mutation para vincular usuário à empresa
+  const linkUserMutation = useMutation({
+    mutationFn: (userExternalId: string) =>
+      userService.linkUserToCompany(companyExternalId, userExternalId),
+    onSuccess: () => {
+      toast.success("Usuário vinculado como proprietário com sucesso!");
+      // Invalidate multiple related queries to ensure data refresh
+      queryClient.invalidateQueries({ queryKey: ["company", companyExternalId] });
+      queryClient.invalidateQueries({ queryKey: ["companies"] });
+      // Force refetch the company data to get updated owner information
+      queryClient.refetchQueries({ queryKey: ["company", companyExternalId] });
+      setConfirmDialogOpen(false);
+      setSelectedUser(null);
+      setSearchQuery("");
+      setSearchResults([]);
+    },
+    onError: (error: any) => {
+      const errorMsg = error.response?.data?.message || "Erro ao vincular usuário";
+      toast.error(errorMsg);
+    },
+  });
+
+  // Função para buscar usuários
+  const searchUsers = useCallback(async (query: string) => {
+    if (!query.trim() || query.length < 3) {
+      setSearchResults([]);
+      return;
+    }
+
+    setIsSearching(true);
+    try {
+      const response = await userService.searchUsers(query);
+      // Handle paginated response structure
+      const responseData = response.data;
+
+      // Check if it's a paginated response or direct array
+      if (responseData.data && Array.isArray(responseData.data)) {
+        setSearchResults(responseData.data);
+      } else if (Array.isArray(responseData)) {
+        setSearchResults(responseData);
+      } else {
+        setSearchResults([]);
+      }
+    } catch (error: any) {
+      console.error("Erro ao buscar usuários:", error);
+      if (error.response?.status === 404) {
+        setSearchResults([]);
+      } else {
+        toast.error("Erro ao buscar usuários");
+      }
+    } finally {
+      setIsSearching(false);
+    }
+  }, []);
+
+  // Effect para busca com debounce
+  useEffect(() => {
+    // Clear previous timer
+    if (debounceTimer) {
+      clearTimeout(debounceTimer);
+    }
+
+    // Set new timer
+    const timer = setTimeout(() => {
+      searchUsers(searchQuery);
+    }, 500);
+
+    setDebounceTimer(timer);
+
+    // Cleanup function
+    return () => {
+      clearTimeout(timer);
+    };
+  }, [searchQuery]); // Only depend on searchQuery
+
+  // Cleanup timer on unmount
+  useEffect(() => {
+    return () => {
+      if (debounceTimer) {
+        clearTimeout(debounceTimer);
+      }
+    };
+  }, []);
+
+  // Função para formatar CPF
+  const formatCPF = (cpf: string) => {
+    if (!cpf) return '';
+    const cleanCPF = cpf.replace(/\D/g, '');
+    if (cleanCPF.length !== 11) return cpf;
+    return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
+  };
+
+  // Função para selecionar usuário
+  const handleSelectUser = (user: User) => {
+    // Verificar se o usuário já é o proprietário atual
+    if (currentOwner && user.external_id === currentOwner.external_id) {
+      toast.info("Este usuário já é o proprietário atual da empresa");
+      return;
+    }
+
+    setSelectedUser(user);
+    setConfirmDialogOpen(true);
+  };
+
+  // Função para confirmar vinculação
+  const handleConfirmLink = () => {
+    if (selectedUser) {
+      linkUserMutation.mutate(selectedUser.external_id);
+    }
+  };
+
+  return (
+    <Card>
+      <CardHeader>
+        <CardTitle className="flex items-center">
+          <Users className="mr-2" size={20} />
+          Gerenciamento de Proprietário
+        </CardTitle>
+      </CardHeader>
+      <CardContent className="space-y-4">
+        {/* Current Owner Display */}
+        {currentOwner ? (
+          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
+            <div className="flex items-start justify-between">
+              <div className="flex-1">
+                <h4 className="font-medium text-green-900 mb-2">Proprietário Atual</h4>
+                <div className="space-y-1">
+                  <p className="text-sm text-green-700 font-medium">{currentOwner.name}</p>
+                  <p className="text-xs text-green-600">{currentOwner.email}</p>
+                  {(currentOwner.cpf || currentOwner.document) && (
+                    <p className="text-xs text-green-600">CPF: {formatCPF(currentOwner.cpf || currentOwner.document || '')}</p>
+                  )}
+                  {currentOwner.phone_numbers && currentOwner.phone_numbers.length > 0 && (
+                    <p className="text-xs text-green-600">Tel: {currentOwner.phone_numbers[0]}</p>
+                  )}
+                </div>
+              </div>
+              <div className="flex flex-col items-end space-y-2">
+                <Badge variant="default" className="bg-green-600">
+                  <Check size={12} className="mr-1" />
+                  Vinculado
+                </Badge>
+              </div>
+            </div>
+          </div>
+        ) : (
+          <div className="p-4 bg-gray-50 border border-gray-200 rounded-lg">
+            <div className="flex items-center justify-center text-gray-500">
+              <UserX size={24} className="mr-2" />
+              <span>Nenhum proprietário vinculado</span>
+            </div>
+          </div>
+        )}
+
+        {/* User Search */}
+        <div className="space-y-2">
+          <label className="text-sm font-medium">
+            Buscar e Vincular Novo Proprietário
+          </label>
+
+          {/* Search Input */}
+          <div className="relative">
+            <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
+            <Input
+              placeholder="Digite CPF, email ou nome para buscar..."
+              value={searchQuery}
+              onChange={(e) => setSearchQuery(e.target.value)}
+              className="pl-10 pr-10"
+            />
+            {isSearching && (
+              <Loader2 size={16} className="absolute right-3 top-1/2 transform -translate-y-1/2 animate-spin text-gray-400" />
+            )}
+          </div>
+
+          {/* Search Results */}
+          {searchQuery.length >= 3 && (
+            <div className="border rounded-md bg-white shadow-sm max-h-60 overflow-y-auto">
+              {isSearching ? (
+                <div className="flex items-center justify-center py-8">
+                  <Loader2 size={20} className="animate-spin mr-2" />
+                  <span className="text-sm text-gray-500">Buscando usuários...</span>
+                </div>
+              ) : searchResults.length > 0 ? (
+                <div className="p-2">
+                  {searchResults.map((user) => (
+                    <div
+                      key={user.external_id}
+                      onClick={() => handleSelectUser(user)}
+                      className="flex items-center justify-between p-3 rounded-md hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
+                    >
+                      <div className="flex-1">
+                        <p className="font-medium text-sm">{user.name}</p>
+                        <p className="text-xs text-gray-500">{user.email}</p>
+                        {(user.cpf || user.document) && (
+                          <p className="text-xs text-gray-400">
+                            CPF: {formatCPF(user.cpf || user.document || '')}
+                          </p>
+                        )}
+                        {user.phone_numbers && user.phone_numbers.length > 0 && (
+                          <p className="text-xs text-gray-400">
+                            Tel: {user.phone_numbers[0]}
+                          </p>
+                        )}
+                      </div>
+                      <div className="flex items-center ml-3">
+                        {currentOwner && user.external_id === currentOwner.external_id ? (
+                          <Badge variant="default" className="text-xs">
+                            Atual
+                          </Badge>
+                        ) : (
+                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
+                            <UserPlus size={14} className="text-gray-400" />
+                          </Button>
+                        )}
+                      </div>
+                    </div>
+                  ))}
+                </div>
+              ) : (
+                <div className="flex items-center justify-center py-8">
+                  <div className="text-center">
+                    <Search size={24} className="mx-auto text-gray-400 mb-2" />
+                    <p className="text-sm text-gray-500">Nenhum usuário encontrado</p>
+                    <p className="text-xs text-gray-400">Tente buscar por CPF, email ou nome</p>
+                  </div>
+                </div>
+              )}
+            </div>
+          )}
+
+          {searchQuery.length > 0 && searchQuery.length < 3 && (
+            <p className="text-xs text-gray-500">
+              Digite pelo menos 3 caracteres para buscar usuários
+            </p>
+          )}
+        </div>
+
+        {/* Confirmation Dialog */}
+        <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
+          <DialogContent>
+            <DialogHeader>
+              <DialogTitle>Confirmar Vinculação de Proprietário</DialogTitle>
+              <DialogDescription>
+                Tem certeza que deseja vincular este usuário como proprietário da empresa?
+                {currentOwner && " Isso substituirá o proprietário atual."}
+              </DialogDescription>
+            </DialogHeader>
+            
+            {selectedUser && (
+              <div className="py-4">
+                <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
+                  <h4 className="font-medium text-blue-900">Usuário Selecionado</h4>
+                  <p className="text-sm text-blue-700">{selectedUser.name}</p>
+                  <p className="text-xs text-blue-600">{selectedUser.email}</p>
+                  {(selectedUser.cpf || selectedUser.document) && (
+                    <p className="text-xs text-blue-600">
+                      CPF: {formatCPF(selectedUser.cpf || selectedUser.document || '')}
+                    </p>
+                  )}
+                </div>
+              </div>
+            )}
+
+            <DialogFooter>
+              <Button
+                variant="outline"
+                onClick={() => setConfirmDialogOpen(false)}
+                disabled={linkUserMutation.isPending}
+              >
+                Cancelar
+              </Button>
+              <Button
+                onClick={handleConfirmLink}
+                disabled={linkUserMutation.isPending}
+              >
+                {linkUserMutation.isPending ? (
+                  <>
+                    <Loader2 size={16} className="mr-2 animate-spin" />
+                    Vinculando...
+                  </>
+                ) : (
+                  <>
+                    <UserPlus size={16} className="mr-2" />
+                    Confirmar Vinculação
+                  </>
+                )}
+              </Button>
+            </DialogFooter>
+          </DialogContent>
+        </Dialog>
+      </CardContent>
+    </Card>
+  );
+};
+
+export default OwnerManagement;
diff --git a/src/components/PartnerLayout.tsx b/src/components/PartnerLayout.tsx
new file mode 100644
index 0000000..5371df8
--- /dev/null
+++ b/src/components/PartnerLayout.tsx
@@ -0,0 +1,224 @@
+import React, { useState } from "react";
+import { Link, useLocation, useNavigate } from "react-router-dom";
+import { useAuth } from "@/contexts/AuthContext";
+import { Button } from "@/components/ui/button";
+import {
+  LogOut,
+  BarChart3,
+  Building,
+  Menu,
+  X,
+  PanelLeft,
+  HelpCircle,
+  ShoppingBag,
+} from "lucide-react";
+import { useIsMobile } from "@/hooks/use-mobile";
+import { cn } from "@/lib/utils";
+import SupportModal from "./SupportModal";
+
+interface PartnerLayoutProps {
+  children: React.ReactNode;
+}
+
+export const PartnerLayout = ({ children }: PartnerLayoutProps) => {
+  const { logout, user } = useAuth();
+  const location = useLocation();
+  const navigate = useNavigate();
+  const isMobile = useIsMobile();
+  const [sidebarOpen, setSidebarOpen] = useState(!isMobile);
+  const [supportModalOpen, setSupportModalOpen] = useState(false);
+
+  // Partner navigation - restricted menu
+  const navigation = [
+    { name: "Dashboard", href: "/partner/dashboard", icon: BarChart3 },
+    { name: "Minhas Empresas", href: "/partner/companies", icon: Building },
+    { name: "Pedidos", href: "/partner/orders", icon: ShoppingBag },
+  ];
+
+  // Função para verificar se o link está ativo
+  const isActive = (path: string) => {
+    return location.pathname === path;
+  };
+
+  const handleLogout = async () => {
+    await logout();
+  };
+
+  return (
+    <div className="flex h-screen bg-gray-50">
+      {/* Sidebar */}
+      <div
+        className={cn(
+          "fixed inset-y-0 left-0 z-30 flex flex-col bg-white border-r border-gray-200 transition-all duration-300",
+          isMobile
+            ? sidebarOpen
+              ? "w-64 translate-x-0"
+              : "w-64 -translate-x-full"
+            : sidebarOpen
+            ? "w-64"
+            : "w-16"
+        )}
+      >
+        {/* Header do Sidebar */}
+        <div className="flex items-center justify-between p-4 border-b border-gray-200">
+          {(sidebarOpen || isMobile) && (
+            <div className="flex items-center">
+              <h1 className="text-xl font-bold text-gray-900">
+                Painel Parceiro
+              </h1>
+            </div>
+          )}
+          
+          {/* Botão para colapsar/expandir (apenas em desktop) */}
+          {!isMobile && (
+            <Button
+              variant="ghost"
+              size="icon"
+              onClick={() => setSidebarOpen(!sidebarOpen)}
+              className="h-8 w-8"
+            >
+              {sidebarOpen ? <X size={16} /> : <PanelLeft size={16} />}
+            </Button>
+          )}
+          
+          {/* Botão para fechar em mobile */}
+          {isMobile && (
+            <Button
+              variant="ghost"
+              size="icon"
+              onClick={() => setSidebarOpen(false)}
+              className="h-8 w-8"
+            >
+              <X size={16} />
+            </Button>
+          )}
+        </div>
+
+        {/* User Info */}
+        {(sidebarOpen || isMobile) && user && (
+          <div className="px-4 py-3 border-b border-gray-200">
+            <p className="text-sm font-medium text-gray-900 truncate">
+              {user.name || user.email}
+            </p>
+            <p className="text-xs text-gray-500 truncate">Parceiro</p>
+          </div>
+        )}
+
+        {/* Sidebar Navigation */}
+        <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
+          {navigation.map((item) => (
+            <Link
+              key={item.name}
+              to={item.href}
+              className={cn(
+                "flex items-center px-4 py-3 rounded-md text-sm font-medium transition-colors",
+                isActive(item.href)
+                  ? "bg-primary text-primary-foreground"
+                  : "text-gray-700 hover:bg-gray-100",
+                !sidebarOpen && !isMobile && "justify-center px-2"
+              )}
+              onClick={() => isMobile && setSidebarOpen(false)}
+              title={!sidebarOpen && !isMobile ? item.name : undefined}
+            >
+              <item.icon
+                size={18}
+                className={cn(
+                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
+                )}
+              />
+              {(sidebarOpen || isMobile) && item.name}
+            </Link>
+          ))}
+        </nav>
+
+        {/* Footer do Sidebar */}
+        <div className="p-4 border-t border-gray-200">
+          <div className="space-y-2">
+            {/* Botão de Suporte */}
+            <Button
+              variant="ghost"
+              onClick={() => setSupportModalOpen(true)}
+              className={cn(
+                "w-full justify-start",
+                !sidebarOpen && !isMobile && "justify-center px-2"
+              )}
+              title={!sidebarOpen && !isMobile ? "Suporte" : undefined}
+            >
+              <HelpCircle
+                size={18}
+                className={cn(
+                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
+                )}
+              />
+              {(sidebarOpen || isMobile) && "Suporte"}
+            </Button>
+
+            {/* Botão de Logout */}
+            <Button
+              variant="ghost"
+              onClick={handleLogout}
+              className={cn(
+                "w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50",
+                !sidebarOpen && !isMobile && "justify-center px-2"
+              )}
+              title={!sidebarOpen && !isMobile ? "Sair" : undefined}
+            >
+              <LogOut
+                size={18}
+                className={cn(
+                  sidebarOpen || isMobile ? "mr-3" : "mx-auto"
+                )}
+              />
+              {(sidebarOpen || isMobile) && "Sair"}
+            </Button>
+          </div>
+        </div>
+      </div>
+
+      {/* Main Content */}
+      <div
+        className={cn(
+          "flex-1 flex flex-col transition-all duration-300",
+          isMobile ? "w-full" : sidebarOpen ? "ml-64" : "ml-16"
+        )}
+      >
+        <main
+          className={cn(
+            "flex-1 transition-all duration-300 overflow-x-hidden",
+            isMobile ? "w-full p-4" : sidebarOpen ? "p-6" : "p-6 w-full"
+          )}
+        >
+          {/* Botão para expandir o sidebar (apenas em desktop quando colapsado) */}
+          {!isMobile && !sidebarOpen && (
+            <Button
+              variant="outline"
+              size="icon"
+              onClick={() => setSidebarOpen(true)}
+              className="mb-4"
+            >
+              <Menu size={18} />
+            </Button>
+          )}
+          
+          {/* Overlay para fechar o sidebar em dispositivos móveis */}
+          {isMobile && sidebarOpen && (
+            <div 
+              className="fixed inset-0 bg-black/50 z-20"
+              onClick={() => setSidebarOpen(false)}
+            />
+          )}
+          
+          <div className="w-full animate-fade-in">
+            {children}
+          </div>
+        </main>
+      </div>
+      
+      {/* Support Modal */}
+      <SupportModal 
+        open={supportModalOpen}
+        onOpenChange={setSupportModalOpen}
+      />
+    </div>
+  );
+};
diff --git a/src/components/PartnerPrivateRoute.tsx b/src/components/PartnerPrivateRoute.tsx
new file mode 100644
index 0000000..aaefe73
--- /dev/null
+++ b/src/components/PartnerPrivateRoute.tsx
@@ -0,0 +1,27 @@
+import { Navigate, Outlet } from "react-router-dom";
+import { useAuth } from "@/contexts/AuthContext";
+import { PartnerLayout } from "@/components/PartnerLayout";
+
+export const PartnerPrivateRoute = () => {
+  const { isAuthenticated, loading, userRole } = useAuth();
+
+  // Wait for auth check to finish
+  if (loading) return null;
+
+  // Se não estiver autenticado, redireciona para a página de login
+  if (!isAuthenticated) {
+    return <Navigate to="/login" replace />;
+  }
+
+  // Se for admin, redireciona para o dashboard admin
+  if (userRole === 'admin') {
+    return <Navigate to="/admin/dashboard" replace />;
+  }
+
+  // Se for partner, renderiza o layout de parceiro com o conteúdo da rota
+  return (
+    <PartnerLayout>
+      <Outlet />
+    </PartnerLayout>
+  );
+};
diff --git a/src/contexts/AuthContext.tsx b/src/contexts/AuthContext.tsx
index bc4d4a8..c86a62d 100644
--- a/src/contexts/AuthContext.tsx
+++ b/src/contexts/AuthContext.tsx
@@ -1,8 +1,8 @@
 import React, { createContext, useContext, useState, useEffect } from "react";
 import { useNavigate } from "react-router-dom";
-import { authService, api } from "@/services/api";
-import { LoginSuccessResponse } from "@/types/api";
-import { useData } from "@/contexts/DataContext";
+import { authService, api, companyService } from "@/services/api";
+import { LoginSuccessResponse, GetMyCompaniesSuccessResponse } from "@/types/api";
+
 import {
   saveTokenWithTimestamp,
   hasValidTokens,
@@ -14,6 +14,9 @@ import {
 interface AuthContextType {
   isAuthenticated: boolean;
   user: any;
+  userRole: 'admin' | 'partner' | null;
+  userCompanies: string[];
+  dashboardUrl: string;
   login: (data: LoginSuccessResponse) => Promise<void>;
   logout: () => Promise<void>;
   loading: boolean;
@@ -25,18 +28,29 @@ const AuthContext = createContext<AuthContextType | undefined>(undefined);
 export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
   const [isAuthenticated, setIsAuthenticated] = useState(false);
   const [user, setUser] = useState<any>(null);
+  const [userRole, setUserRole] = useState<'admin' | 'partner' | null>(null);
+  const [userCompanies, setUserCompanies] = useState<string[]>([]);
+  const [dashboardUrl, setDashboardUrl] = useState('/admin/dashboard');
   const [loading, setLoading] = useState(true);
   const navigate = useNavigate();
-  const { refreshProducts, refreshCategories } = useData();
 
-  // Função para buscar dados do usuário autenticado
+  // Função para buscar dados do usuário autenticado e suas empresas
   const fetchUser = async () => {
     try {
       const { data } = await api.get("/v1/user/me");
       setUser(data.data);
       setIsAuthenticated(true);
+
+      // Buscar empresas do usuário para determinar role e acesso
+      await fetchUserCompanies();
+
       return true;
     } catch (error: any) {
+      // Se for erro de acesso não autorizado, propagar o erro
+      if (error.message === "ACESSO NÃO AUTORIZADO!") {
+        throw error;
+      }
+
       // Só desconecta se for erro AUTH_REQUIRED (refresh token expirado)
       if (error.message === "AUTH_REQUIRED") {
         forceLogout();
@@ -45,11 +59,42 @@ export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children
     }
   };
 
+  // Função para buscar empresas do usuário e determinar role
+  const fetchUserCompanies = async () => {
+    try {
+      const response = await companyService.getMyCompanies();
+      const myCompaniesData = response.data as GetMyCompaniesSuccessResponse;
+
+      setUserCompanies(myCompaniesData.data.company_external_ids);
+      setDashboardUrl(myCompaniesData.data.dashboard_url);
+
+      // Determinar role baseado na URL do dashboard
+      const role = myCompaniesData.data.dashboard_url.includes('/admin/') ? 'admin' : 'partner';
+      setUserRole(role);
+
+    } catch (error: any) {
+      console.error("Erro ao buscar empresas do usuário:", error);
+
+      // Se retornar 403, significa acesso não autorizado - falhar o login
+      if (error.response?.status === 403) {
+        throw new Error("ACESSO NÃO AUTORIZADO!");
+      }
+
+      // Para outros erros, assumir role admin como fallback
+      setUserRole('admin');
+      setDashboardUrl('/admin/dashboard');
+      setUserCompanies([]);
+    }
+  };
+
   // Função para forçar logout (apenas quando refresh token expira)
   const forceLogout = () => {
     console.log("🚪 Forçando logout - refresh token expirado");
     setIsAuthenticated(false);
     setUser(null);
+    setUserRole(null);
+    setUserCompanies([]);
+    setDashboardUrl('/admin/dashboard');
     clearAllTokens();
     delete api.defaults.headers.common["Authorization"];
     navigate('/login');
@@ -83,12 +128,26 @@ export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children
     saveTokenWithTimestamp(refresh_token, 'refresh');
 
     api.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;
-    await fetchUser();
-    await Promise.all([
-      refreshProducts(),
-      refreshCategories()
-    ]);
-    navigate('/');
+
+    try {
+      await fetchUser();
+
+      // Redirecionar para o dashboard apropriado baseado no role
+      navigate(dashboardUrl || '/admin/dashboard');
+    } catch (error: any) {
+      // Se houver erro de acesso não autorizado, limpar tokens e propagar erro
+      if (error.message === "ACESSO NÃO AUTORIZADO!") {
+        setIsAuthenticated(false);
+        setUser(null);
+        setUserRole(null);
+        setUserCompanies([]);
+        setDashboardUrl('/admin/dashboard');
+        clearAllTokens();
+        delete api.defaults.headers.common["Authorization"];
+        throw error;
+      }
+      throw error;
+    }
   };
 
   const logout = async () => {
@@ -101,6 +160,9 @@ export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children
     } finally {
       setIsAuthenticated(false);
       setUser(null);
+      setUserRole(null);
+      setUserCompanies([]);
+      setDashboardUrl('/admin/dashboard');
       clearAllTokens();
       delete api.defaults.headers.common["Authorization"];
       navigate('/login');
@@ -108,7 +170,17 @@ export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children
   };
 
   return (
-    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, loading, forceLogout }}>
+    <AuthContext.Provider value={{
+      isAuthenticated,
+      user,
+      userRole,
+      userCompanies,
+      dashboardUrl,
+      login,
+      logout,
+      loading,
+      forceLogout
+    }}>
       {children}
     </AuthContext.Provider>
   );
diff --git a/src/contexts/DataContext.tsx b/src/contexts/DataContext.tsx
index 4949185..453db04 100644
--- a/src/contexts/DataContext.tsx
+++ b/src/contexts/DataContext.tsx
@@ -1,7 +1,8 @@
 import React, { createContext, useContext, useState, useEffect } from 'react';
 import { productService, categoryService, companyService } from '@/services/api';
-import { GetActiveProductsResponse } from '@/types/api';
+import { GetActiveProductsResponse, GetMyCompaniesResponse } from '@/types/api';
 import { useToast } from '@/components/ui/use-toast';
+import { useAuth } from '@/contexts/AuthContext';
 
 interface Category {
   external_id: string;
@@ -20,9 +21,11 @@ interface DataContextType {
   categories: Category[];
   companies: Company[];
   isLoading: boolean;
-  refreshProducts: () => Promise<void>;
-  refreshCategories: () => Promise<void>;
-  refreshCompanies: () => Promise<void>;
+  isAdmin: boolean;
+  isCheckingAdminStatus: boolean;
+  refreshProducts: (forceRefresh?: boolean) => Promise<void>;
+  refreshCategories: (forceRefresh?: boolean) => Promise<void>;
+  refreshCompanies: (forceRefresh?: boolean) => Promise<void>;
 }
 
 const DataContext = createContext<DataContextType | undefined>(undefined);
@@ -31,22 +34,67 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
   const [products, setProducts] = useState<GetActiveProductsResponse[]>([]);
   const [categories, setCategories] = useState<Category[]>([]);
   const [companies, setCompanies] = useState<Company[]>([]);
-  const [isLoading, setIsLoading] = useState(true);
+  const [isLoading, setIsLoading] = useState(false);
+  const [isAdmin, setIsAdmin] = useState(false);
+  const [isCheckingAdminStatus, setIsCheckingAdminStatus] = useState(true);
   const { toast } = useToast();
+  const { user } = useAuth();
+
+  // Check if user is admin by calling my-companies endpoint and load admin data
+  useEffect(() => {
+    const checkAdminStatusAndLoadData = async () => {
+      if (user?.external_id) {
+        setIsCheckingAdminStatus(true);
+        try {
+          const response = await companyService.getMyCompanies();
+          const data = response.data as GetMyCompaniesResponse;
+          const isUserAdmin = data.dashboard_url === "/admin/dashboard";
+          setIsAdmin(isUserAdmin);
+
+          // If user is admin, automatically fetch all admin data
+          if (isUserAdmin) {
+            console.log('🔧 Admin user detected, loading admin data...');
+            await Promise.all([
+              fetchAllProducts(),
+              fetchAllCategories(),
+              fetchAllCompanies()
+            ]);
+            console.log('✅ Admin data loaded successfully');
+          }
+        } catch (error) {
+          console.error('Error checking admin status:', error);
+          setIsAdmin(false);
+        } finally {
+          setIsCheckingAdminStatus(false);
+        }
+      } else {
+        setIsCheckingAdminStatus(false);
+      }
+    };
+
+    checkAdminStatusAndLoadData();
+  }, [user?.external_id]);
 
   // Load from localStorage on mount
   useEffect(() => {
     const storedProducts = localStorage.getItem('izy_products');
     const storedCategories = localStorage.getItem('izy_categories');
     const storedCompanies = localStorage.getItem('izy_companies');
+
     if (storedProducts) {
-      setProducts(JSON.parse(storedProducts));
+      const parsedProducts = JSON.parse(storedProducts);
+      setProducts(parsedProducts);
+      console.log('📦 Loaded products from localStorage:', parsedProducts.length);
     }
     if (storedCategories) {
-      setCategories(JSON.parse(storedCategories));
+      const parsedCategories = JSON.parse(storedCategories);
+      setCategories(parsedCategories);
+      console.log('📂 Loaded categories from localStorage:', parsedCategories.length);
     }
     if (storedCompanies) {
-      setCompanies(JSON.parse(storedCompanies));
+      const parsedCompanies = JSON.parse(storedCompanies);
+      setCompanies(parsedCompanies);
+      console.log('🏢 Loaded companies from localStorage:', parsedCompanies.length);
     }
     setIsLoading(false);
   }, []);
@@ -56,6 +104,7 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
       const response = await productService.getProducts(1, 10000);
       setProducts(response.data.data);
       localStorage.setItem('izy_products', JSON.stringify(response.data.data));
+      console.log('📦 Products loaded:', response.data.data.length);
     } catch (error) {
       console.error('Error fetching products:', error);
       toast({
@@ -71,6 +120,7 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
       const response = await categoryService.getCategories(1, 10000);
       setCategories(response.data.data);
       localStorage.setItem('izy_categories', JSON.stringify(response.data.data));
+      console.log('📂 Categories loaded:', response.data.data.length);
     } catch (error) {
       console.error('Error fetching categories:', error);
       toast({
@@ -86,6 +136,7 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
       const response = await companyService.getCompanies();
       setCompanies(response.data.data);
       localStorage.setItem('izy_companies', JSON.stringify(response.data.data));
+      console.log('🏢 Companies loaded:', response.data.data.length);
     } catch (error) {
       console.error('Error fetching companies:', error);
       toast({
@@ -96,19 +147,34 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
     }
   };
 
-  const refreshProducts = async () => {
+  const refreshProducts = async (forceRefresh = false) => {
+    // Allow refresh if explicitly forced (from admin pages) or if user is admin
+    if (!forceRefresh && !isAdmin) {
+      console.warn('Products refresh is admin-only. User does not have admin access.');
+      return;
+    }
     setIsLoading(true);
     await fetchAllProducts();
     setIsLoading(false);
   };
 
-  const refreshCategories = async () => {
+  const refreshCategories = async (forceRefresh = false) => {
+    // Allow refresh if explicitly forced (from admin pages) or if user is admin
+    if (!forceRefresh && !isAdmin) {
+      console.warn('Categories refresh is admin-only. User does not have admin access.');
+      return;
+    }
     setIsLoading(true);
     await fetchAllCategories();
     setIsLoading(false);
   };
 
-  const refreshCompanies = async () => {
+  const refreshCompanies = async (forceRefresh = false) => {
+    // Allow refresh if explicitly forced (from admin pages) or if user is admin
+    if (!forceRefresh && !isAdmin) {
+      console.warn('Companies refresh is admin-only. User does not have admin access.');
+      return;
+    }
     setIsLoading(true);
     await fetchAllCompanies();
     setIsLoading(false);
@@ -121,6 +187,8 @@ export const DataProvider: React.FC<{ children: React.ReactNode }> = ({ children
         categories,
         companies,
         isLoading,
+        isAdmin,
+        isCheckingAdminStatus,
         refreshProducts,
         refreshCategories,
         refreshCompanies,
diff --git a/src/hooks/useAccessControl.ts b/src/hooks/useAccessControl.ts
new file mode 100644
index 0000000..d985d5c
--- /dev/null
+++ b/src/hooks/useAccessControl.ts
@@ -0,0 +1,153 @@
+import { useAuth } from "@/contexts/AuthContext";
+
+/**
+ * Hook for managing access control based on user role and company associations
+ */
+export const useAccessControl = () => {
+  const { userRole, userCompanies } = useAuth();
+
+  /**
+   * Check if user has access to a specific company
+   */
+  const hasCompanyAccess = (companyExternalId: string): boolean => {
+    // Admin has access to all companies
+    if (userRole === 'admin') {
+      return true;
+    }
+    
+    // Partner only has access to their assigned companies
+    if (userRole === 'partner') {
+      return userCompanies.includes(companyExternalId);
+    }
+    
+    return false;
+  };
+
+  /**
+   * Check if user is admin
+   */
+  const isAdmin = (): boolean => {
+    return userRole === 'admin';
+  };
+
+  /**
+   * Check if user is partner
+   */
+  const isPartner = (): boolean => {
+    return userRole === 'partner';
+  };
+
+  /**
+   * Check if user can edit company profile data
+   * Only admins can edit company profiles
+   */
+  const canEditCompanyProfile = (companyExternalId?: string): boolean => {
+    return userRole === 'admin';
+  };
+
+  /**
+   * Check if user can manage company products
+   * Both admins and partners (with access) can manage products
+   */
+  const canManageProducts = (companyExternalId?: string): boolean => {
+    if (userRole === 'admin') {
+      return true;
+    }
+    
+    if (userRole === 'partner' && companyExternalId) {
+      return hasCompanyAccess(companyExternalId);
+    }
+    
+    return false;
+  };
+
+  /**
+   * Check if user can manage orders
+   * Both admins and partners (with access) can manage orders
+   */
+  const canManageOrders = (companyExternalId?: string): boolean => {
+    if (userRole === 'admin') {
+      return true;
+    }
+    
+    if (userRole === 'partner' && companyExternalId) {
+      return hasCompanyAccess(companyExternalId);
+    }
+    
+    return false;
+  };
+
+  /**
+   * Check if user can view billing/revenue data
+   * Both admins and partners (with access) can view billing
+   */
+  const canViewBilling = (companyExternalId?: string): boolean => {
+    if (userRole === 'admin') {
+      return true;
+    }
+    
+    if (userRole === 'partner' && companyExternalId) {
+      return hasCompanyAccess(companyExternalId);
+    }
+    
+    return false;
+  };
+
+  /**
+   * Check if user can create new companies
+   * Only admins can create companies
+   */
+  const canCreateCompanies = (): boolean => {
+    return userRole === 'admin';
+  };
+
+  /**
+   * Check if user can manage global products/categories
+   * Only admins can manage global products and categories
+   */
+  const canManageGlobalProducts = (): boolean => {
+    return userRole === 'admin';
+  };
+
+  /**
+   * Check if user can manage coupons
+   * Only admins can manage coupons
+   */
+  const canManageCoupons = (): boolean => {
+    return userRole === 'admin';
+  };
+
+  /**
+   * Get filtered companies based on user access
+   * Returns all companies for admin, only assigned companies for partner
+   */
+  const getAccessibleCompanies = (allCompanies: any[]): any[] => {
+    if (userRole === 'admin') {
+      return allCompanies;
+    }
+    
+    if (userRole === 'partner') {
+      return allCompanies.filter(company => 
+        userCompanies.includes(company.external_id)
+      );
+    }
+    
+    return [];
+  };
+
+  return {
+    userRole,
+    userCompanies,
+    hasCompanyAccess,
+    isAdmin,
+    isPartner,
+    canEditCompanyProfile,
+    canManageProducts,
+    canManageOrders,
+    canViewBilling,
+    canCreateCompanies,
+    canManageGlobalProducts,
+    canManageCoupons,
+    getAccessibleCompanies,
+  };
+};
diff --git a/src/hooks/usePartnerData.ts b/src/hooks/usePartnerData.ts
new file mode 100644
index 0000000..b6e8bfb
--- /dev/null
+++ b/src/hooks/usePartnerData.ts
@@ -0,0 +1,236 @@
+import { useState, useEffect, useCallback } from 'react';
+import { useQuery, useQueryClient } from '@tanstack/react-query';
+import { companyService } from '@/services/api';
+import { GetMyCompaniesResponse, GetActiveCompanySuccessResponse } from '@/types/api';
+import { useAuth } from '@/contexts/AuthContext';
+
+interface PartnerCompanyData {
+  external_id: string;
+  cnpj?: string;
+  name: string;
+  email?: string;
+  bio?: string;
+  picture?: string;
+  is_active: boolean;
+  products?: Array<{
+    external_id: string;
+    name: string;
+    brand: string;
+    image?: string;
+    price: number;
+    discount: number;
+    stock: number;
+  }>;
+  address?: {
+    street: string;
+    number: string;
+    city: string;
+    state: string;
+    zip_code: string;
+  };
+  phone_numbers?: string[];
+  delivery_modes?: string[];
+  shipping_fee?: number;
+}
+
+export const usePartnerData = () => {
+  const { user, isAuthenticated, loading: authLoading } = useAuth();
+  const queryClient = useQueryClient();
+  const [cachedCompanies, setCachedCompanies] = useState<PartnerCompanyData[]>([]);
+  const [isLoadingFromCache, setIsLoadingFromCache] = useState(true);
+
+  // Cache key based on partner external ID
+  const cacheKey = user?.external_id ? `${user.external_id}-companies` : null;
+
+  // Debug logging function
+  const logDebug = useCallback((message: string, data?: any) => {
+    console.log(`🔍 [usePartnerData] ${message}`, data || '');
+  }, []);
+
+  // Load cached data on mount - only when user is available and stable
+  useEffect(() => {
+    if (!authLoading && isAuthenticated && cacheKey) {
+      logDebug('Loading cached data', { cacheKey });
+      const cached = localStorage.getItem(cacheKey);
+      if (cached) {
+        try {
+          const parsedData = JSON.parse(cached);
+          setCachedCompanies(parsedData);
+          logDebug('Cached data loaded successfully', { count: parsedData.length });
+        } catch (error) {
+          logDebug('Error parsing cached company data', error);
+          localStorage.removeItem(cacheKey); // Remove corrupted cache
+        }
+      } else {
+        logDebug('No cached data found');
+      }
+    }
+    setIsLoadingFromCache(false);
+  }, [cacheKey, authLoading, isAuthenticated, logDebug]);
+
+  // Step 1: Get company external IDs - only when user is authenticated and stable
+  const { data: myCompaniesResponse, isLoading: isLoadingCompanyIds, error: companyIdsError } = useQuery({
+    queryKey: ["my-companies", user?.external_id],
+    queryFn: async () => {
+      logDebug('🚀 Calling GET /v1/company/my-companies', { userExternalId: user?.external_id });
+      const response = await companyService.getMyCompanies();
+      logDebug('✅ GET /v1/company/my-companies RAW response', response);
+      logDebug('✅ GET /v1/company/my-companies response.data', response.data);
+      logDebug('✅ GET /v1/company/my-companies response.data.data', response.data?.data);
+
+      // Check if the response structure is correct
+      if (response.data?.data) {
+        logDebug('✅ Using response.data.data structure');
+        return response.data.data as GetMyCompaniesResponse;
+      } else if (response.data) {
+        logDebug('✅ Using response.data structure');
+        return response.data as GetMyCompaniesResponse;
+      } else {
+        logDebug('❌ Unexpected response structure', response);
+        throw new Error('Invalid response structure from /v1/company/my-companies');
+      }
+    },
+    enabled: !authLoading && isAuthenticated && !!user?.external_id,
+    staleTime: 10 * 60 * 1000, // 10 minutes - longer stale time to prevent duplicate calls
+    gcTime: 15 * 60 * 1000, // 15 minutes garbage collection time
+    retry: 1, // Only retry once on failure
+  });
+
+  const companyIds = myCompaniesResponse?.company_external_ids || [];
+  logDebug('🔍 Extracted company IDs', {
+    myCompaniesResponse,
+    company_external_ids: myCompaniesResponse?.company_external_ids,
+    companyIds,
+    companyIdsLength: companyIds.length
+  });
+
+  // Step 2: Fetch detailed company data for each ID
+  const { data: companiesDetails, isLoading: isLoadingCompanies, error: companiesError } = useQuery({
+    queryKey: ["companies-details", companyIds, user?.external_id],
+    queryFn: async () => {
+      if (!companyIds.length) {
+        logDebug('No company IDs to fetch details for');
+        return [];
+      }
+
+      logDebug('🚀 Fetching company details', { companyIds, count: companyIds.length });
+
+      const companiesPromises = companyIds.map(async (externalId) => {
+        logDebug(`🚀 Calling GET /v1/company/${externalId}`);
+        const response = await companyService.getCompany(externalId);
+        logDebug(`✅ GET /v1/company/${externalId} response`, {
+          name: response.data.data.name,
+          productsCount: response.data.data.products?.length || 0
+        });
+        return response.data as GetActiveCompanySuccessResponse;
+      });
+
+      const results = await Promise.all(companiesPromises);
+      const companies = results.map(result => result.data);
+      logDebug('✅ All company details fetched', { totalCompanies: companies.length });
+      return companies;
+    },
+    enabled: !authLoading && isAuthenticated && companyIds.length > 0,
+    staleTime: 10 * 60 * 1000, // 10 minutes
+    gcTime: 15 * 60 * 1000, // 15 minutes
+    retry: 1,
+  });
+
+  // Step 3: Cache the fetched data locally
+  useEffect(() => {
+    if (companiesDetails && companiesDetails.length > 0 && cacheKey) {
+      logDebug('💾 Caching company data', { count: companiesDetails.length, cacheKey });
+
+      const dataToCache: PartnerCompanyData[] = companiesDetails.map(company => ({
+        external_id: company.external_id,
+        name: company.name,
+        picture: company.picture,
+        is_active: company.is_active,
+        products: company.products || [],
+        address: company.address,
+        delivery_modes: company.delivery_modes,
+        shipping_fee: company.shipping_fee,
+      }));
+
+      try {
+        localStorage.setItem(cacheKey, JSON.stringify(dataToCache));
+        setCachedCompanies(dataToCache);
+        logDebug('✅ Data cached successfully');
+      } catch (error) {
+        logDebug('❌ Error caching data', error);
+      }
+    }
+  }, [companiesDetails, cacheKey, logDebug]);
+
+  // Use fresh data if available, otherwise use cached data
+  const companies = companiesDetails || cachedCompanies;
+  const isLoading = authLoading || isLoadingFromCache || isLoadingCompanyIds || isLoadingCompanies;
+
+  // Calculate metrics from company data
+  const metrics = {
+    totalCompanies: companies.length,
+    totalProducts: companies.reduce((total, company) => total + (company.products?.length || 0), 0),
+    activeCompanies: companies.filter(company => company.is_active).length,
+    companiesWithProducts: companies.filter(company => (company.products?.length || 0) > 0).length,
+  };
+
+  // Refresh function to force refetch
+  const refreshData = useCallback(async () => {
+    logDebug('🔄 Refreshing data - clearing cache and invalidating queries');
+
+    if (cacheKey) {
+      localStorage.removeItem(cacheKey);
+      setCachedCompanies([]);
+    }
+
+    // Invalidate React Query cache
+    await queryClient.invalidateQueries({ queryKey: ["my-companies"] });
+    await queryClient.invalidateQueries({ queryKey: ["companies-details"] });
+
+    logDebug('✅ Data refresh completed');
+  }, [cacheKey, queryClient, logDebug]);
+
+  // Debug effect to track state changes
+  useEffect(() => {
+    logDebug('State update', {
+      authLoading,
+      isAuthenticated,
+      userExternalId: user?.external_id,
+      isLoadingCompanyIds,
+      isLoadingCompanies,
+      companyIdsCount: companyIds.length,
+      companiesCount: companies.length,
+      hasCache: cachedCompanies.length > 0,
+      errors: {
+        companyIdsError: companyIdsError?.message,
+        companiesError: companiesError?.message,
+      }
+    });
+  }, [
+    authLoading, isAuthenticated, user?.external_id, isLoadingCompanyIds,
+    isLoadingCompanies, companyIds.length, companies.length,
+    cachedCompanies.length, companyIdsError, companiesError, logDebug
+  ]);
+
+  return {
+    companies,
+    metrics,
+    isLoading,
+    refreshData,
+    hasCache: cachedCompanies.length > 0,
+    companyIds,
+    dashboardUrl: myCompaniesResponse?.dashboard_url,
+    isAdmin: myCompaniesResponse?.dashboard_url === "/admin/dashboard",
+    // Debug information
+    debug: {
+      authLoading,
+      isAuthenticated,
+      userExternalId: user?.external_id,
+      isLoadingCompanyIds,
+      isLoadingCompanies,
+      companyIdsError: companyIdsError?.message,
+      companiesError: companiesError?.message,
+      cacheKey,
+    }
+  };
+};
diff --git a/src/pages/Categories.tsx b/src/pages/Categories.tsx
index 23b7f66..a2d3b5e 100644
--- a/src/pages/Categories.tsx
+++ b/src/pages/Categories.tsx
@@ -20,6 +20,8 @@ import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from
 import { Label } from "@/components/ui/label";
 import { useToast } from "@/components/ui/use-toast";
 import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
+import { useData } from "@/contexts/DataContext";
+import { useAuth } from "@/contexts/AuthContext";
 
 interface CategoryFormData {
   name: string;
@@ -29,6 +31,7 @@ interface CategoryFormData {
 const Categories = () => {
   const { toast } = useToast();
   const queryClient = useQueryClient();
+  const { userRole, loading: authLoading } = useAuth();
   const [searchTerm, setSearchTerm] = useState("");
   const [pageNumber, setPageNumber] = useState(1);
   const [limit, setLimit] = useState(10);
@@ -50,6 +53,7 @@ const Categories = () => {
         throw error;
       }
     },
+    enabled: userRole === 'admin', // Only fetch if user is admin
   });
 
   const createCategory = useMutation({
@@ -188,6 +192,35 @@ const Categories = () => {
     </div>
   );
 
+  // Show loading while checking auth status
+  if (authLoading) {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+          <p>Carregando dados...</p>
+        </div>
+      </div>
+    );
+  }
+
+  // Check if user has admin access
+  if (userRole !== 'admin') {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
+          <h3 className="text-lg font-medium text-gray-900 mb-2">
+            Acesso Restrito
+          </h3>
+          <p className="text-gray-500">
+            Esta página é apenas para administradores.
+          </p>
+        </div>
+      </div>
+    );
+  }
+
   return (
     <div className="space-y-6">
       <div className="flex justify-between items-center">
diff --git a/src/pages/Companies.tsx b/src/pages/Companies.tsx
index cc201a0..17d5666 100644
--- a/src/pages/Companies.tsx
+++ b/src/pages/Companies.tsx
@@ -67,7 +67,7 @@ const Companies = () => {
     <div className="space-y-6">
       <div className="flex justify-between items-center">
         <h2 className="text-2xl font-bold tracking-tight">Empresas Parceiras</h2>
-        <Link to="/companies/new">
+        <Link to="/admin/companies/new">
           <Button>
             <Plus className="mr-2" size={18} />
             Nova Empresa
@@ -122,7 +122,7 @@ const Companies = () => {
                         </TableCell>
                         <TableCell>{formatDate(company.created_at)}</TableCell>
                         <TableCell className="text-right">
-                          <Link to={`/companies/${company.external_id}`}>
+                          <Link to={`/admin/companies/${company.external_id}`}>
                             <Button size="sm" variant="ghost">
                               <Eye size={16} className="mr-1" />
                               Ver
@@ -165,7 +165,7 @@ const Companies = () => {
           ) : (
             <div className="text-center py-8">
               <p className="mb-4">Nenhuma empresa cadastrada ainda.</p>
-              <Link to="/companies/new">
+              <Link to="/admin/companies/new">
                 <Button>Cadastrar Empresa</Button>
               </Link>
             </div>
diff --git a/src/pages/CompanyDetails.tsx b/src/pages/CompanyDetails.tsx
index 0000f2a..a6fa618 100644
--- a/src/pages/CompanyDetails.tsx
+++ b/src/pages/CompanyDetails.tsx
@@ -23,6 +23,8 @@ import { Input } from "@/components/ui/input";
 import { Badge } from "@/components/ui/badge";
 import { companyService, productService } from "@/services/api";
 import { GetActiveCompanySuccessResponse, GetActiveProductsResponse } from "@/types/api";
+import { useAccessControl } from "@/hooks/useAccessControl";
+import OwnerManagement from "@/components/OwnerManagement";
 import { toast } from "sonner";
 import {
   ArrowLeft,
@@ -38,7 +40,10 @@ import {
   BarChart3,
   Edit2,
   Save,
-  X
+  X,
+  Users,
+  Power,
+  PowerOff
 } from "lucide-react";
 import { ScrollArea } from "@/components/ui/scroll-area";
 import { Separator } from "@/components/ui/separator";
@@ -135,6 +140,7 @@ const CompanyDetails = () => {
   const queryClient = useQueryClient();
   const fileInputRef = useRef<HTMLInputElement>(null);
   const { products } = useData();
+  const { isAdmin } = useAccessControl();
   
   // Estado
   const [productDialogOpen, setProductDialogOpen] = useState(false);
@@ -278,6 +284,8 @@ const CompanyDetails = () => {
   });
   const companyData = companyInfo?.data;
 
+
+
   // Filtra produtos pelo termo de pesquisa
   const filteredProducts = products.filter((product) =>
     (product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
@@ -331,6 +339,22 @@ const CompanyDetails = () => {
     },
   });
 
+  // Mutation para atualizar status da empresa
+  const updateStatusMutation = useMutation({
+    mutationFn: async (activate: boolean) => {
+      if (!id) throw new Error("ID da empresa não fornecido");
+      return companyService.updateCompanyStatus(id, activate);
+    },
+    onSuccess: (_, activate) => {
+      toast.success(`Empresa ${activate ? 'ativada' : 'desativada'} com sucesso!`);
+      queryClient.invalidateQueries({ queryKey: ["company", id] });
+      queryClient.invalidateQueries({ queryKey: ["companies"] });
+    },
+    onError: (error: any) => {
+      toast.error(error.response?.data?.message || "Erro ao atualizar status da empresa");
+    },
+  });
+
   // Mutation para adicionar produtos à empresa
   const addProductsMutation = useMutation({
     mutationFn: ({ productId, price, discount, stock }: {
@@ -438,14 +462,19 @@ const CompanyDetails = () => {
 
   // Funções para edição de campos
   const openEditModal = () => {
-    console.log("LATITUDE: ",companyData.address.location.latitude?.toString());
-    console.log("LONGITUDE: ",companyData.address.location.longitude?.toString());
+    // Get the first address from addresses array or fallback to legacy address field
+    const primaryAddress = companyData.addresses && companyData.addresses.length > 0
+      ? companyData.addresses[0]
+      : companyData.address;
+
+    console.log("LATITUDE: ", primaryAddress?.location?.latitude?.toString());
+    console.log("LONGITUDE: ", primaryAddress?.location?.longitude?.toString());
     setEditFormData({
       name: companyData.name || '',
       cnpj: companyData.cnpj || companyData.document || '',
       phone_numbers: companyData.phone_numbers?.[0] || '',
       bio: companyData.bio || '',
-      address: companyData.address || {
+      address: primaryAddress || {
         street: '',
         number: '',
         complement: '',
@@ -454,8 +483,8 @@ const CompanyDetails = () => {
         state: '',
         zip_code: ''
       },
-      address_location_latitude: companyData.address?.location?.latitude?.toString() || '',
-      address_location_longitude: companyData.address?.location?.longitude?.toString() || '',
+      address_location_latitude: primaryAddress?.location?.latitude?.toString() || '',
+      address_location_longitude: primaryAddress?.location?.longitude?.toString() || '',
       shipping_fee: companyData.shipping_fee ? (companyData.shipping_fee / 100).toString() : '0',
       delivery_modes: companyData.delivery_modes || []
     });
@@ -470,6 +499,18 @@ const CompanyDetails = () => {
     setEditFormData({});
   };
 
+  // Função para alternar status da empresa
+  const handleToggleStatus = () => {
+    if (!companyData) return;
+
+    const newStatus = !companyData.is_active;
+    const action = newStatus ? 'ativar' : 'desativar';
+
+    if (window.confirm(`Tem certeza que deseja ${action} esta empresa?`)) {
+      updateStatusMutation.mutate(newStatus);
+    }
+  };
+
   const saveCompanyData = () => {
     // Validar modalidades de entrega
     if (!editFormData.delivery_modes || editFormData.delivery_modes.length === 0) {
@@ -499,11 +540,15 @@ const CompanyDetails = () => {
         location: {
           latitude: getCoordinateValue(
             editFormData.address_location_latitude,
-            companyData.address?.location?.latitude
+            (companyData.addresses && companyData.addresses.length > 0
+              ? companyData.addresses[0].location?.latitude
+              : companyData.address?.location?.latitude)
           ),
           longitude: getCoordinateValue(
             editFormData.address_location_longitude,
-            companyData.address?.location?.longitude
+            (companyData.addresses && companyData.addresses.length > 0
+              ? companyData.addresses[0].location?.longitude
+              : companyData.address?.location?.longitude)
           )
         }
       },
@@ -612,14 +657,42 @@ const CompanyDetails = () => {
 
   return (
     <div className="space-y-6">
-      <div className="flex items-center space-x-2">
-        <Button variant="ghost" size="icon" onClick={() => navigate("/companies")}>
-          <ArrowLeft size={20} />
-        </Button>
-        <h2 className="text-2xl font-bold tracking-tight">{companyData.name}</h2>
-        <Badge variant={companyData.is_active ? "default" : "secondary"}>
-          {companyData.is_active ? "ATIVO" : "INATIVO"}
-        </Badge>
+      <div className="flex items-center justify-between">
+        <div className="flex items-center space-x-2">
+          <Button variant="ghost" size="icon" onClick={() => navigate("/admin/companies")}>
+            <ArrowLeft size={20} />
+          </Button>
+          <h2 className="text-2xl font-bold tracking-tight">{companyData.name}</h2>
+          <Badge variant={companyData.is_active ? "default" : "secondary"}>
+            {companyData.is_active ? "ATIVO" : "INATIVO"}
+          </Badge>
+        </div>
+
+        {/* Botão de ativar/desativar - apenas para admins */}
+        {isAdmin && (
+          <Button
+            variant={companyData.is_active ? "destructive" : "default"}
+            onClick={handleToggleStatus}
+            disabled={updateStatusMutation.isPending}
+            className="flex items-center space-x-2"
+          >
+            {updateStatusMutation.isPending ? (
+              <Loader size={16} className="animate-spin" />
+            ) : companyData.is_active ? (
+              <PowerOff size={16} />
+            ) : (
+              <Power size={16} />
+            )}
+            <span>
+              {updateStatusMutation.isPending
+                ? "Processando..."
+                : companyData.is_active
+                  ? "Desativar"
+                  : "Ativar"
+              }
+            </span>
+          </Button>
+        )}
       </div>
 
       {/* Imagem da empresa com upload */}
@@ -674,10 +747,13 @@ const CompanyDetails = () => {
         onValueChange={setActiveTab}
         className="w-full"
       >
-        <TabsList className="grid w-full grid-cols-3">
+        <TabsList className={`grid w-full ${isAdmin ? 'grid-cols-4' : 'grid-cols-3'}`}>
           <TabsTrigger value="details">Detalhes</TabsTrigger>
           <TabsTrigger value="orders">Pedidos</TabsTrigger>
           <TabsTrigger value="billing">Faturamento</TabsTrigger>
+          {isAdmin && (
+            <TabsTrigger value="owner">Proprietário</TabsTrigger>
+          )}
         </TabsList>
 
         {/* Conteúdo da Tab de Detalhes */}
@@ -731,7 +807,13 @@ const CompanyDetails = () => {
                 <div className="md:col-span-2">
                   <p className="text-sm font-medium text-gray-500">Endereço</p>
                   <p className="text-lg">
-                    {companyData.address ? (
+                    {companyData.addresses && companyData.addresses.length > 0 ? (
+                      <>
+                        {companyData.addresses[0].street}, {companyData.addresses[0].number} {companyData.addresses[0].complement ? ` - ${companyData.addresses[0].complement}` : ''} <br />
+                        {companyData.addresses[0].neighborhood}, {companyData.addresses[0].city} - {companyData.addresses[0].state} <br />
+                        CEP: {companyData.addresses[0].zip_code}
+                      </>
+                    ) : companyData.address ? (
                       <>
                         {companyData.address.street}, {companyData.address.number} {companyData.address.complement ? ` - ${companyData.address.complement}` : ''} <br />
                         {companyData.address.neighborhood}, {companyData.address.city} - {companyData.address.state} <br />
@@ -782,6 +864,20 @@ const CompanyDetails = () => {
                     <p className="text-lg">{new Date(companyData.updated_at).toLocaleDateString('pt-BR')}</p>
                   </div>
                 )}
+
+                {/* Proprietário - apenas para admins */}
+                {isAdmin && (
+                  <div className="md:col-span-2">
+                    <p className="text-sm font-medium text-gray-500">Proprietário</p>
+                    {companyData.owner ? (
+                      <div className="flex items-center space-x-2">
+                        <p className="text-lg">{companyData.owner.name}</p>
+                      </div>
+                    ) : (
+                      <p className="text-lg text-gray-400">Nenhum proprietário vinculado</p>
+                    )}
+                  </div>
+                )}
               </div>
             </CardContent>
           </Card>
@@ -890,6 +986,16 @@ const CompanyDetails = () => {
         <TabsContent value="billing" className="mt-6">
           <CompanyBillingTab />
         </TabsContent>
+
+        {/* Conteúdo da Tab de Proprietário - Apenas para Admins */}
+        {isAdmin && (
+          <TabsContent value="owner" className="mt-6">
+            <OwnerManagement
+              companyExternalId={id || ""}
+              currentOwner={companyData?.owner || null}
+            />
+          </TabsContent>
+        )}
       </Tabs>
 
       {/* Modal para editar dados da empresa */}
diff --git a/src/pages/CouponDetails.tsx b/src/pages/CouponDetails.tsx
index 755d2f1..90581a3 100644
--- a/src/pages/CouponDetails.tsx
+++ b/src/pages/CouponDetails.tsx
@@ -50,7 +50,7 @@ const CouponDetails = () => {
         title: "Cupom removido com sucesso",
         description: "O cupom foi removido permanentemente.",
       });
-      navigate("/coupons");
+      navigate("/admin/coupons");
     },
     onError: (error: any) => {
       toast({
diff --git a/src/pages/Coupons.tsx b/src/pages/Coupons.tsx
index da613dc..7a45709 100644
--- a/src/pages/Coupons.tsx
+++ b/src/pages/Coupons.tsx
@@ -67,7 +67,7 @@ const Coupons = () => {
             Gerencie os cupons de desconto disponíveis
           </p>
         </div>
-        <Button onClick={() => navigate("/coupons/new")}>
+        <Button onClick={() => navigate("/admin/coupons/new")}>
           <Plus className="mr-2 h-4 w-4" />
           Novo Cupom
         </Button>
diff --git a/src/pages/Dashboard.tsx b/src/pages/Dashboard.tsx
index 872e9b8..23974fd 100644
--- a/src/pages/Dashboard.tsx
+++ b/src/pages/Dashboard.tsx
@@ -4,11 +4,15 @@ import { Link } from "react-router-dom";
 import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
 import { Building, Package, Plus } from "lucide-react";
 import { Button } from "@/components/ui/button";
+import { Badge } from "@/components/ui/badge";
 import { useQuery } from "@tanstack/react-query";
 import { companyService, productService } from "@/services/api";
 import { GetActiveCompaniesSuccessResponse, GetActiveProductsSuccessPaginatedResponse } from "@/types/api";
+import { useData } from "@/contexts/DataContext";
 
 const Dashboard = () => {
+  const { isAdmin } = useData();
+
   // Busca as empresas do servidor
   const { data: companiesData, isLoading: isLoadingCompanies } = useQuery({
     queryKey: ["companies"],
@@ -16,6 +20,7 @@ const Dashboard = () => {
       const response = await companyService.getCompanies();
       return response.data as GetActiveCompaniesSuccessResponse;
     },
+    enabled: isAdmin, // Only fetch if user is admin
   });
 
   // Busca os produtos do servidor para mostrar a contagem
@@ -25,6 +30,7 @@ const Dashboard = () => {
       const response = await productService.getProducts(1, 10);
       return response.data as GetActiveProductsSuccessPaginatedResponse;
     },
+    enabled: isAdmin, // Only fetch if user is admin
   });
 
   // Resumo para o dashboard
@@ -87,13 +93,18 @@ const Dashboard = () => {
                   key={company.external_id}
                   className="flex items-center justify-between border-b pb-2"
                 >
-                  <div>
-                    <h3 className="font-medium">{company.name}</h3>
+                  <div className="flex-1">
+                    <div className="flex items-center space-x-2">
+                      <h3 className="font-medium">{company.name}</h3>
+                      <Badge variant={company.is_active ? "default" : "secondary"}>
+                        {company.is_active ? "Ativo" : "Inativo"}
+                      </Badge>
+                    </div>
                     <p className="text-sm text-muted-foreground">
                       {company.email}
                     </p>
                   </div>
-                  <Link to={`/companies/${company.external_id}`}>
+                  <Link to={`/admin/companies/${company.external_id}`}>
                     <Button variant="ghost" size="sm">
                       Visualizar
                     </Button>
@@ -105,7 +116,7 @@ const Dashboard = () => {
             <div className="text-center py-4">
               Nenhum parceiro cadastrado ainda.
               <div className="mt-4">
-                <Link to="/companies/new">
+                <Link to="/admin/companies/new">
                   <Button>Cadastrar Parceiro</Button>
                 </Link>
               </div>
diff --git a/src/pages/Login.tsx b/src/pages/Login.tsx
index d082016..4160c99 100644
--- a/src/pages/Login.tsx
+++ b/src/pages/Login.tsx
@@ -44,10 +44,21 @@ const Login = () => {
       const response = await authService.login({ email: values.email, login_code: values.login_code });
       await login(response.data);
     } catch (error: any) {
+      let errorMessage = "Tente novamente mais tarde";
+      let errorTitle = "Erro ao fazer login";
+
+      // Verificar se é erro de acesso não autorizado
+      if (error.message === "ACESSO NÃO AUTORIZADO!") {
+        errorTitle = "Acesso Negado";
+        errorMessage = "ACESSO NÃO AUTORIZADO! Você não tem permissão para acessar este sistema.";
+      } else if (error.response?.data?.message) {
+        errorMessage = error.response.data.message;
+      }
+
       toast({
         variant: "destructive",
-        title: "Erro ao fazer login",
-        description: error.response?.data?.message || "Tente novamente mais tarde",
+        title: errorTitle,
+        description: errorMessage,
       });
     } finally {
       setIsLoading(false);
diff --git a/src/pages/NewCompany.tsx b/src/pages/NewCompany.tsx
index 1ed78c8..e74454b 100644
--- a/src/pages/NewCompany.tsx
+++ b/src/pages/NewCompany.tsx
@@ -303,7 +303,7 @@ const NewCompany = () => {
       toast.success("Empresa criada com sucesso!");
 
       // Navigate to the company details page
-      navigate(`/companies/${response.data.data}`);
+      navigate(`/admin/companies/${response.data.data}`);
     },
     onError: (error: any) => {
       console.log("API Error:", error);
@@ -332,7 +332,7 @@ const NewCompany = () => {
   return (
     <div className="space-y-6">
       <div className="flex items-center space-x-2">
-        <Button variant="ghost" size="icon" onClick={() => navigate("/companies")}>
+        <Button variant="ghost" size="icon" onClick={() => navigate("/admin/companies")}>
           <ArrowLeft size={20} />
         </Button>
         <h2 className="text-2xl font-bold tracking-tight">Nova Empresa Parceira</h2>
@@ -777,7 +777,7 @@ const NewCompany = () => {
               </div>
 
               <div className="flex justify-end space-x-2">
-                <Button type="button" variant="outline" onClick={() => navigate("/companies")}>
+                <Button type="button" variant="outline" onClick={() => navigate("/admin/companies")}>
                   Cancelar
                 </Button>
                 <Button type="button" variant="secondary" onClick={testSubmit}>
diff --git a/src/pages/NewCoupon.tsx b/src/pages/NewCoupon.tsx
index 8ffa63f..d789378 100644
--- a/src/pages/NewCoupon.tsx
+++ b/src/pages/NewCoupon.tsx
@@ -116,7 +116,7 @@ export function NewCoupon() {
         // Then show success message
         toast.success("Cupom criado com sucesso!");
         // Finally navigate
-        navigate("/coupons", { replace: true });
+        navigate("/admin/coupons", { replace: true });
       } catch (error) {
         console.error("Error during success handling:", error);
         toast.error("Erro ao finalizar criação do cupom");
@@ -326,7 +326,7 @@ export function NewCoupon() {
                 <Button
                   type="button"
                   variant="outline"
-                  onClick={() => navigate("/coupons")}
+                  onClick={() => navigate("/admin/coupons")}
                 >
                   Cancelar
                 </Button>
diff --git a/src/pages/PartnerCompanies.tsx b/src/pages/PartnerCompanies.tsx
new file mode 100644
index 0000000..caff417
--- /dev/null
+++ b/src/pages/PartnerCompanies.tsx
@@ -0,0 +1,210 @@
+import React from "react";
+import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
+import { Button } from "@/components/ui/button";
+import { Building, Package, ShoppingBag, Eye } from "lucide-react";
+import { Link } from "react-router-dom";
+import { usePartnerData } from "@/hooks/usePartnerData";
+
+const PartnerCompanies = () => {
+  // Use the custom hook for partner data management
+  const { companies: userCompaniesData, metrics, isLoading, debug } = usePartnerData();
+
+  // Debug logging
+  React.useEffect(() => {
+    console.log('📋 [PartnerCompanies] State Update:', {
+      isLoading,
+      companiesCount: userCompaniesData?.length || 0,
+      metrics,
+      debug
+    });
+  }, [isLoading, userCompaniesData, metrics, debug]);
+
+  if (isLoading) {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+          <p>Carregando suas empresas...</p>
+        </div>
+      </div>
+    );
+  }
+
+
+
+  return (
+    <div className="space-y-6">
+      {/* Header */}
+      <div className="flex justify-between items-center">
+        <div>
+          <h2 className="text-2xl font-bold tracking-tight">Minhas Empresas</h2>
+          <p className="text-muted-foreground">
+            Empresas que você tem permissão para gerenciar
+          </p>
+        </div>
+      </div>
+
+      {/* Companies Grid */}
+      {(Array.isArray(userCompaniesData) && userCompaniesData.length > 0) ? (
+        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
+          {userCompaniesData.map((company) => (
+            <Card key={company.external_id} className="hover:shadow-lg transition-shadow">
+              <CardHeader className="pb-3">
+                <div className="flex items-start space-x-3">
+                  {company.picture ? (
+                    <img
+                      src={company.picture}
+                      alt={company.name}
+                      className="w-16 h-16 rounded-lg object-cover"
+                    />
+                  ) : (
+                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
+                      <Building size={24} className="text-gray-500" />
+                    </div>
+                  )}
+                  <div className="flex-1 min-w-0">
+                    <CardTitle className="text-lg truncate">{company.name}</CardTitle>
+                    <CardDescription className="text-sm">
+                      {company.cnpj || 'CNPJ não informado'}
+                    </CardDescription>
+                    {company.is_active ? (
+                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-1">
+                        Ativa
+                      </span>
+                    ) : (
+                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-1">
+                        Inativa
+                      </span>
+                    )}
+                  </div>
+                </div>
+              </CardHeader>
+              
+              <CardContent className="space-y-4">
+                {/* Company Stats */}
+                <div className="grid grid-cols-2 gap-4 text-center">
+                  <div className="p-3 bg-blue-50 rounded-lg">
+                    <Package size={20} className="mx-auto text-blue-600 mb-1" />
+                    <p className="text-sm font-medium text-blue-900">
+                      {company.products?.length || 0}
+                    </p>
+                    <p className="text-xs text-blue-600">Produtos</p>
+                  </div>
+                  <div className={`p-3 rounded-lg ${company.is_active ? 'bg-green-50' : 'bg-red-50'}`}>
+                    <ShoppingBag size={20} className={`mx-auto mb-1 ${company.is_active ? 'text-green-600' : 'text-red-600'}`} />
+                    <p className={`text-sm font-medium ${company.is_active ? 'text-green-900' : 'text-red-900'}`}>
+                      {company.is_active ? 'Ativa' : 'Inativa'}
+                    </p>
+                    <p className={`text-xs ${company.is_active ? 'text-green-600' : 'text-red-600'}`}>Status</p>
+                  </div>
+                </div>
+
+                {/* Company Info */}
+                <div className="space-y-2 text-sm">
+                  {company.email && (
+                    <div className="flex justify-between">
+                      <span className="text-muted-foreground">Email:</span>
+                      <span className="truncate ml-2">{company.email}</span>
+                    </div>
+                  )}
+                  {company.phone_numbers?.[0] && company.phone_numbers.length > 0 && (
+                    <div className="flex justify-between">
+                      <span className="text-muted-foreground">Telefone:</span>
+                      <span className="ml-2">{company.phone_numbers[0]}</span>
+                    </div>
+                  )}
+                  {company.address && (
+                    <div className="flex justify-between">
+                      <span className="text-muted-foreground">Cidade:</span>
+                      <span className="ml-2">{company.address.city}, {company.address.state}</span>
+                    </div>
+                  )}
+                  {company.delivery_modes && company.delivery_modes.length > 0 && (
+                    <div className="flex justify-between">
+                      <span className="text-muted-foreground">Modalidades:</span>
+                      <span className="ml-2 capitalize">{company.delivery_modes.join(', ')}</span>
+                    </div>
+                  )}
+                  {company.shipping_fee !== undefined && (
+                    <div className="flex justify-between">
+                      <span className="text-muted-foreground">Taxa de entrega:</span>
+                      <span className="ml-2">R$ {(company.shipping_fee / 100).toFixed(2)}</span>
+                    </div>
+                  )}
+                </div>
+
+                {/* Action Buttons */}
+                <div className="flex space-x-2">
+                  <Link to={`/partner/companies/${company.external_id}`} className="flex-1">
+                    <Button className="w-full" size="sm">
+                      <Eye size={16} className="mr-2" />
+                      Gerenciar
+                    </Button>
+                  </Link>
+                </div>
+
+                {/* Bio/Description */}
+                {company.bio && (
+                  <div className="pt-2 border-t">
+                    <p className="text-xs text-muted-foreground line-clamp-2">
+                      {company.bio}
+                    </p>
+                  </div>
+                )}
+              </CardContent>
+            </Card>
+          ))}
+        </div>
+      ) : (
+        <Card>
+          <CardContent className="text-center py-12">
+            <Building size={64} className="mx-auto text-gray-400 mb-4" />
+            <h3 className="text-lg font-medium text-gray-900 mb-2">
+              Nenhuma empresa encontrada
+            </h3>
+            <p className="text-gray-500 mb-4">
+              Você não tem permissão para gerenciar nenhuma empresa no momento.
+            </p>
+            <p className="text-sm text-gray-400">
+              Entre em contato com o administrador para obter acesso às empresas.
+            </p>
+          </CardContent>
+        </Card>
+      )}
+
+      {/* Summary Card */}
+      {(Array.isArray(userCompaniesData) && userCompaniesData.length > 0) && (
+        <Card>
+          <CardHeader>
+            <CardTitle>Resumo</CardTitle>
+          </CardHeader>
+          <CardContent>
+            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
+              <div className="p-4 bg-blue-50 rounded-lg">
+                <Building size={24} className="mx-auto text-blue-600 mb-2" />
+                <p className="text-2xl font-bold text-blue-900">{metrics.totalCompanies}</p>
+                <p className="text-sm text-blue-600">Empresas Gerenciadas</p>
+              </div>
+              <div className="p-4 bg-green-50 rounded-lg">
+                <Package size={24} className="mx-auto text-green-600 mb-2" />
+                <p className="text-2xl font-bold text-green-900">
+                  {metrics.totalProducts}
+                </p>
+                <p className="text-sm text-green-600">Total de Produtos</p>
+              </div>
+              <div className="p-4 bg-purple-50 rounded-lg">
+                <ShoppingBag size={24} className="mx-auto text-purple-600 mb-2" />
+                <p className="text-2xl font-bold text-purple-900">
+                  {metrics.activeCompanies}
+                </p>
+                <p className="text-sm text-purple-600">Empresas Ativas</p>
+              </div>
+            </div>
+          </CardContent>
+        </Card>
+      )}
+    </div>
+  );
+};
+
+export default PartnerCompanies;
diff --git a/src/pages/PartnerCompanyDetails.tsx b/src/pages/PartnerCompanyDetails.tsx
new file mode 100644
index 0000000..2f1bc18
--- /dev/null
+++ b/src/pages/PartnerCompanyDetails.tsx
@@ -0,0 +1,638 @@
+import React, { useState, useMemo } from "react";
+import { useParams, Navigate } from "react-router-dom";
+import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
+import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { Button } from "@/components/ui/button";
+import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
+import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
+import { Input } from "@/components/ui/input";
+import { Label } from "@/components/ui/label";
+import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
+import { ScrollArea } from "@/components/ui/scroll-area";
+import { Separator } from "@/components/ui/separator";
+import { companyService, productService } from "@/services/api";
+import { useAuth } from "@/contexts/AuthContext";
+import { GetActiveCompanySuccessResponse, GetActiveProductsResponse } from "@/types/api";
+import { ArrowLeft, Building, Package, ShoppingBag, BarChart3, Plus, Search, Trash2, Check } from "lucide-react";
+import { Link } from "react-router-dom";
+import CompanyOrdersTab from "@/components/CompanyOrdersTab";
+import CompanyBillingTab from "@/components/CompanyBillingTab";
+import { toast } from "sonner";
+import { useForm } from "react-hook-form";
+import { CurrencyInput } from "@/components/ui/currency-input";
+
+interface ProductFormValues {
+  price: number; // Value in centavos
+  discount: number;
+  stock: number;
+}
+
+const PartnerCompanyDetails = () => {
+  const { id } = useParams<{ id: string }>();
+  const { userCompanies } = useAuth();
+  const queryClient = useQueryClient();
+  const [activeTab, setActiveTab] = useState("products");
+  const [productDialogOpen, setProductDialogOpen] = useState(false);
+  const [selectedProduct, setSelectedProduct] = useState<GetActiveProductsResponse | null>(null);
+  const [searchTerm, setSearchTerm] = useState("");
+
+  // Form for product pricing
+  const { register, handleSubmit, reset, setValue, formState: { errors } } = useForm<ProductFormValues>();
+  const [priceValue, setPriceValue] = useState<number>(0);
+
+  // Verificar se o usuário tem acesso a esta empresa
+  const hasAccess = userCompanies.includes(id || "");
+
+  // Se não tem acesso, redirecionar
+  if (!hasAccess) {
+    return <Navigate to="/partner/dashboard" replace />;
+  }
+
+  // Buscar dados da empresa
+  const { data: companyResponse, isLoading, error } = useQuery({
+    queryKey: ["company", id],
+    queryFn: async () => {
+      if (!id) throw new Error("ID da empresa não fornecido");
+      const response = await companyService.getCompany(id);
+      return response.data as GetActiveCompanySuccessResponse;
+    },
+    enabled: !!id,
+  });
+
+  // Buscar todos os produtos disponíveis para adicionar
+  const { data: productsResponse, isLoading: isLoadingProducts } = useQuery({
+    queryKey: ["products-for-partner"],
+    queryFn: async () => {
+      const response = await productService.getProducts(1, 10000);
+      return response.data;
+    },
+    enabled: productDialogOpen, // Only fetch when dialog is open
+  });
+
+  const allProducts = productsResponse?.data || [];
+
+  const companyData = companyResponse?.data;
+
+  // Filter products based on search term
+  const filteredProducts = useMemo(() => {
+    if (!searchTerm) return allProducts;
+    return allProducts.filter((product) =>
+      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
+      product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
+      product.ean.includes(searchTerm)
+    );
+  }, [allProducts, searchTerm]);
+
+  // Mutation para adicionar produtos à empresa
+  const addProductsMutation = useMutation({
+    mutationFn: ({ productId, price, discount, stock }: {
+      productId: string;
+      price: number; // Already in centavos from CurrencyInput
+      discount: number;
+      stock: number
+    }) => {
+      if (!id) throw new Error("ID da empresa não fornecido");
+      return companyService.addProductToCompany(id, {
+        product_external_id: productId,
+        price: price,
+        discount,
+        stock
+      });
+    },
+    onSuccess: () => {
+      toast.success("Produto adicionado com sucesso!");
+      queryClient.invalidateQueries({ queryKey: ["company", id] });
+      setSelectedProduct(null);
+      setProductDialogOpen(false);
+      reset();
+    },
+    onError: (error: any) => {
+      const errorMsg = error.response?.data?.message || "Erro ao adicionar produto";
+      toast.error(errorMsg);
+    },
+  });
+
+  // Mutation para remover produtos da empresa
+  const removeProductMutation = useMutation({
+    mutationFn: (productId: string) => {
+      if (!id) throw new Error("ID da empresa não fornecido");
+      return companyService.removeProductsFromCompany(id, {
+        product_external_id: productId,
+        price: 0,
+        discount: 0,
+        stock: 0
+      });
+    },
+    onSuccess: () => {
+      toast.success("Produto removido com sucesso!");
+      queryClient.invalidateQueries({ queryKey: ["company", id] });
+    },
+    onError: (error: any) => {
+      const errorMsg = error.response?.data?.message || "Erro ao remover produto";
+      toast.error(errorMsg);
+    },
+  });
+
+  // Verifica se a empresa já tem o produto
+  const isProductAlreadyAdded = (productId: string) => {
+    return companyData?.products?.some((product) => product.external_id === productId) || false;
+  };
+
+  // Handler para selecionar produto
+  const handleProductSelection = (product: GetActiveProductsResponse) => {
+    if (isProductAlreadyAdded(product.external_id)) {
+      toast.info("Este produto já foi adicionado");
+      return;
+    }
+    setSelectedProduct(product);
+  };
+
+  // Handler para adicionar produto com preço, desconto e estoque
+  const handleAddProduct = (values: ProductFormValues) => {
+    if (!selectedProduct) return;
+
+    addProductsMutation.mutate({
+      productId: selectedProduct.external_id,
+      price: values.price || 0,
+      discount: values.discount || 0,
+      stock: values.stock || 0
+    });
+  };
+
+  // Handler para remover produto
+  const handleRemoveProduct = (productId: string) => {
+    if (confirm("Tem certeza que deseja remover este produto?")) {
+      removeProductMutation.mutate(productId);
+    }
+  };
+
+  // Função para fechar o modal e limpar seleção
+  const handleCloseModal = () => {
+    setProductDialogOpen(false);
+    setSelectedProduct(null);
+    setSearchTerm("");
+    setPriceValue(0);
+    reset();
+  };
+
+  // Função para formatar preço
+  const formatPrice = (price: number) => {
+    return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(price / 100);
+  };
+
+  if (isLoading) {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+          <p>Carregando dados da empresa...</p>
+        </div>
+      </div>
+    );
+  }
+
+  if (error || !companyData) {
+    return (
+      <div className="text-center py-8">
+        <p className="text-red-500 mb-4">Erro ao carregar dados da empresa</p>
+        <Link to="/partner/dashboard">
+          <Button variant="outline">Voltar ao Dashboard</Button>
+        </Link>
+      </div>
+    );
+  }
+
+  return (
+    <div className="space-y-6">
+      {/* Header */}
+      <div className="flex items-center justify-between">
+        <div className="flex items-center space-x-4">
+          <Link to="/partner/dashboard">
+            <Button variant="outline" size="icon">
+              <ArrowLeft size={16} />
+            </Button>
+          </Link>
+          <div>
+            <h1 className="text-2xl font-bold">{companyData.name}</h1>
+            <p className="text-muted-foreground">Gerenciar empresa</p>
+          </div>
+        </div>
+      </div>
+
+      {/* Company Info Card - Read Only */}
+      <Card>
+        <CardHeader>
+          <CardTitle className="flex items-center">
+            <Building className="mr-2" size={20} />
+            Informações da Empresa
+          </CardTitle>
+        </CardHeader>
+        <CardContent>
+          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
+            <div>
+              <p className="text-sm font-medium text-gray-500">Nome</p>
+              <p className="text-lg">{companyData.name}</p>
+            </div>
+            <div>
+              <p className="text-sm font-medium text-gray-500">CNPJ</p>
+              <p className="text-lg">{companyData.cnpj || companyData.document}</p>
+            </div>
+            <div>
+              <p className="text-sm font-medium text-gray-500">Email</p>
+              <p className="text-lg">{companyData.email}</p>
+            </div>
+            <div>
+              <p className="text-sm font-medium text-gray-500">Telefone</p>
+              <p className="text-lg">{companyData.phone_numbers?.[0] || "Não informado"}</p>
+            </div>
+            <div>
+              <p className="text-sm font-medium text-gray-500">Status</p>
+              <p className="text-lg capitalize">{companyData.status}</p>
+            </div>
+            <div>
+              <p className="text-sm font-medium text-gray-500">Produtos</p>
+              <p className="text-lg">{companyData.products?.length || 0}</p>
+            </div>
+          </div>
+          {companyData.bio && (
+            <div className="mt-4">
+              <p className="text-sm font-medium text-gray-500">Descrição</p>
+              <p className="text-base mt-1">{companyData.bio}</p>
+            </div>
+          )}
+          {companyData.address && (
+            <div className="mt-4">
+              <p className="text-sm font-medium text-gray-500">Endereço</p>
+              <p className="text-base mt-1">
+                {companyData.address.street}, {companyData.address.number}
+                {companyData.address.complement && `, ${companyData.address.complement}`}
+                <br />
+                {companyData.address.neighborhood}, {companyData.address.city} - {companyData.address.state}
+                <br />
+                CEP: {companyData.address.zip_code}
+              </p>
+            </div>
+          )}
+        </CardContent>
+      </Card>
+
+      {/* Tabs for Management */}
+      <Tabs value={activeTab} onValueChange={setActiveTab}>
+        <TabsList className="grid w-full grid-cols-3">
+          <TabsTrigger value="products" className="flex items-center">
+            <Package className="mr-2" size={16} />
+            Produtos
+          </TabsTrigger>
+          <TabsTrigger value="orders" className="flex items-center">
+            <ShoppingBag className="mr-2" size={16} />
+            Pedidos
+          </TabsTrigger>
+          <TabsTrigger value="billing" className="flex items-center">
+            <BarChart3 className="mr-2" size={16} />
+            Faturamento
+          </TabsTrigger>
+        </TabsList>
+
+        {/* Products Tab */}
+        <TabsContent value="products" className="mt-6">
+          <Card>
+            <CardHeader className="flex flex-row items-center justify-between">
+              <CardTitle className="flex items-center">
+                <Package className="mr-2" size={20} />
+                Produtos
+              </CardTitle>
+              <Button onClick={() => setProductDialogOpen(true)}>
+                <Plus className="mr-2" size={18} />
+                Adicionar Produto
+              </Button>
+            </CardHeader>
+            <CardContent>
+              {companyData.products && companyData.products.length > 0 ? (
+                <div className="rounded-md border">
+                  <Table>
+                    <TableHeader>
+                      <TableRow>
+                        <TableHead>Imagem</TableHead>
+                        <TableHead>Nome</TableHead>
+                        <TableHead>Marca</TableHead>
+                        <TableHead>EAN</TableHead>
+                        <TableHead>Preço</TableHead>
+                        <TableHead>Desconto</TableHead>
+                        <TableHead>Estoque</TableHead>
+                        <TableHead>Categorias</TableHead>
+                        <TableHead>SKU</TableHead>
+                        <TableHead>Ações</TableHead>
+                      </TableRow>
+                    </TableHeader>
+                    <TableBody>
+                      {companyData.products.map((product) => (
+                        <TableRow key={product.external_id}>
+                          <TableCell>
+                            {product.image ? (
+                              <img
+                                src={product.image}
+                                alt={product.name}
+                                className="w-12 h-12 object-cover rounded-lg"
+                              />
+                            ) : (
+                              <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
+                                <Package size={16} className="text-gray-500" />
+                              </div>
+                            )}
+                          </TableCell>
+                          <TableCell className="font-medium">{product.name}</TableCell>
+                          <TableCell>{product.brand}</TableCell>
+                          <TableCell>{product.ean}</TableCell>
+                          <TableCell>{formatPrice(product.price || 0)}</TableCell>
+                          <TableCell>{product.discount || 0}%</TableCell>
+                          <TableCell>{product.stock || 0}</TableCell>
+                          <TableCell>
+                            <div className="flex flex-wrap gap-1">
+                              {product.categories?.map((category) => (
+                                <span
+                                  key={category.external_id}
+                                  className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
+                                >
+                                  {category.name}
+                                </span>
+                              )) || "Sem categoria"}
+                            </div>
+                          </TableCell>
+                          <TableCell>{product.external_id}</TableCell>
+                          <TableCell>
+                            <Button
+                              variant="ghost"
+                              size="icon"
+                              onClick={() => handleRemoveProduct(product.external_id)}
+                              className="text-red-500 hover:text-red-700 hover:bg-red-50"
+                            >
+                              <Trash2 size={16} />
+                            </Button>
+                          </TableCell>
+                        </TableRow>
+                      ))}
+                    </TableBody>
+                  </Table>
+                </div>
+              ) : (
+                <div className="text-center py-8">
+                  <p className="mb-4">Este parceiro ainda não tem produtos associados.</p>
+                </div>
+              )}
+            </CardContent>
+          </Card>
+        </TabsContent>
+
+        {/* Orders Tab */}
+        <TabsContent value="orders" className="mt-6">
+          <CompanyOrdersTab companyData={companyData} />
+        </TabsContent>
+
+        {/* Billing Tab */}
+        <TabsContent value="billing" className="mt-6">
+          <CompanyBillingTab />
+        </TabsContent>
+      </Tabs>
+
+      {/* Modal para adicionar produtos */}
+      <Dialog open={productDialogOpen} onOpenChange={handleCloseModal}>
+        <DialogContent className="sm:max-w-3xl">
+          <DialogHeader>
+            <DialogTitle>Adicionar Produto</DialogTitle>
+            <DialogDescription>
+              {selectedProduct
+                ? "Informe o preço, desconto e estoque para o produto selecionado"
+                : "Clique em um produto para selecioná-lo"}
+            </DialogDescription>
+          </DialogHeader>
+
+          <div className="py-4">
+            {!selectedProduct ? (
+              <>
+                <div className="flex items-center space-x-2 mb-4">
+                  <Search size={20} className="text-gray-400" />
+                  <Input
+                    placeholder="Buscar por nome, marca ou código"
+                    value={searchTerm}
+                    onChange={(e) => setSearchTerm(e.target.value)}
+                    className="flex-1"
+                  />
+                </div>
+
+                <Separator className="my-4" />
+
+                <ScrollArea className="h-[400px] rounded-md">
+                  {isLoadingProducts ? (
+                    <div className="flex items-center justify-center h-32">
+                      <div className="text-center">
+                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+                        <p>Carregando produtos...</p>
+                      </div>
+                    </div>
+                  ) : filteredProducts.length > 0 ? (
+                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4">
+                      {filteredProducts.map((product) => {
+                        const isAdded = isProductAlreadyAdded(product.external_id);
+                        const isSelected = selectedProduct?.external_id === product.external_id;
+
+                        return (
+                          <div
+                            key={product.external_id}
+                            className={`relative rounded-lg border p-4 transition-all ${
+                              isAdded
+                                ? "bg-green-50 border-green-200"
+                                : isSelected
+                                  ? "bg-blue-50 border-blue-200 ring-2 ring-blue-400"
+                                  : "hover:bg-gray-50 hover:border-gray-300 cursor-pointer"
+                            }`}
+                            onClick={() => {
+                              if (!isAdded) {
+                                handleProductSelection(product);
+                              }
+                            }}
+                          >
+                            {isAdded && (
+                              <div className="absolute top-2 right-2">
+                                <Check size={16} className="text-green-600" />
+                              </div>
+                            )}
+
+                            <div className="flex items-start space-x-3">
+                              {product.image ? (
+                                <img
+                                  src={product.image}
+                                  alt={product.name}
+                                  className="w-16 h-16 object-cover rounded-lg"
+                                />
+                              ) : (
+                                <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
+                                  <Package size={20} className="text-gray-500" />
+                                </div>
+                              )}
+
+                              <div className="flex-1 min-w-0">
+                                <h3 className="font-medium text-sm truncate">{product.name}</h3>
+                                <p className="text-xs text-gray-500 mt-1">Marca: {product.brand}</p>
+                                <p className="text-xs text-gray-500">EAN: {product.ean}</p>
+
+                                {product.categories && product.categories.length > 0 && (
+                                  <div className="flex flex-wrap gap-1 mt-2">
+                                    {product.categories.slice(0, 2).map((category) => (
+                                      <span
+                                        key={category.external_id}
+                                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
+                                      >
+                                        {category.name}
+                                      </span>
+                                    ))}
+                                    {product.categories.length > 2 && (
+                                      <span className="text-xs text-gray-500">
+                                        +{product.categories.length - 2} mais
+                                      </span>
+                                    )}
+                                  </div>
+                                )}
+                              </div>
+                            </div>
+
+                            {isAdded && (
+                              <div className="mt-2 text-xs text-green-600 font-medium">
+                                ✓ Produto já adicionado
+                              </div>
+                            )}
+                          </div>
+                        );
+                      })}
+                    </div>
+                  ) : (
+                    <div className="text-center py-8">
+                      <Package size={48} className="mx-auto text-gray-400 mb-4" />
+                      <p className="text-gray-500">Nenhum produto encontrado</p>
+                    </div>
+                  )}
+                </ScrollArea>
+              </>
+            ) : (
+              <div className="space-y-6">
+                {/* Produto selecionado */}
+                <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
+                  {selectedProduct.image ? (
+                    <img
+                      src={selectedProduct.image}
+                      alt={selectedProduct.name}
+                      className="w-16 h-16 object-cover rounded-lg"
+                    />
+                  ) : (
+                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
+                      <Package size={20} className="text-gray-500" />
+                    </div>
+                  )}
+
+                  <div className="flex-1">
+                    <h3 className="font-medium">{selectedProduct.name}</h3>
+                    <p className="text-sm text-gray-600">Marca: {selectedProduct.brand}</p>
+                    <p className="text-sm text-gray-600">EAN: {selectedProduct.ean}</p>
+                  </div>
+
+                  <Button
+                    variant="outline"
+                    size="sm"
+                    onClick={() => setSelectedProduct(null)}
+                  >
+                    Trocar produto
+                  </Button>
+                </div>
+
+                {/* Formulário de preço, desconto e estoque */}
+                <form onSubmit={handleSubmit(handleAddProduct)} className="space-y-4">
+                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
+                    <div>
+                      <Label htmlFor="price">Preço (R$)</Label>
+                      <CurrencyInput
+                        id="price"
+                        placeholder="R$ 0,00"
+                        value={priceValue}
+                        onValueChange={(value) => {
+                          setPriceValue(value);
+                          setValue("price", value, { shouldValidate: true });
+                        }}
+                        className={errors.price ? "border-red-500" : ""}
+                      />
+                      {errors.price && (
+                        <p className="text-sm text-red-500 mt-1">{errors.price.message}</p>
+                      )}
+                      {/* Hidden input for form validation */}
+                      <input
+                        type="hidden"
+                        {...register("price", {
+                          required: "Preço é obrigatório",
+                          min: { value: 1, message: "Preço deve ser maior que zero" }
+                        })}
+                      />
+                    </div>
+
+                    <div>
+                      <Label htmlFor="discount">Desconto (%)</Label>
+                      <Input
+                        id="discount"
+                        type="number"
+                        min="0"
+                        max="100"
+                        step="0.01"
+                        placeholder="0"
+                        {...register("discount", {
+                          valueAsNumber: true,
+                          min: { value: 0, message: "Desconto não pode ser negativo" },
+                          max: { value: 100, message: "Desconto não pode ser maior que 100%" }
+                        })}
+                      />
+                      {errors.discount && (
+                        <p className="text-sm text-red-500 mt-1">{errors.discount.message}</p>
+                      )}
+                    </div>
+
+                    <div>
+                      <Label htmlFor="stock">Estoque</Label>
+                      <Input
+                        id="stock"
+                        type="number"
+                        min="0"
+                        placeholder="0"
+                        {...register("stock", {
+                          valueAsNumber: true,
+                          required: "Estoque é obrigatório",
+                          min: { value: 0, message: "Estoque não pode ser negativo" }
+                        })}
+                      />
+                      {errors.stock && (
+                        <p className="text-sm text-red-500 mt-1">{errors.stock.message}</p>
+                      )}
+                    </div>
+                  </div>
+
+                  <div className="flex justify-end space-x-2">
+                    <Button
+                      type="button"
+                      variant="outline"
+                      onClick={handleCloseModal}
+                    >
+                      Cancelar
+                    </Button>
+                    <Button
+                      type="submit"
+                      disabled={addProductsMutation.isPending}
+                    >
+                      {addProductsMutation.isPending ? "Adicionando..." : "Adicionar Produto"}
+                    </Button>
+                  </div>
+                </form>
+              </div>
+            )}
+          </div>
+        </DialogContent>
+      </Dialog>
+    </div>
+  );
+};
+
+export default PartnerCompanyDetails;
diff --git a/src/pages/PartnerDashboard.tsx b/src/pages/PartnerDashboard.tsx
new file mode 100644
index 0000000..3657289
--- /dev/null
+++ b/src/pages/PartnerDashboard.tsx
@@ -0,0 +1,249 @@
+import React from "react";
+import { useQuery } from "@tanstack/react-query";
+import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
+import { orderService } from "@/services/api";
+import { ListOrdersSuccessResponse } from "@/types/api";
+import { Building, Package, ShoppingBag, TrendingUp, Loader2 } from "lucide-react";
+import { Link } from "react-router-dom";
+import { Button } from "@/components/ui/button";
+import { usePartnerData } from "@/hooks/usePartnerData";
+
+const PartnerDashboard = () => {
+
+  // Use the custom hook for partner data management
+  const { companies: userCompaniesData, metrics, isLoading, companyIds, hasCache, debug } = usePartnerData();
+
+  // Debug logging
+  React.useEffect(() => {
+    console.log('📊 [PartnerDashboard] State Update:', {
+      isLoading,
+      hasCache,
+      companyIds,
+      companiesCount: userCompaniesData?.length || 0,
+      metrics,
+      debug
+    });
+  }, [isLoading, hasCache, companyIds, userCompaniesData, metrics, debug]);
+
+  // Busca pedidos para mostrar métricas
+  const { data: ordersData, isLoading: isLoadingOrders } = useQuery({
+    queryKey: ["partner-orders"],
+    queryFn: async () => {
+      const response = await orderService.getOrders(1, 100); // Buscar mais pedidos para métricas
+      return response.data as ListOrdersSuccessResponse;
+    },
+  });
+
+  // Filter orders to only include orders from partner's companies
+  const partnerOrders = ordersData?.data?.filter(order =>
+    companyIds.includes(order.company_external_id)
+  ) || [];
+
+
+
+  // Calcula métricas dos pedidos (apenas das empresas do parceiro)
+  const orderMetrics = React.useMemo(() => {
+    if (!partnerOrders.length) return { total: 0, pending: 0, completed: 0, revenue: 0 };
+
+    const total = partnerOrders.length;
+    const pending = partnerOrders.filter(order =>
+      ['pending', 'processing', 'preparing', 'ready', 'delivering'].includes(order.status)
+    ).length;
+    const completed = partnerOrders.filter(order => order.status === 'completed').length;
+    const revenue = partnerOrders
+      .filter(order => order.status === 'completed')
+      .reduce((sum, order) => sum + order.amount, 0) / 100; // Convert from centavos to reais
+
+    return { total, pending, completed, revenue };
+  }, [partnerOrders]);
+
+
+
+  // Cards de resumo para o dashboard do parceiro
+  const summaryCards = [
+    {
+      title: "Minhas Empresas",
+      value: isLoading ? "..." : metrics.totalCompanies,
+      icon: Building,
+      description: "Empresas sob sua gestão",
+      color: "bg-blue-500",
+      href: "/partner/companies"
+    },
+    {
+      title: "Total de Produtos",
+      value: isLoading ? "..." : metrics.totalProducts,
+      icon: Package,
+      description: "Produtos em todas as empresas",
+      color: "bg-green-500",
+      href: "/partner/companies"
+    },
+    {
+      title: "Pedidos Ativos",
+      value: isLoadingOrders ? "..." : orderMetrics.pending,
+      icon: ShoppingBag,
+      description: "Pedidos em andamento",
+      color: "bg-orange-500",
+      href: "/partner/orders"
+    },
+    {
+      title: "Faturamento",
+      value: isLoadingOrders ? "..." : `R$ ${orderMetrics.revenue.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`,
+      icon: TrendingUp,
+      description: "Receita de pedidos concluídos",
+      color: "bg-purple-500",
+    },
+  ];
+
+  return (
+    <div className="space-y-8">
+      <div className="flex justify-between items-center">
+        <div>
+          <h2 className="text-2xl font-bold tracking-tight">Dashboard do Parceiro</h2>
+          <p className="text-muted-foreground">
+            Gerencie suas empresas, produtos e pedidos
+          </p>
+        </div>
+      </div>
+
+      {/* Cards de resumo */}
+      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
+        {summaryCards.map((card, index) => (
+          <Card key={index} className="overflow-hidden hover:shadow-md transition-shadow">
+            <CardHeader className={`${card.color} text-white p-4`}>
+              <div className="flex justify-between items-center">
+                <CardTitle className="text-lg font-medium">{card.title}</CardTitle>
+                <card.icon size={20} />
+              </div>
+            </CardHeader>
+            <CardContent className="p-4">
+              <div className="text-2xl font-bold mb-2">{card.value}</div>
+              <CardDescription className="mb-3">{card.description}</CardDescription>
+              {card.href && (
+                <Link to={card.href}>
+                  <Button variant="outline" size="sm" className="w-full">
+                    Ver detalhes
+                  </Button>
+                </Link>
+              )}
+            </CardContent>
+          </Card>
+        ))}
+      </div>
+
+      {/* Lista das empresas do usuário */}
+      <Card>
+        <CardHeader>
+          <CardTitle className="flex items-center">
+            <Building className="mr-2" size={20} />
+            Suas Empresas
+          </CardTitle>
+          <CardDescription>
+            Empresas que você tem permissão para gerenciar
+          </CardDescription>
+        </CardHeader>
+        <CardContent>
+          {isLoading ? (
+            <div className="text-center py-8">
+              <Loader2 className="mx-auto h-6 w-6 animate-spin mb-2" />
+              <p>Carregando empresas...</p>
+            </div>
+          ) : (Array.isArray(userCompaniesData) && userCompaniesData.length > 0) ? (
+            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
+              {userCompaniesData.map((company) => (
+                <Card key={company.external_id} className="hover:shadow-md transition-shadow">
+                  <CardContent className="p-4">
+                    <div className="flex items-start space-x-3">
+                      {company.picture ? (
+                        <img
+                          src={company.picture}
+                          alt={company.name}
+                          className="w-12 h-12 rounded-lg object-cover"
+                        />
+                      ) : (
+                        <div className="w-12 h-12 bg-gray-200 rounded-lg flex items-center justify-center">
+                          <Building size={20} className="text-gray-500" />
+                        </div>
+                      )}
+                      <div className="flex-1 min-w-0">
+                        <h3 className="font-medium text-sm truncate">{company.name}</h3>
+                        <p className="text-xs text-muted-foreground">
+                          {company.products?.length || 0} produtos
+                        </p>
+                        <div className="flex items-center mt-1">
+                          {company.is_active ? (
+                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
+                              Ativa
+                            </span>
+                          ) : (
+                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
+                              Inativa
+                            </span>
+                          )}
+                        </div>
+                        <Link to={`/partner/companies/${company.external_id}`}>
+                          <Button variant="outline" size="sm" className="mt-2 w-full">
+                            Gerenciar
+                          </Button>
+                        </Link>
+                      </div>
+                    </div>
+                  </CardContent>
+                </Card>
+              ))}
+            </div>
+          ) : (
+            <div className="text-center py-8">
+              <Building size={48} className="mx-auto text-gray-400 mb-4" />
+              <p className="text-gray-500 mb-2">Nenhuma empresa encontrada</p>
+              <p className="text-sm text-gray-400">
+                Você não tem permissão para gerenciar nenhuma empresa no momento.
+              </p>
+            </div>
+          )}
+        </CardContent>
+      </Card>
+
+      {/* Resumo de pedidos recentes */}
+      {partnerOrders.length > 0 && (
+        <Card>
+          <CardHeader>
+            <CardTitle className="flex items-center">
+              <ShoppingBag className="mr-2" size={20} />
+              Pedidos Recentes
+            </CardTitle>
+            <CardDescription>
+              Últimos pedidos das suas empresas
+            </CardDescription>
+          </CardHeader>
+          <CardContent>
+            <div className="space-y-3">
+              {partnerOrders.slice(0, 5).map((order) => (
+                <div key={order.order_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
+                  <div>
+                    <p className="font-medium text-sm">Pedido #{order.order_id.slice(-8)}</p>
+                    <p className="text-xs text-muted-foreground">{order.user_name}</p>
+                  </div>
+                  <div className="text-right">
+                    <p className="font-medium text-sm">
+                      R$ {(order.amount / 100).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
+                    </p>
+                    <p className="text-xs text-muted-foreground capitalize">{order.status}</p>
+                  </div>
+                </div>
+              ))}
+            </div>
+            <div className="mt-4">
+              <Link to="/partner/orders">
+                <Button variant="outline" className="w-full">
+                  Ver todos os pedidos
+                </Button>
+              </Link>
+            </div>
+          </CardContent>
+        </Card>
+      )}
+    </div>
+  );
+};
+
+export default PartnerDashboard;
diff --git a/src/pages/PartnerOrders.tsx b/src/pages/PartnerOrders.tsx
new file mode 100644
index 0000000..512088f
--- /dev/null
+++ b/src/pages/PartnerOrders.tsx
@@ -0,0 +1,83 @@
+import React from "react";
+import { useQuery } from "@tanstack/react-query";
+import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { orderService } from "@/services/api";
+import { usePartnerData } from "@/hooks/usePartnerData";
+import { ListOrdersSuccessResponse } from "@/types/api";
+import { ShoppingBag } from "lucide-react";
+import CompanyOrdersTab from "@/components/CompanyOrdersTab";
+
+const PartnerOrders = () => {
+  const { companyIds, companies } = usePartnerData();
+
+  // Busca pedidos para o parceiro
+  const { data: ordersData, isLoading, error } = useQuery({
+    queryKey: ["partner-orders"],
+    queryFn: async () => {
+      const response = await orderService.getOrders(1, 100);
+      return response.data as ListOrdersSuccessResponse;
+    },
+  });
+
+  // Filter orders to only show orders from partner's companies
+  const partnerOrders = ordersData?.data?.filter(order =>
+    companyIds.includes(order.company_external_id)
+  ) || [];
+
+  if (isLoading) {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+          <p>Carregando pedidos...</p>
+        </div>
+      </div>
+    );
+  }
+
+  if (error) {
+    return (
+      <div className="text-center py-8">
+        <p className="text-red-500 mb-4">Erro ao carregar pedidos</p>
+        <button 
+          onClick={() => window.location.reload()}
+          className="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90"
+        >
+          Tentar novamente
+        </button>
+      </div>
+    );
+  }
+
+  return (
+    <div className="space-y-6">
+      {/* Header */}
+      <div className="flex justify-between items-center">
+        <div>
+          <h2 className="text-2xl font-bold tracking-tight">Pedidos</h2>
+          <p className="text-muted-foreground">
+            Gerencie os pedidos das suas empresas
+          </p>
+        </div>
+      </div>
+
+      {/* Orders Management */}
+      <Card>
+        <CardHeader>
+          <CardTitle className="flex items-center">
+            <ShoppingBag className="mr-2" size={20} />
+            Todos os Pedidos
+          </CardTitle>
+        </CardHeader>
+        <CardContent>
+          <CompanyOrdersTab companyData={{
+            partnerCompanyIds: companyIds,
+            partnerCompanies: companies
+          }} />
+        </CardContent>
+      </Card>
+    </div>
+  );
+};
+
+export default PartnerOrders;
diff --git a/src/pages/Products.tsx b/src/pages/Products.tsx
index 6300248..51bfeea 100644
--- a/src/pages/Products.tsx
+++ b/src/pages/Products.tsx
@@ -16,6 +16,7 @@ import { Switch } from "@/components/ui/switch";
 import { useToast } from "@/components/ui/use-toast";
 import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
 import { useData } from "@/contexts/DataContext";
+import { useAuth } from "@/contexts/AuthContext";
 
 interface ProductFormData {
   name: string;
@@ -32,6 +33,22 @@ const Products = () => {
   const { toast } = useToast();
   const queryClient = useQueryClient();
   const { products, categories, isLoading: isDataLoading, refreshProducts } = useData();
+  const { userRole, loading: authLoading } = useAuth();
+
+  // Trigger products loading when admin user is detected
+  useEffect(() => {
+    console.log('🔍 Products state:', {
+      userRole,
+      productsLength: products.length,
+      isDataLoading,
+      categoriesLength: categories.length
+    });
+
+    if (userRole === 'admin' && products.length === 0 && !isDataLoading) {
+      console.log('🔄 Admin detected, triggering products refresh...');
+      refreshProducts(true); // Force refresh for admin users
+    }
+  }, [userRole, products.length, isDataLoading, refreshProducts, categories.length]);
   const [searchTerm, setSearchTerm] = useState("");
   const [currentPage, setCurrentPage] = useState(1);
   const [showReviewedOnly, setShowReviewedOnly] = useState<null | boolean>(null);
@@ -48,6 +65,35 @@ const Products = () => {
     is_18_plus: false,
   });
 
+  // Show loading while checking auth status or loading products
+  if (authLoading || (userRole === 'admin' && products.length === 0 && isDataLoading)) {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
+          <p>{authLoading ? 'Verificando autenticação...' : 'Carregando produtos...'}</p>
+        </div>
+      </div>
+    );
+  }
+
+  // Check if user has admin access
+  if (userRole !== 'admin') {
+    return (
+      <div className="flex items-center justify-center h-64">
+        <div className="text-center">
+          <Package size={64} className="mx-auto text-gray-400 mb-4" />
+          <h3 className="text-lg font-medium text-gray-900 mb-2">
+            Acesso Restrito
+          </h3>
+          <p className="text-gray-500">
+            Esta página é apenas para administradores.
+          </p>
+        </div>
+      </div>
+    );
+  }
+
   // Filter products based on search term and toggles
   const filteredProducts = products.filter((product) => {
     const matchesSearch = 
@@ -78,7 +124,7 @@ const Products = () => {
       return response.data;
     },
     onSuccess: () => {
-      refreshProducts();
+      refreshProducts(true); // Force refresh after creating product
       setIsCreateDialogOpen(false);
       setFormData({
         name: "",
@@ -109,7 +155,7 @@ const Products = () => {
       return response.data;
     },
     onSuccess: () => {
-      refreshProducts();
+      refreshProducts(true); // Force refresh after updating product
       setIsEditDialogOpen(false);
       setFormData({
         name: "",
@@ -142,7 +188,7 @@ const Products = () => {
       return response.data;
     },
     onSuccess: () => {
-      refreshProducts();
+      refreshProducts(true); // Force refresh after updating product image
       toast({
         title: "Success",
         description: "Product image updated successfully",
@@ -296,7 +342,7 @@ const Products = () => {
           </p>
         </div>
         <div className="flex items-center gap-4">
-          <Button variant="outline" onClick={() => refreshProducts()}>
+          <Button variant="outline" onClick={() => refreshProducts(true)}>
             <RefreshCw className="mr-2 h-4 w-4" />
             Atualizar
           </Button>
diff --git a/src/pages/Users.tsx b/src/pages/Users.tsx
new file mode 100644
index 0000000..7db803b
--- /dev/null
+++ b/src/pages/Users.tsx
@@ -0,0 +1,449 @@
+import React, { useState, useEffect } from "react";
+import { useMutation } from "@tanstack/react-query";
+import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
+import { Button } from "@/components/ui/button";
+import { Input } from "@/components/ui/input";
+import {
+  Dialog,
+  DialogContent,
+  DialogDescription,
+  DialogHeader,
+  DialogTitle,
+  DialogFooter,
+} from "@/components/ui/dialog";
+import { Label } from "@/components/ui/label";
+import { userService } from "@/services/api";
+import {
+  CreateUserRequest,
+  CheckEmailExistsResponse,
+  User
+} from "@/types/api";
+import { toast } from "sonner";
+import {
+  Users,
+  UserPlus,
+  Loader2
+} from "lucide-react";
+
+const UsersPage = () => {
+  const [createModalOpen, setCreateModalOpen] = useState(false);
+  const [formData, setFormData] = useState<CreateUserRequest>({
+    name: "",
+    email: "",
+    cpf: "",
+    phone_numbers: [""]
+  });
+  const [emailError, setEmailError] = useState("");
+  const [isCheckingEmail, setIsCheckingEmail] = useState(false);
+  const [emailDebounceTimer, setEmailDebounceTimer] = useState<NodeJS.Timeout | null>(null);
+
+  // Search for user status management
+  const [searchQuery, setSearchQuery] = useState("");
+  const [searchResults, setSearchResults] = useState<User[]>([]);
+  const [isSearching, setIsSearching] = useState(false);
+
+  // Cleanup timer on unmount
+  useEffect(() => {
+    return () => {
+      if (emailDebounceTimer) {
+        clearTimeout(emailDebounceTimer);
+      }
+    };
+  }, [emailDebounceTimer]);
+
+  // Create user mutation
+  const createUserMutation = useMutation({
+    mutationFn: (userData: CreateUserRequest) => userService.createUser(userData),
+    onSuccess: () => {
+      toast.success("Usuário criado com sucesso!");
+      setCreateModalOpen(false);
+      resetForm();
+    },
+    onError: (error: any) => {
+      toast.error(error.response?.data?.message || "Erro ao criar usuário");
+    },
+  });
+
+  // Update user status mutation
+  const updateStatusMutation = useMutation({
+    mutationFn: ({ userExternalId, isActive }: { userExternalId: string; isActive: boolean }) =>
+      userService.updateUserStatus(userExternalId, isActive),
+    onSuccess: (_, { isActive }) => {
+      toast.success(`Usuário ${isActive ? 'ativado' : 'desativado'} com sucesso!`);
+      // Refresh search results to show updated status
+      if (searchQuery) {
+        handleUserSearch();
+      }
+    },
+    onError: (error: any) => {
+      toast.error(error.response?.data?.message || "Erro ao atualizar status do usuário");
+    },
+  });
+
+  // Search for users (for status management)
+  const handleUserSearch = async () => {
+    if (!searchQuery.trim()) {
+      setSearchResults([]);
+      return;
+    }
+
+    setIsSearching(true);
+    try {
+      const response = await userService.searchUsers(searchQuery);
+      setSearchResults(response.data.data || []);
+    } catch (error) {
+      console.error("Erro ao buscar usuários:", error);
+      toast.error("Erro ao buscar usuários");
+      setSearchResults([]);
+    } finally {
+      setIsSearching(false);
+    }
+  };
+
+  // Check email exists
+  const checkEmailExists = async (email: string) => {
+    // Don't check if email is empty or invalid
+    if (!email || !email.includes('@') || email.length < 5) {
+      setEmailError("");
+      setIsCheckingEmail(false);
+      return;
+    }
+
+    setIsCheckingEmail(true);
+    setEmailError(""); // Clear previous errors
+
+    try {
+      const response = await userService.checkEmailExists(email);
+      const data = response.data as CheckEmailExistsResponse;
+
+      if (data.data === true) {
+        setEmailError("Este email já está em uso");
+      } else {
+        setEmailError("");
+      }
+    } catch (error: any) {
+      console.error("Erro ao verificar email:", error);
+      // Don't show error to user for API failures during email check
+      // Just log it and allow them to proceed
+      setEmailError("");
+    } finally {
+      setIsCheckingEmail(false);
+    }
+  };
+
+  // Handle form input changes
+  const handleInputChange = (field: keyof CreateUserRequest, value: string) => {
+    if (field === 'phone_numbers') {
+      setFormData(prev => ({ ...prev, phone_numbers: [value] }));
+    } else {
+      setFormData(prev => ({ ...prev, [field]: value }));
+    }
+
+    if (field === 'email') {
+      // Clear previous timer
+      if (emailDebounceTimer) {
+        clearTimeout(emailDebounceTimer);
+      }
+
+      // Clear previous error when user starts typing
+      setEmailError("");
+      setIsCheckingEmail(false);
+
+      // Set new timer for debounced email check
+      const timeoutId = setTimeout(() => {
+        checkEmailExists(value);
+      }, 800); // 800ms debounce
+
+      setEmailDebounceTimer(timeoutId);
+    }
+  };
+
+  // Reset form
+  const resetForm = () => {
+    // Clear email debounce timer
+    if (emailDebounceTimer) {
+      clearTimeout(emailDebounceTimer);
+      setEmailDebounceTimer(null);
+    }
+
+    setFormData({
+      name: "",
+      email: "",
+      cpf: "",
+      phone_numbers: [""]
+    });
+    setEmailError("");
+    setIsCheckingEmail(false);
+  };
+
+  // Handle user status toggle
+  const handleStatusToggle = (user: User) => {
+    const newStatus = !user.is_active;
+    const action = newStatus ? 'ativar' : 'desativar';
+
+    if (window.confirm(`Tem certeza que deseja ${action} o usuário ${user.name}?`)) {
+      updateStatusMutation.mutate({
+        userExternalId: user.external_id,
+        isActive: newStatus
+      });
+    }
+  };
+
+  // Handle form submission
+  const handleSubmit = (e: React.FormEvent) => {
+    e.preventDefault();
+
+    if (emailError) {
+      toast.error("Corrija os erros no formulário antes de continuar");
+      return;
+    }
+
+    if (!formData.name || !formData.email || !formData.cpf || !formData.phone_numbers[0]) {
+      toast.error("Preencha todos os campos obrigatórios");
+      return;
+    }
+
+    createUserMutation.mutate(formData);
+  };
+
+  // Handle search form submission
+  const handleSearchSubmit = (e: React.FormEvent) => {
+    e.preventDefault();
+    handleUserSearch();
+  };
+
+  return (
+    <div className="space-y-6">
+      {/* Header */}
+      <div className="flex justify-between items-center">
+        <div>
+          <h2 className="text-2xl font-bold tracking-tight">Gerenciamento de Usuários</h2>
+          <p className="text-muted-foreground">
+            Crie novos usuários e gerencie status de usuários existentes
+          </p>
+        </div>
+        <Button onClick={() => setCreateModalOpen(true)}>
+          <UserPlus className="mr-2" size={16} />
+          Criar Usuário
+        </Button>
+      </div>
+
+      {/* Create User Card */}
+      <Card>
+        <CardHeader>
+          <CardTitle className="flex items-center">
+            <UserPlus className="mr-2" size={20} />
+            Criar Novo Usuário
+          </CardTitle>
+        </CardHeader>
+        <CardContent>
+          <p className="text-muted-foreground mb-4">
+            Clique no botão "Criar Usuário" acima para adicionar um novo usuário ao sistema.
+          </p>
+        </CardContent>
+      </Card>
+
+      {/* User Status Management */}
+      <Card>
+        <CardHeader>
+          <CardTitle className="flex items-center">
+            <Users className="mr-2" size={20} />
+            Gerenciar Status de Usuários
+          </CardTitle>
+        </CardHeader>
+        <CardContent>
+          <form onSubmit={handleSearchSubmit} className="flex gap-4 mb-6">
+            <div className="flex-1">
+              <Input
+                placeholder="Buscar usuário por nome, email ou CPF..."
+                value={searchQuery}
+                onChange={(e) => setSearchQuery(e.target.value)}
+              />
+            </div>
+            <Button type="submit" disabled={isSearching}>
+              {isSearching ? (
+                <>
+                  <Loader2 size={16} className="mr-2 animate-spin" />
+                  Buscando...
+                </>
+              ) : (
+                "Buscar"
+              )}
+            </Button>
+          </form>
+
+          {/* Search Results */}
+          {searchResults.length > 0 ? (
+            <div className="space-y-4">
+              <h3 className="text-lg font-medium">Resultados da Busca</h3>
+              {searchResults.map((user) => (
+                <div
+                  key={user.external_id}
+                  className="flex items-center justify-between p-4 border rounded-lg"
+                >
+                  <div className="flex-1">
+                    <div className="flex items-center space-x-2">
+                      <h4 className="font-medium">{user.name}</h4>
+                      <span className={`px-2 py-1 text-xs rounded-full ${
+                        user.is_active
+                          ? 'bg-green-100 text-green-800'
+                          : 'bg-gray-100 text-gray-800'
+                      }`}>
+                        {user.is_active ? "Ativo" : "Inativo"}
+                      </span>
+                    </div>
+                    <p className="text-sm text-muted-foreground">{user.email}</p>
+                    {user.cpf && (
+                      <p className="text-sm text-muted-foreground">
+                        CPF: {user.cpf}
+                      </p>
+                    )}
+                  </div>
+                  <Button
+                    variant={user.is_active ? "destructive" : "default"}
+                    size="sm"
+                    onClick={() => handleStatusToggle(user)}
+                    disabled={updateStatusMutation.isPending}
+                  >
+                    {updateStatusMutation.isPending ? (
+                      <Loader2 size={14} className="animate-spin" />
+                    ) : user.is_active ? (
+                      "Desativar"
+                    ) : (
+                      "Ativar"
+                    )}
+                  </Button>
+                </div>
+              ))}
+            </div>
+          ) : searchQuery && !isSearching ? (
+            <div className="text-center py-8">
+              <Users size={48} className="mx-auto text-gray-400 mb-4" />
+              <p className="text-gray-500 mb-2">Nenhum usuário encontrado</p>
+              <p className="text-sm text-gray-400">
+                Tente ajustar os termos de busca
+              </p>
+            </div>
+          ) : !searchQuery ? (
+            <div className="text-center py-8">
+              <Users size={48} className="mx-auto text-gray-400 mb-4" />
+              <p className="text-gray-500 mb-2">Digite um termo para buscar usuários</p>
+              <p className="text-sm text-gray-400">
+                Busque por nome, email ou CPF para gerenciar o status dos usuários
+              </p>
+            </div>
+          ) : null}
+        </CardContent>
+      </Card>
+
+      {/* Create User Modal */}
+      <Dialog open={createModalOpen} onOpenChange={setCreateModalOpen}>
+        <DialogContent className="sm:max-w-md">
+          <DialogHeader>
+            <DialogTitle>Criar Novo Usuário</DialogTitle>
+            <DialogDescription>
+              Preencha os dados para criar um novo usuário no sistema.
+            </DialogDescription>
+          </DialogHeader>
+          
+          <form onSubmit={handleSubmit} className="space-y-4">
+            <div>
+              <Label htmlFor="name">Nome *</Label>
+              <Input
+                id="name"
+                value={formData.name}
+                onChange={(e) => handleInputChange('name', e.target.value)}
+                placeholder="Nome completo"
+                required
+              />
+            </div>
+
+            <div>
+              <Label htmlFor="email">Email *</Label>
+              <div className="relative">
+                <Input
+                  id="email"
+                  type="email"
+                  value={formData.email}
+                  onChange={(e) => handleInputChange('email', e.target.value)}
+                  placeholder="<EMAIL>"
+                  className={emailError ? "border-red-500 focus:border-red-500 focus:ring-red-500" : ""}
+                  required
+                />
+                {isCheckingEmail && (
+                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin text-gray-400" />
+                )}
+              </div>
+              {emailError && (
+                <p className="text-sm text-red-500 mt-1 flex items-center">
+                  <span className="mr-1">⚠️</span>
+                  {emailError}
+                </p>
+              )}
+            </div>
+
+            <div>
+              <Label htmlFor="cpf">CPF *</Label>
+              <Input
+                id="cpf"
+                value={formData.cpf}
+                onChange={(e) => handleInputChange('cpf', e.target.value)}
+                placeholder="000.000.000-00"
+                required
+              />
+            </div>
+
+            <div>
+              <Label htmlFor="phone">Telefone *</Label>
+              <Input
+                id="phone"
+                value={formData.phone_numbers[0] || ""}
+                onChange={(e) => handleInputChange('phone_numbers', e.target.value)}
+                placeholder="+5511999999999"
+                required
+              />
+            </div>
+
+            <DialogFooter>
+              <Button
+                type="button"
+                variant="outline"
+                onClick={() => {
+                  setCreateModalOpen(false);
+                  resetForm();
+                }}
+                disabled={createUserMutation.isPending}
+              >
+                Cancelar
+              </Button>
+              <Button
+                type="submit"
+                disabled={createUserMutation.isPending || !!emailError || isCheckingEmail}
+                className={emailError ? "opacity-50 cursor-not-allowed" : ""}
+              >
+                {createUserMutation.isPending ? (
+                  <>
+                    <Loader2 size={16} className="mr-2 animate-spin" />
+                    Criando...
+                  </>
+                ) : isCheckingEmail ? (
+                  <>
+                    <Loader2 size={16} className="mr-2 animate-spin" />
+                    Verificando email...
+                  </>
+                ) : (
+                  <>
+                    <UserPlus size={16} className="mr-2" />
+                    Criar Usuário
+                  </>
+                )}
+              </Button>
+            </DialogFooter>
+          </form>
+        </DialogContent>
+      </Dialog>
+    </div>
+  );
+};
+
+export default UsersPage;
diff --git a/src/services/api.ts b/src/services/api.ts
index 2c39870..d1f8128 100644
--- a/src/services/api.ts
+++ b/src/services/api.ts
@@ -184,11 +184,15 @@ export const authService = {
 // Serviços de empresas
 export const companyService = {
   getCompanies: async () => {
-    return api.get("/v1/company");
+    return api.get("/v1/company/all");
   },
-  
+
+  getMyCompanies: async () => {
+    return api.get("/v1/company/my-companies");
+  },
+
   getCompany: async (externalID: string) => {
-    return api.get(`/v1/company/${externalID}`);
+    return api.get(`/v1/company/${externalID}/details`);
   },
   
   createCompany: async (formData: FormData) => {
@@ -250,6 +254,12 @@ export const companyService = {
       }
     });
   },
+
+  updateCompanyStatus: async (externalID: string, activate: boolean) => {
+    return api.post(`/v1/company/${externalID}/status`, {
+      activate
+    });
+  },
 };
 
 // Serviços de produtos
@@ -352,3 +362,40 @@ export const orderService = {
     return api.put(`/v1/company/invoice/${orderId}/status`, data);
   }
 };
+
+// Serviço de usuários
+export const userService = {
+  // Search users by query (for owner assignment and status management)
+  searchUsers: async (query: string) => {
+    return api.get(`/v1/user/search/${encodeURIComponent(query)}`);
+  },
+
+  // Create new user
+  createUser: async (userData: {
+    name: string;
+    email: string;
+    cpf: string;
+    phone_numbers: string[];
+  }) => {
+    return api.post('/v1/user', userData);
+  },
+
+  // Check if email exists
+  checkEmailExists: async (email: string) => {
+    return api.post('/v1/user/exists/email', { email });
+  },
+
+  // Update user status (activate/deactivate)
+  updateUserStatus: async (userExternalId: string, isActive: boolean) => {
+    return api.patch(`/v1/user/${userExternalId}/status`, {
+      is_active: isActive
+    });
+  },
+
+  // Link user to company (for owner assignment)
+  linkUserToCompany: async (companyExternalId: string, userExternalId: string) => {
+    return api.put(`/v1/company/${companyExternalId}/owner`, {
+      user_external_id: userExternalId
+    });
+  }
+};
diff --git a/src/types/api.ts b/src/types/api.ts
index 4af0708..5109228 100644
--- a/src/types/api.ts
+++ b/src/types/api.ts
@@ -65,21 +65,28 @@ export interface Company {
   id: string;
   external_id: string;
   name: string;
-  document: string;
-  email: string;
+  document?: string;
+  email?: string;
   phone_numbers: string[];
-  status: string;
+  status?: string;
   created_at: string;
   updated_at: string;
   bio?: string;
   cnpj?: string;
   picture?: string;
   pix_key?: string;
-  address?: Address;
+  address?: Address; // Legacy single address field
+  addresses?: Address[]; // New addresses array from /details endpoint
   products?: Product[];
   is_active: boolean;
   delivery_modes?: string[];
   shipping_fee?: number;
+  owner?: User;
+  subscription_id?: number | null;
+  rating?: number;
+  affiliate_balance?: number;
+  commission_rate?: number;
+  cashback_rate?: number;
 }
 
 export interface CreateCompanyRequest {
@@ -114,6 +121,16 @@ export interface GetActiveCompanySuccessResponse {
   data: Company;
 }
 
+export interface GetMyCompaniesResponse {
+  company_external_ids: string[];
+  dashboard_url: string;
+  owner_external_id: string;
+}
+
+export interface GetMyCompaniesSuccessResponse {
+  data: GetMyCompaniesResponse;
+}
+
 export interface CreateSubAccountRequest {
   companyId: string;
   name: string;
@@ -254,6 +271,7 @@ export interface Order {
   updated_at: string;
   finished_at?: string; // when order is completed
   reason?: string; // for status changes
+  company_external_id: string; // Company that this order belongs to
 }
 
 export interface ListOrdersSuccessResponse {
@@ -264,6 +282,70 @@ export interface ListOrdersSuccessResponse {
   totalPages: number;
 }
 
+export interface User {
+  external_id: string;
+  name: string;
+  email: string;
+  phone_numbers: string[];
+  cpf?: string; // CPF field from API
+  document?: string; // Alternative CPF field (for compatibility)
+  cashback_value?: number;
+  subscription_id?: number;
+  is_active?: boolean;
+  is_deleted?: boolean;
+  created_at: string;
+  updated_at: string;
+}
+
+export interface SearchUsersSuccessResponse {
+  data: User[];
+}
+
+export interface LinkUserToCompanyRequest {
+  user_external_id: string;
+}
+
+export interface LinkUserToCompanySuccessResponse {
+  message: string;
+}
+
+export interface UpdateCompanyStatusRequest {
+  activate: boolean;
+}
+
+export interface UpdateCompanyStatusSuccessResponse {
+  message: string;
+}
+
+// User Management Types
+export interface CreateUserRequest {
+  name: string;
+  email: string;
+  cpf: string;
+  phone_numbers: string[];
+}
+
+export interface CreateUserSuccessResponse {
+  data: User;
+  message: string;
+}
+
+export interface CheckEmailExistsRequest {
+  email: string;
+}
+
+export interface CheckEmailExistsResponse {
+  data: boolean;
+}
+
+export interface UpdateUserStatusRequest {
+  is_active: boolean;
+}
+
+export interface UpdateUserStatusSuccessResponse {
+  message: string;
+}
+
 export interface UpdateOrderStatusRequest {
   status: "processing" | "preparing" | "ready" | "delivering" | "completed" | "canceled";
   reason?: string;
diff --git a/src/utils/formatters.ts b/src/utils/formatters.ts
new file mode 100644
index 0000000..babab44
--- /dev/null
+++ b/src/utils/formatters.ts
@@ -0,0 +1,106 @@
+/**
+ * Utility functions for formatting data
+ */
+
+/**
+ * Format CPF with mask (000.000.000-00)
+ */
+export const formatCPF = (cpf: string): string => {
+  if (!cpf) return '';
+  
+  // Remove all non-numeric characters
+  const cleanCPF = cpf.replace(/\D/g, '');
+  
+  // Check if it has the correct length
+  if (cleanCPF.length !== 11) return cpf;
+  
+  // Apply the mask
+  return cleanCPF.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
+};
+
+/**
+ * Format CNPJ with mask (00.000.000/0000-00)
+ */
+export const formatCNPJ = (cnpj: string): string => {
+  if (!cnpj) return '';
+  
+  // Remove all non-numeric characters
+  const cleanCNPJ = cnpj.replace(/\D/g, '');
+  
+  // Check if it has the correct length
+  if (cleanCNPJ.length !== 14) return cnpj;
+  
+  // Apply the mask
+  return cleanCNPJ.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5');
+};
+
+/**
+ * Format phone number with mask (+55 11 99999-9999)
+ */
+export const formatPhone = (phone: string): string => {
+  if (!phone) return '';
+  
+  // Remove all non-numeric characters
+  const cleanPhone = phone.replace(/\D/g, '');
+  
+  // Check if it starts with country code
+  if (cleanPhone.startsWith('55') && cleanPhone.length === 13) {
+    // Format: +55 11 99999-9999
+    return cleanPhone.replace(/(\d{2})(\d{2})(\d{5})(\d{4})/, '+$1 $2 $3-$4');
+  } else if (cleanPhone.length === 11) {
+    // Format: (11) 99999-9999
+    return cleanPhone.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
+  } else if (cleanPhone.length === 10) {
+    // Format: (11) 9999-9999
+    return cleanPhone.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
+  }
+  
+  return phone;
+};
+
+/**
+ * Format currency in Brazilian Real (R$ 1.234,56)
+ */
+export const formatCurrency = (value: number): string => {
+  return new Intl.NumberFormat('pt-BR', {
+    style: 'currency',
+    currency: 'BRL',
+  }).format(value);
+};
+
+/**
+ * Format date in Brazilian format (dd/mm/yyyy)
+ */
+export const formatDate = (date: string | Date): string => {
+  const dateObj = typeof date === 'string' ? new Date(date) : date;
+  return dateObj.toLocaleDateString('pt-BR');
+};
+
+/**
+ * Format date and time in Brazilian format (dd/mm/yyyy HH:mm)
+ */
+export const formatDateTime = (date: string | Date): string => {
+  const dateObj = typeof date === 'string' ? new Date(date) : date;
+  return dateObj.toLocaleString('pt-BR');
+};
+
+/**
+ * Remove formatting from CPF (keep only numbers)
+ */
+export const cleanCPF = (cpf: string): string => {
+  return cpf.replace(/\D/g, '');
+};
+
+/**
+ * Remove formatting from CNPJ (keep only numbers)
+ */
+export const cleanCNPJ = (cnpj: string): string => {
+  return cnpj.replace(/\D/g, '');
+};
+
+/**
+ * Remove formatting from phone (keep only numbers)
+ */
+export const cleanPhone = (phone: string): string => {
+  return phone.replace(/\D/g, '');
+};
diff --git a/swagger.yaml b/swagger.yaml
new file mode 100644
index 0000000..c200620
--- /dev/null
+++ b/swagger.yaml
@@ -0,0 +1,3848 @@
+definitions:
+  common.ErrorResponse:
+    properties:
+      code:
+        example: "001"
+        type: string
+      details: {}
+      message:
+        type: string
+    type: object
+  common.SuccessResponse-array_handlers_SearchResponse:
+    properties:
+      data:
+        items:
+          $ref: '#/definitions/handlers.SearchResponse'
+        type: array
+    type: object
+  custom_models.AddressParams:
+    properties:
+      city:
+        example: São Paulo
+        type: string
+      complement:
+        example: Apto 101
+        type: string
+      external_id:
+        example: "123456"
+        type: string
+      is_default:
+        example: true
+        type: boolean
+      location:
+        $ref: '#/definitions/custom_models.Location'
+      name:
+        example: Home
+        type: string
+      neighborhood:
+        example: Downtown
+        type: string
+      number:
+        example: "123"
+        type: string
+      state:
+        example: SP
+        type: string
+      street:
+        example: Rua das Flores
+        type: string
+      zip_code:
+        example: "12345678"
+        type: string
+    type: object
+  custom_models.Category:
+    properties:
+      external_id:
+        example: "123456"
+        type: string
+      image:
+        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
+        type: string
+      name:
+        example: Bebidas
+        type: string
+    type: object
+  custom_models.Location:
+    properties:
+      latitude:
+        example: -5.9015168
+        type: number
+      longitude:
+        example: -35.2485376
+        type: number
+    type: object
+  custom_models.Product:
+    properties:
+      brand:
+        example: Taeq
+        type: string
+      categories:
+        items:
+          $ref: '#/definitions/custom_models.Category'
+        type: array
+      description:
+        example: água de coco integral taeq 200ml - SEM GLÚTEN - 100% NATURAL
+        type: string
+      discount:
+        example: 10
+        type: integer
+      ean:
+        example: "7895000292035"
+        type: string
+      external_id:
+        example: "123456"
+        type: string
+      id:
+        example: 1
+        type: integer
+      image:
+        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
+        type: string
+      is_18_plus:
+        example: false
+        type: boolean
+      is_active:
+        example: true
+        type: boolean
+      name:
+        example: água De Coco Taeq
+        type: string
+      price:
+        example: 1000
+        type: integer
+      quantity:
+        example: 1
+        type: integer
+      stock:
+        example: 10
+        type: integer
+    type: object
+  custom_models.ProductList:
+    properties:
+      external_id:
+        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
+        type: string
+      quantity:
+        example: 1
+        type: integer
+    type: object
+  handlers.ActivateCompanyResponse:
+    properties:
+      external_id:
+        type: string
+      is_active:
+        type: boolean
+      name:
+        type: string
+    type: object
+  handlers.ActivateCompanySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.ActivateCompanyResponse'
+    type: object
+  handlers.AddProductsToCompanyRequest:
+    properties:
+      discount:
+        example: 10
+        type: integer
+      price:
+        example: 1000
+        type: integer
+      product_external_id:
+        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
+        type: string
+      stock:
+        example: 10
+        type: integer
+    type: object
+  handlers.ApplyCouponRequest:
+    properties:
+      company_external_id:
+        type: string
+      coupon_code:
+        type: string
+      order_value:
+        type: integer
+    required:
+    - company_external_id
+    - coupon_code
+    - order_value
+    type: object
+  handlers.ApplyCouponResponse:
+    properties:
+      discount_value:
+        type: integer
+      final_value:
+        type: integer
+      message:
+        type: string
+    type: object
+  handlers.AssignRoleToUserRequest:
+    properties:
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+      role:
+        enum:
+        - admin
+        - partner
+        - user
+        example: admin
+        type: string
+    required:
+    - external_id
+    - role
+    type: object
+  handlers.AssignRoleToUserSuccessResponse:
+    type: object
+  handlers.ChargeEvent:
+    properties:
+      account:
+        properties:
+          environment:
+            type: string
+        type: object
+      charge:
+        properties:
+          additionalInfo:
+            items: {}
+            type: array
+          brCode:
+            type: string
+          comment:
+            type: string
+          correlationID:
+            type: string
+          createdAt:
+            type: string
+          customer:
+            properties:
+              correlationID:
+                type: string
+              email:
+                type: string
+              name:
+                type: string
+            type: object
+          discount:
+            type: integer
+          expiresDate:
+            type: string
+          expiresIn:
+            type: integer
+          fee:
+            type: integer
+          globalID:
+            type: string
+          identifier:
+            type: string
+          paidAt:
+            type: string
+          payer:
+            properties:
+              address:
+                properties:
+                  city:
+                    type: string
+                  complement:
+                    type: string
+                  neighborhood:
+                    type: string
+                  number:
+                    type: string
+                  state:
+                    type: string
+                  street:
+                    type: string
+                  zipcode:
+                    type: string
+                type: object
+              correlationID:
+                type: string
+              email:
+                type: string
+              name:
+                type: string
+              phone:
+                type: string
+              taxID:
+                properties:
+                  taxID:
+                    type: string
+                  type:
+                    type: string
+                type: object
+            type: object
+          paymentLinkID:
+            type: string
+          paymentLinkUrl:
+            type: string
+          paymentMethods:
+            properties:
+              pix:
+                properties:
+                  brCode:
+                    type: string
+                  fee:
+                    type: integer
+                  identifier:
+                    type: string
+                  method:
+                    type: string
+                  qrCodeImage:
+                    type: string
+                  status:
+                    type: string
+                  transactionID:
+                    type: string
+                  txId:
+                    type: string
+                  value:
+                    type: integer
+                type: object
+            type: object
+          pixKey:
+            type: string
+          qrCodeImage:
+            type: string
+          splits:
+            items:
+              properties:
+                pixKey:
+                  type: string
+                pixKeyType:
+                  type: string
+                sourceAccount:
+                  type: string
+                splitType:
+                  type: string
+                value:
+                  type: integer
+              type: object
+            type: array
+          status:
+            type: string
+          transactionID:
+            type: string
+          type:
+            type: string
+          updatedAt:
+            type: string
+          value:
+            type: integer
+          valueWithDiscount:
+            type: integer
+        type: object
+      company:
+        properties:
+          id:
+            type: string
+          name:
+            type: string
+          taxID:
+            type: string
+        type: object
+      event:
+        type: string
+      evento:
+        example: teste_webhook
+        type: string
+      pix:
+        properties:
+          charge:
+            properties:
+              additionalInfo:
+                items: {}
+                type: array
+              brCode:
+                type: string
+              comment:
+                type: string
+              correlationID:
+                type: string
+              createdAt:
+                type: string
+              customer:
+                properties:
+                  correlationID:
+                    type: string
+                  email:
+                    type: string
+                  name:
+                    type: string
+                type: object
+              discount:
+                type: integer
+              expiresDate:
+                type: string
+              expiresIn:
+                type: integer
+              fee:
+                type: integer
+              globalID:
+                type: string
+              identifier:
+                type: string
+              paidAt:
+                type: string
+              payer:
+                properties:
+                  address:
+                    properties:
+                      city:
+                        type: string
+                      complement:
+                        type: string
+                      neighborhood:
+                        type: string
+                      number:
+                        type: string
+                      state:
+                        type: string
+                      street:
+                        type: string
+                      zipcode:
+                        type: string
+                    type: object
+                  correlationID:
+                    type: string
+                  email:
+                    type: string
+                  name:
+                    type: string
+                  phone:
+                    type: string
+                  taxID:
+                    properties:
+                      taxID:
+                        type: string
+                      type:
+                        type: string
+                    type: object
+                type: object
+              paymentLinkID:
+                type: string
+              paymentLinkUrl:
+                type: string
+              pixKey:
+                type: string
+              qrCodeImage:
+                type: string
+              splits:
+                items:
+                  properties:
+                    pixKey:
+                      type: string
+                    pixKeyType:
+                      type: string
+                    sourceAccount:
+                      type: string
+                    splitType:
+                      type: string
+                    value:
+                      type: integer
+                  type: object
+                type: array
+              status:
+                type: string
+              transactionID:
+                type: string
+              type:
+                type: string
+              updatedAt:
+                type: string
+              value:
+                type: integer
+              valueWithDiscount:
+                type: integer
+            type: object
+          createdAt:
+            type: string
+          customer:
+            properties:
+              correlationID:
+                type: string
+              email:
+                type: string
+              name:
+                type: string
+            type: object
+          endToEndId:
+            type: string
+          globalID:
+            type: string
+          infoPagador:
+            type: string
+          payer:
+            properties:
+              address:
+                properties:
+                  _id:
+                    type: string
+                  city:
+                    type: string
+                  complement:
+                    type: string
+                  country:
+                    type: string
+                  location:
+                    properties:
+                      coordinates:
+                        items: {}
+                        type: array
+                    type: object
+                  neighborhood:
+                    type: string
+                  number:
+                    type: string
+                  state:
+                    type: string
+                  street:
+                    type: string
+                  zipcode:
+                    type: string
+                type: object
+              correlationID:
+                type: string
+              email:
+                type: string
+              name:
+                type: string
+              phone:
+                type: string
+              taxID:
+                properties:
+                  taxID:
+                    type: string
+                  type:
+                    type: string
+                type: object
+            type: object
+          time:
+            type: string
+          transactionID:
+            type: string
+          type:
+            type: string
+          value:
+            type: integer
+        type: object
+    type: object
+  handlers.CheckIfEmailExistsRequest:
+    properties:
+      email:
+        example: <EMAIL>
+        type: string
+    required:
+    - email
+    type: object
+  handlers.CheckIfEmailExistsSuccessResponse:
+    properties:
+      data:
+        type: boolean
+    type: object
+  handlers.CheckoutRequest:
+    properties:
+      address_external_id:
+        type: string
+      company:
+        type: string
+      coupon_code:
+        type: string
+      credit_card:
+        $ref: '#/definitions/handlers.CreditCard'
+      delivery_mode:
+        description: |-
+          TODO: adicionar validação de delivery mode
+          DeliveryMode      string             `json:"delivery_mode" validate:"required,oneof=delivery pickup"`
+        type: string
+      payment_method:
+        enum:
+        - pix
+        - credit_card
+        type: string
+      products:
+        items:
+          $ref: '#/definitions/handlers.ProductsCheckout'
+        type: array
+    required:
+    - address_external_id
+    - company
+    - payment_method
+    - products
+    type: object
+  handlers.CheckoutSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/woovi.PixSuccessResponse'
+    type: object
+  handlers.Coupon:
+    properties:
+      code:
+        type: string
+      created_at:
+        type: string
+      expires_at:
+        type: string
+      external_id:
+        type: string
+      is_active:
+        type: boolean
+      min_order_value:
+        type: integer
+      owner_external_id:
+        type: string
+      owner_type:
+        type: string
+      quantity:
+        type: integer
+      type:
+        type: string
+      updated_at:
+        type: string
+      value:
+        type: integer
+    type: object
+  handlers.CreateCategoryResponse:
+    properties:
+      external_id:
+        example: "123456"
+        type: string
+    type: object
+  handlers.CreateCategorySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.CreateCategoryResponse'
+    type: object
+  handlers.CreateCompanyResponse:
+    properties:
+      external_id:
+        type: string
+    type: object
+  handlers.CreateCompanySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.CreateCompanyResponse'
+    type: object
+  handlers.CreateCouponRequest:
+    properties:
+      code:
+        type: string
+      expires_at:
+        type: string
+      min_order_value:
+        type: number
+      owner_type:
+        enum:
+        - company
+        - admin
+        type: string
+      quantity:
+        type: integer
+      type:
+        enum:
+        - percentage
+        - fixed
+        type: string
+      value:
+        type: number
+    required:
+    - code
+    - expires_at
+    - min_order_value
+    - owner_type
+    - quantity
+    - type
+    - value
+    type: object
+  handlers.CreateProductSuccessResponse:
+    properties:
+      data:
+        type: string
+    type: object
+  handlers.CreateProductsListRequest:
+    properties:
+      icon_url:
+        example: https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png
+        type: string
+      is_public:
+        example: false
+        type: boolean
+      name:
+        example: Compras do mês
+        type: string
+      products:
+        items:
+          $ref: '#/definitions/custom_models.ProductList'
+        type: array
+    required:
+    - icon_url
+    - is_public
+    - name
+    type: object
+  handlers.CreateProductsListResponse:
+    properties:
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+    type: object
+  handlers.CreateProductsListSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.CreateProductsListResponse'
+    type: object
+  handlers.CreateSubAccountRequest:
+    properties:
+      name:
+        type: string
+      pix_key:
+        type: string
+    required:
+    - name
+    - pix_key
+    type: object
+  handlers.CreateUserAddressResponse:
+    properties:
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+    type: object
+  handlers.CreateUserAddressSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.CreateUserAddressResponse'
+    type: object
+  handlers.CreateUserRequest:
+    properties:
+      cpf:
+        example: "***********"
+        type: string
+      email:
+        example: <EMAIL>
+        type: string
+      name:
+        example: Vini
+        type: string
+      phone_numbers:
+        example:
+        - "***********"
+        items:
+          type: string
+        type: array
+    required:
+    - cpf
+    - email
+    - name
+    - phone_numbers
+    type: object
+  handlers.CreateUserResponse:
+    properties:
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+    type: object
+  handlers.CreateUserSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.CreateUserResponse'
+    type: object
+  handlers.CreditCard:
+    properties:
+      billing_address:
+        $ref: '#/definitions/custom_models.AddressParams'
+      card_holder_birth:
+        type: string
+      card_holder_cpf:
+        type: string
+      card_holder_name:
+        type: string
+      card_number:
+        type: string
+      cvv:
+        type: string
+      expiration_date:
+        type: string
+      installments:
+        type: integer
+    type: object
+  handlers.EmptySuccessResponse:
+    type: object
+  handlers.GetActiveCategoriesPaginatedResponse:
+    properties:
+      data:
+        items:
+          $ref: '#/definitions/handlers.GetActiveCategoriesResponse'
+        type: array
+      limit:
+        type: integer
+      pageNumber:
+        type: integer
+      totalItems:
+        type: integer
+      totalPages:
+        type: integer
+    type: object
+  handlers.GetActiveCategoriesResponse:
+    properties:
+      external_id:
+        example: "123456"
+        type: string
+      image:
+        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
+        type: string
+      name:
+        example: Bebidas
+        type: string
+    type: object
+  handlers.GetActiveCategoriesSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetActiveCategoriesPaginatedResponse'
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.GetActiveCompaniesResponse:
+    properties:
+      address:
+        $ref: '#/definitions/custom_models.AddressParams'
+      bio:
+        type: string
+      cnpj:
+        type: string
+      created_at:
+        type: string
+      delivery_modes:
+        items:
+          type: string
+        type: array
+      external_id:
+        type: string
+      is_active:
+        type: boolean
+      name:
+        type: string
+      phone_numbers:
+        items:
+          type: string
+        type: array
+      picture:
+        type: string
+      pix_key:
+        type: string
+      products:
+        items:
+          $ref: '#/definitions/custom_models.Product'
+        type: array
+      rating:
+        type: number
+      shipping_fee:
+        type: integer
+      updated_at:
+        type: string
+    type: object
+  handlers.GetActiveCompaniesSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetActiveCompaniesResponse'
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.GetActiveCompanySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetActiveCompaniesResponse'
+    type: object
+  handlers.GetActiveProductsGroupedByCategorySuccessPaginatedResponse:
+    type: object
+  handlers.GetActiveProductsResponse:
+    properties:
+      brand:
+        example: Taeq
+        type: string
+      categories:
+        items:
+          $ref: '#/definitions/custom_models.Category'
+        type: array
+      ean:
+        example: "7895000292035"
+        type: string
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+      image:
+        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
+        type: string
+      is_18_plus:
+        example: false
+        type: boolean
+      is_active:
+        example: true
+        type: boolean
+      is_reviewed:
+        example: false
+        type: boolean
+      name:
+        example: água De Coco Taeq
+        type: string
+    type: object
+  handlers.GetActiveProductsSuccessPaginatedResponse:
+    properties:
+      data:
+        items:
+          $ref: '#/definitions/handlers.GetActiveProductsResponse'
+        type: array
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.GetActiveProductsSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetActiveProductsResponse'
+    type: object
+  handlers.GetCompanyProductsByCategoryResponse:
+    properties:
+      CategoryExternalID:
+        type: string
+      CategoryImage:
+        type: string
+      CategoryName:
+        type: string
+      CompanyExternalID:
+        type: string
+      CompanyName:
+        type: string
+      Products:
+        items:
+          $ref: '#/definitions/custom_models.Product'
+        type: array
+    type: object
+  handlers.GetCompanyProductsByCategorySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetCompanyProductsByCategoryResponse'
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.GetDefaultPriceComparisonRequest:
+    properties:
+      latitude:
+        example: -5.9015168
+        type: number
+      list_external_id:
+        example: list_123
+        type: string
+      longitude:
+        example: -35.2485376
+        type: number
+    required:
+    - latitude
+    - list_external_id
+    - longitude
+    type: object
+  handlers.GetDefaultPriceComparisonResponse:
+    properties:
+      delivery_modes:
+        example:
+        - '["delivery"'
+        - '"pickup"]'
+        items:
+          type: string
+        type: array
+      distance_km:
+        example: 1.5
+        type: number
+      external_id:
+        example: 01JV0K5T2ZGTBJT3RB6CMK2D0P
+        type: string
+      matched_list:
+        items:
+          $ref: '#/definitions/handlers.MatchedProduct'
+        type: array
+      matched_products_count:
+        example: 5
+        type: integer
+      name:
+        example: Supermercado Duas Irmãs
+        type: string
+      picture:
+        example: https://example.com/picture.jpg
+        type: string
+      rating:
+        example: 4.5
+        type: number
+      shipping_fee:
+        example: 50
+        type: integer
+      total_price:
+        example: 1390
+        type: integer
+      unmatched_product_count:
+        example: 2
+        type: integer
+      unmatched_products:
+        items:
+          $ref: '#/definitions/handlers.UnmatchedProductInfo'
+        type: array
+    type: object
+  handlers.GetInvoicesSuccessResponse:
+    properties:
+      data:
+        items:
+          $ref: '#/definitions/handlers.InvoiceResponse'
+        type: array
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.GetMeResponse:
+    properties:
+      addresses:
+        items:
+          $ref: '#/definitions/custom_models.AddressParams'
+        type: array
+      cashback_value:
+        example: 0
+        type: integer
+      cpf:
+        example: "***********"
+        type: string
+      email:
+        example: <EMAIL>
+        type: string
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
+        type: string
+      name:
+        example: Vini
+        type: string
+      phone_numbers:
+        example:
+        - "11999999999"
+        items:
+          type: string
+        type: array
+      subscription_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
+        type: string
+    type: object
+  handlers.GetMeSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetMeResponse'
+    type: object
+  handlers.GetMyCompaniesResponse:
+    properties:
+      company_external_ids:
+        items:
+          type: string
+        type: array
+      dashboard_url:
+        type: string
+      owner_external_id:
+        type: string
+    type: object
+  handlers.GetMyCompaniesSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetMyCompaniesResponse'
+    type: object
+  handlers.GetProductsListResponse:
+    properties:
+      created_at:
+        example: "2021-09-01T00:00:00Z"
+        type: string
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+      icon_url:
+        example: https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png
+        type: string
+      is_public:
+        example: false
+        type: boolean
+      name:
+        example: Compras do mês
+        type: string
+      products:
+        items:
+          $ref: '#/definitions/custom_models.Product'
+        type: array
+      updated_at:
+        example: "2021-09-01T00:00:00Z"
+        type: string
+    type: object
+  handlers.GetProductsListSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.GetProductsListResponse'
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.InvoiceProductResponse:
+    properties:
+      discount:
+        example: 50
+        type: integer
+      product_brand:
+        example: Brand Name
+        type: string
+      product_ean:
+        example: "1234567890123"
+        type: string
+      product_external_id:
+        example: prod_abc123
+        type: string
+      product_image:
+        example: https://example.com/image.jpg
+        type: string
+      product_name:
+        example: Product Name
+        type: string
+      quantity:
+        example: 2
+        type: integer
+      unit_price:
+        example: 750
+        type: integer
+    type: object
+  handlers.InvoiceResponse:
+    properties:
+      amount:
+        example: 1500
+        type: integer
+      company_bio:
+        example: Store description
+        type: string
+      company_cnpj:
+        example: "12345678000100"
+        type: string
+      company_external_id:
+        example: comp_abc123
+        type: string
+      company_name:
+        example: Store Name
+        type: string
+      company_phone_numbers:
+        example:
+        - '["+5511999999999"]'
+        items:
+          type: string
+        type: array
+      company_picture:
+        example: https://example.com/store.jpg
+        type: string
+      company_pix_key:
+        example: "12345678000100"
+        type: string
+      coupon:
+        example: SAVE10
+        type: string
+      created_at:
+        example: "2024-01-01T10:00:00Z"
+        type: string
+      delivery_mode:
+        example: delivery
+        type: string
+      discount:
+        example: 100
+        type: integer
+      finished_at:
+        example: "2024-01-01T15:30:00Z"
+        type: string
+      info:
+        example: Additional information
+        type: string
+      order_id:
+        example: order_abc123
+        type: string
+      payment_method:
+        example: pix
+        type: string
+      products:
+        items:
+          $ref: '#/definitions/handlers.InvoiceProductResponse'
+        type: array
+      shipping_fee:
+        example: 500
+        type: integer
+      status:
+        example: processing
+        type: string
+      status_description:
+        example: Pagamento aprovado - Aguardando aceite do supermercado
+        type: string
+      updated_at:
+        example: "2024-01-01T10:30:00Z"
+        type: string
+      user_address:
+        example: Rua Example, 123
+        type: string
+      user_cpf:
+        example: "***********"
+        type: string
+      user_email:
+        example: <EMAIL>
+        type: string
+      user_name:
+        example: Customer Name
+        type: string
+      user_phone_number:
+        example: "+5511999999999"
+        type: string
+    type: object
+  handlers.LinkUserToCompanyRequest:
+    properties:
+      user_external_id:
+        example: 01JR0X8RNYFJECMV1NBNVK9921
+        type: string
+    required:
+    - user_external_id
+    type: object
+  handlers.LinkUserToCompanyResponse:
+    properties:
+      company_external_id:
+        type: string
+      company_name:
+        type: string
+      user_external_id:
+        type: string
+      user_name:
+        type: string
+    type: object
+  handlers.LinkUserToCompanySuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.LinkUserToCompanyResponse'
+    type: object
+  handlers.ListCouponsSuccessResponse:
+    properties:
+      data:
+        items:
+          $ref: '#/definitions/handlers.Coupon'
+        type: array
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.ListOneCouponSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.Coupon'
+    type: object
+  handlers.LoginRequest:
+    properties:
+      email:
+        example: <EMAIL>
+        type: string
+      login_code:
+        example: "12345"
+        type: string
+    required:
+    - email
+    - login_code
+    type: object
+  handlers.LoginResponse:
+    properties:
+      access_token:
+        type: string
+      refresh_token:
+        type: string
+    type: object
+  handlers.LoginSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.LoginResponse'
+    type: object
+  handlers.MatchedProduct:
+    properties:
+      brand:
+        example: Marca
+        type: string
+      categories:
+        items:
+          $ref: '#/definitions/custom_models.Category'
+        type: array
+      description:
+        example: Descrição do produto
+        type: string
+      discount:
+        example: 10
+        type: integer
+      ean:
+        example: "7891234567890"
+        type: string
+      external_id:
+        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
+        type: string
+      image:
+        example: https://example.com/image.jpg
+        type: string
+      is_18_plus:
+        example: false
+        type: boolean
+      name:
+        example: Produto Disponível
+        type: string
+      price:
+        example: 1250
+        type: integer
+      quantity:
+        example: 2
+        type: integer
+      stock:
+        example: 50
+        type: integer
+    type: object
+  handlers.MissingProduct:
+    properties:
+      external_id:
+        example: 01JSF54A7XXP82H87M64C26AC8
+        type: string
+      image:
+        example: https://example.com/image.jpg
+        type: string
+      name:
+        example: Produto Original
+        type: string
+      quantity:
+        example: 2
+        type: integer
+    type: object
+  handlers.ProductsCheckout:
+    properties:
+      external_id:
+        type: string
+      quantity:
+        type: integer
+    required:
+    - external_id
+    - quantity
+    type: object
+  handlers.RefreshRequest:
+    properties:
+      refresh_token:
+        type: string
+    required:
+    - refresh_token
+    type: object
+  handlers.RefreshResponse:
+    properties:
+      access_token:
+        type: string
+    type: object
+  handlers.RefreshSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.RefreshResponse'
+    type: object
+  handlers.SearchResponse:
+    properties:
+      category_external_id:
+        type: string
+      category_image:
+        type: string
+      category_name:
+        type: string
+      company_external_id:
+        type: string
+      company_name:
+        type: string
+      discount:
+        type: integer
+      price:
+        type: integer
+      product_brand:
+        type: string
+      product_external_id:
+        type: string
+      product_image:
+        type: string
+      product_is_18_plus:
+        type: boolean
+      product_name:
+        type: string
+      search_rank:
+        type: number
+      stock:
+        type: integer
+    type: object
+  handlers.SearchSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.SearchResponse'
+      limit:
+        example: 10
+        type: integer
+      pageNumber:
+        example: 1
+        type: integer
+      totalItems:
+        example: 100
+        type: integer
+      totalPages:
+        example: 10
+        type: integer
+    type: object
+  handlers.SendLoginCodeRequest:
+    properties:
+      email:
+        example: <EMAIL>
+        type: string
+    required:
+    - email
+    type: object
+  handlers.SimilarProducts:
+    properties:
+      brand:
+        example: Taeq
+        type: string
+      external_id:
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
+        type: string
+      name:
+        example: água De Coco Taeq
+        type: string
+    type: object
+  handlers.SubstituteProduct:
+    properties:
+      brand:
+        example: Marca
+        type: string
+      categories:
+        items:
+          $ref: '#/definitions/custom_models.Category'
+        type: array
+      description:
+        example: Descrição do produto
+        type: string
+      discount:
+        example: 10
+        type: integer
+      ean:
+        example: "7891234567890"
+        type: string
+      external_id:
+        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
+        type: string
+      image:
+        example: https://example.com/image.jpg
+        type: string
+      is_18_plus:
+        example: false
+        type: boolean
+      match_reason:
+        example: same_category
+        type: string
+      name:
+        example: Produto Substituto
+        type: string
+      price:
+        example: 1250
+        type: integer
+      stock:
+        example: 50
+        type: integer
+    type: object
+  handlers.UnmatchedProductInfo:
+    properties:
+      missing_product:
+        $ref: '#/definitions/handlers.MissingProduct'
+      substitutes:
+        items:
+          $ref: '#/definitions/handlers.SubstituteProduct'
+        type: array
+    type: object
+  handlers.UpdateCompanyDataRequest:
+    properties:
+      address:
+        $ref: '#/definitions/custom_models.AddressParams'
+      bio:
+        example: Mercadinho Caicó é o melhor mercadinho da região
+        type: string
+      delivery_modes:
+        example:
+        - delivery
+        - pickup
+        items:
+          type: string
+        type: array
+      phone_numbers:
+        example:
+        - "***********"
+        items:
+          type: string
+        type: array
+      shipping_fee:
+        example: 500
+        type: integer
+    required:
+    - address
+    - bio
+    - delivery_modes
+    - phone_numbers
+    type: object
+  handlers.UpdateInvoiceStatusResponse:
+    properties:
+      message:
+        example: Invoice status updated successfully
+        type: string
+      order_id:
+        example: order_abc123
+        type: string
+      status:
+        example: processing
+        type: string
+      status_description:
+        example: Pagamento aprovado - Aguardando aceite do supermercado
+        type: string
+    type: object
+  handlers.UpdateInvoiceStatusSuccessResponse:
+    properties:
+      data:
+        $ref: '#/definitions/handlers.UpdateInvoiceStatusResponse'
+    type: object
+  handlers.UpdateProductDataRequest:
+    properties:
+      brand:
+        example: Brand XYZ
+        type: string
+      categories:
+        items:
+          $ref: '#/definitions/custom_models.Category'
+        maxItems: 1
+        type: array
+      ean:
+        example: "1234567890123"
+        type: string
+      is_18_plus:
+        example: false
+        type: boolean
+      is_active:
+        example: true
+        type: boolean
+      is_reviewed:
+        example: false
+        type: boolean
+      name:
+        example: Product Name
+        type: string
+    required:
+    - brand
+    - categories
+    - ean
+    - name
+    type: object
+  handlers.UpdateStatusRequest:
+    properties:
+      reason:
+        example: Order accepted by store
+        type: string
+      status:
+        example: processing
+        type: string
+    type: object
+  handlers.UpdateUserNameRequest:
+    properties:
+      name:
+        example: Vini
+        type: string
+    required:
+    - name
+    type: object
+  woovi.PixSuccessResponse:
+    properties:
+      charge:
+        properties:
+          additionalInfo:
+            items:
+              properties:
+                key:
+                  type: string
+                value:
+                  type: string
+              type: object
+            type: array
+          brCode:
+            type: string
+          comment:
+            type: string
+          correlationID:
+            type: string
+          createdAt:
+            type: string
+          customer:
+            properties:
+              email:
+                type: string
+              name:
+                type: string
+              phone:
+                type: string
+              taxID:
+                properties:
+                  taxID:
+                    type: string
+                  type:
+                    type: string
+                type: object
+            type: object
+          expiresDate:
+            type: string
+          expiresIn:
+            type: integer
+          paymentLinkID:
+            type: string
+          paymentLinkUrl:
+            type: string
+          paymentMethods:
+            properties:
+              pix:
+                properties:
+                  additionalInfo:
+                    items: {}
+                    type: array
+                  brCode:
+                    type: string
+                  fee:
+                    type: integer
+                  identifier:
+                    type: string
+                  method:
+                    type: string
+                  qrCodeImage:
+                    type: string
+                  status:
+                    type: string
+                  transactionID:
+                    type: string
+                  txId:
+                    type: string
+                  value:
+                    type: integer
+                type: object
+            type: object
+          qrCodeImage:
+            type: string
+          status:
+            type: string
+          updatedAt:
+            type: string
+          value:
+            type: integer
+        type: object
+    type: object
+host: '{{.Host}}'
+info:
+  contact:
+    email: <EMAIL>
+    name: Developer Support
+    url: https://www.izymercado.com.br
+  description: '### API Documentation'
+  title: Izy Mercado API
+  version: "1.0"
+paths:
+  /v1/auth/login:
+    post:
+      consumes:
+      - application/json
+      description: Login
+      parameters:
+      - description: Login payload
+        in: body
+        name: body
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.LoginRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.LoginSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Login
+      tags:
+      - Auth
+  /v1/auth/logout:
+    post:
+      consumes:
+      - application/json
+      description: Logout
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Logout
+      tags:
+      - Auth
+  /v1/auth/refresh:
+    post:
+      consumes:
+      - application/json
+      description: Refresh token
+      parameters:
+      - description: Refresh token payload
+        in: body
+        name: body
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.RefreshRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.RefreshSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Refresh token
+      tags:
+      - Auth
+  /v1/auth/send-login-code:
+    post:
+      consumes:
+      - application/json
+      description: Send login code
+      parameters:
+      - description: Send login code payload
+        in: body
+        name: body
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.SendLoginCodeRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Send login code
+      tags:
+      - Auth
+  /v1/category:
+    get:
+      consumes:
+      - application/json
+      description: Get active categories
+      parameters:
+      - description: Page
+        in: query
+        name: page
+        type: string
+      - description: Limit
+        in: query
+        name: limit
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: List of categories
+          schema:
+            $ref: '#/definitions/handlers.GetActiveCategoriesSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get active categories
+      tags:
+      - Category
+    post:
+      consumes:
+      - multipart/form-data
+      description: Create a new category
+      parameters:
+      - description: Category name
+        in: formData
+        name: name
+        required: true
+        type: string
+      - description: Category image
+        in: formData
+        name: image
+        required: true
+        type: file
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: Category created
+          schema:
+            $ref: '#/definitions/handlers.CreateCategorySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Create a new category
+      tags:
+      - Category
+  /v1/company:
+    get:
+      consumes:
+      - application/json
+      description: Get active companies
+      parameters:
+      - description: Page
+        in: query
+        name: page
+        type: string
+      - description: Limit
+        in: query
+        name: limit
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: List of companies
+          schema:
+            $ref: '#/definitions/handlers.GetActiveCompaniesSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get active companies
+      tags:
+      - Company
+    post:
+      consumes:
+      - miltipart/form-data
+      description: Create a new company
+      parameters:
+      - description: Company name
+        in: formData
+        name: name
+        required: true
+        type: string
+      - description: Company CNPJ
+        in: formData
+        name: cnpj
+        required: true
+        type: string
+      - description: Company bio
+        in: formData
+        name: bio
+        required: true
+        type: string
+      - description: Company image
+        in: formData
+        name: image
+        required: true
+        type: file
+      - description: Pix key
+        in: formData
+        name: pix_key
+        required: true
+        type: string
+      - description: Delivery modes
+        in: formData
+        name: delivery_modes
+        required: true
+        type: string
+      - description: Shipping fee
+        in: formData
+        name: shipping_fee
+        required: true
+        type: integer
+      - description: Commission rate
+        in: formData
+        name: commission_rate
+        required: true
+        type: integer
+      - description: Cashback rate
+        in: formData
+        name: cashback_rate
+        required: true
+        type: integer
+      - description: Phone numbers
+        in: formData
+        name: phone_numbers
+        required: true
+        type: string
+      - description: Address name
+        in: formData
+        name: address_name
+        required: true
+        type: string
+      - description: Address street
+        in: formData
+        name: address_street
+        required: true
+        type: string
+      - description: Address number
+        in: formData
+        name: address_number
+        required: true
+        type: string
+      - description: Address neighborhood
+        in: formData
+        name: address_neighborhood
+        required: true
+        type: string
+      - description: Address city
+        in: formData
+        name: address_city
+        required: true
+        type: string
+      - description: Address state
+        in: formData
+        name: address_state
+        required: true
+        type: string
+      - description: Address zip code
+        in: formData
+        name: address_zip_code
+        required: true
+        type: string
+      - description: Address location latitude
+        in: formData
+        name: address_location_latitude
+        required: true
+        type: number
+      - description: Address location longitude
+        in: formData
+        name: address_location_longitude
+        required: true
+        type: number
+      - description: Address complement
+        in: formData
+        name: address_complement
+        type: string
+      - description: Owner external ID
+        in: formData
+        name: owner_external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: Company created
+          schema:
+            $ref: '#/definitions/handlers.CreateCompanySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Create a new company
+      tags:
+      - Company
+  /v1/company/{company_external_id}/category/{category_external_id}:
+    get:
+      consumes:
+      - application/json
+      description: Get company products by category
+      parameters:
+      - description: Company external ID
+        in: path
+        name: company_external_id
+        required: true
+        type: string
+      - description: Category external ID
+        in: path
+        name: category_external_id
+        required: true
+        type: string
+      - description: Page
+        in: query
+        name: page
+        type: string
+      - description: Limit
+        in: query
+        name: limit
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company products
+          schema:
+            $ref: '#/definitions/handlers.GetCompanyProductsByCategorySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get company products by category
+      tags:
+      - Company
+  /v1/company/{externalID}:
+    get:
+      consumes:
+      - application/json
+      description: Get one company
+      parameters:
+      - description: External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company
+          schema:
+            $ref: '#/definitions/handlers.GetActiveCompanySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get one company
+      tags:
+      - Company
+    patch:
+      consumes:
+      - miltipart/form-data
+      description: Update company image
+      parameters:
+      - description: External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      - description: Company image
+        in: formData
+        name: image
+        required: true
+        type: file
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company image updated
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update company image
+      tags:
+      - Company
+    put:
+      consumes:
+      - application/json
+      description: Update company data
+      parameters:
+      - description: External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      - description: Company data
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.UpdateCompanyDataRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company data updated
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update company data
+      tags:
+      - Company
+  /v1/company/{externalID}/activate:
+    put:
+      consumes:
+      - application/json
+      description: Activate a company by setting is_active to true. Requires admin
+        role and validates company has valid owner.
+      parameters:
+      - description: Company External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company activated successfully
+          schema:
+            $ref: '#/definitions/handlers.ActivateCompanySuccessResponse'
+        "400":
+          description: Bad request - Company cannot be activated without valid owner
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "403":
+          description: Forbidden - Admin role required
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Company not found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Activate a company
+      tags:
+      - Company
+  /v1/company/{externalID}/owner:
+    put:
+      consumes:
+      - application/json
+      description: Link an active user to a company by setting the user as the company
+        owner. Requires admin role and validates user is active.
+      parameters:
+      - description: Company External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      - description: User external ID to link as owner
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.LinkUserToCompanyRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: User linked to company successfully
+          schema:
+            $ref: '#/definitions/handlers.LinkUserToCompanySuccessResponse'
+        "400":
+          description: Bad request - User not found or not active
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "403":
+          description: Forbidden - Admin role required
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Company not found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Link a user to company as owner
+      tags:
+      - Company
+  /v1/company/{externalID}/products:
+    delete:
+      consumes:
+      - application/json
+      description: Remove products from a company
+      parameters:
+      - description: External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      - description: Products data
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.AddProductsToCompanyRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Products removed successfully
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Remove products from a company
+      tags:
+      - Company
+    put:
+      consumes:
+      - application/json
+      description: Add products to a company
+      parameters:
+      - description: External ID
+        in: path
+        name: externalID
+        required: true
+        type: string
+      - description: Products data
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.AddProductsToCompanyRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Products added successfully
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Add products to a company
+      tags:
+      - Company
+  /v1/company/invoice:
+    get:
+      consumes:
+      - application/json
+      description: Get all invoices for companies owned by the authenticated user
+        with user information and products
+      parameters:
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Company invoices retrieved successfully
+          schema:
+            $ref: '#/definitions/handlers.GetInvoicesSuccessResponse'
+        "400":
+          description: Bad request - invalid pagination parameters
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get company invoices
+      tags:
+      - Company
+  /v1/company/invoice/{order_id}/status:
+    put:
+      consumes:
+      - application/json
+      description: Update the status of an invoice for a company owned by the authenticated
+        user (allows cancellation in specific states)
+      parameters:
+      - description: Order ID
+        in: path
+        name: order_id
+        required: true
+        type: string
+      - description: Status update payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.UpdateStatusRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Invoice status updated successfully
+          schema:
+            $ref: '#/definitions/handlers.UpdateInvoiceStatusSuccessResponse'
+        "400":
+          description: Bad request - invalid status or transition
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "403":
+          description: Forbidden - user doesn't own company or invalid cancellation
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Invoice not found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update company invoice status
+      tags:
+      - Company
+  /v1/company/my-companies:
+    get:
+      consumes:
+      - application/json
+      description: Get all company external IDs that the authenticated user owns
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: List of company external IDs owned by the user
+          schema:
+            $ref: '#/definitions/handlers.GetMyCompaniesSuccessResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get my companies
+      tags:
+      - Company
+  /v1/company/subaccount:
+    post:
+      consumes:
+      - application/json
+      description: Create a new subaccount
+      parameters:
+      - description: Subaccount data
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CreateSubAccountRequest'
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: Subaccount created
+          schema:
+            type: string
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Create a new subaccount
+      tags:
+      - Company
+  /v1/coupon:
+    get:
+      consumes:
+      - application/json
+      description: List all coupons with pagination
+      parameters:
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.ListCouponsSuccessResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: List all coupons
+      tags:
+      - Coupons
+    post:
+      consumes:
+      - application/json
+      description: Cria um novo cupom de desconto
+      parameters:
+      - description: Dados do cupom
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CreateCouponRequest'
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: Cupom criado com sucesso
+          schema:
+            type: string
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Cria um novo cupom
+      tags:
+      - Coupons
+  /v1/coupon/{external_id}:
+    get:
+      consumes:
+      - application/json
+      description: Get a specific coupon by its ExternalID
+      parameters:
+      - description: Coupon ExternalID
+        in: path
+        name: external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.ListOneCouponSuccessResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Not Found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get a coupon by ExternalID
+      tags:
+      - Coupons
+  /v1/coupon/{external_id}/status:
+    patch:
+      consumes:
+      - application/json
+      description: Ativa ou desativa um cupom através do parâmetro status
+      parameters:
+      - description: Coupon ExternalID
+        in: path
+        name: external_id
+        required: true
+        type: string
+      - description: true para ativar, false para desativar
+        in: query
+        name: status
+        required: true
+        type: boolean
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Not Found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Atualiza o status de um cupom
+      tags:
+      - Coupons
+  /v1/coupon/apply:
+    post:
+      consumes:
+      - application/json
+      description: Valida e aplica um cupom de desconto ao pedido
+      parameters:
+      - description: Dados para aplicar o cupom
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.ApplyCouponRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.ApplyCouponResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Aplica um cupom de desconto
+      tags:
+      - Coupons
+  /v1/payment/checkout:
+    post:
+      consumes:
+      - application/json
+      description: Checkout
+      parameters:
+      - description: Checkout Request
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CheckoutRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.CheckoutSuccessResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Checkout
+      tags:
+      - Payment
+  /v1/payment/process-expired-charge-event-callback:
+    post:
+      consumes:
+      - application/json
+      description: Process expired charge event callback
+      parameters:
+      - description: Charge Event
+        in: body
+        name: body
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.ChargeEvent'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            type: string
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - ApiKeyAuth: []
+      summary: Process expired charge event callback
+      tags:
+      - Payment
+  /v1/payment/process-paid-charge-event-callback:
+    post:
+      consumes:
+      - application/json
+      description: Transfer between sub accounts callback
+      parameters:
+      - description: Charge Event
+        in: body
+        name: body
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.ChargeEvent'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            type: string
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - ApiKeyAuth: []
+      summary: Transfer between sub accounts callback
+      tags:
+      - Payment
+  /v1/price-comparison/default:
+    post:
+      consumes:
+      - application/json
+      description: Get default price comparison
+      parameters:
+      - description: Get default price comparison request
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.GetDefaultPriceComparisonRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            $ref: '#/definitions/handlers.GetDefaultPriceComparisonResponse'
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get default price comparison
+      tags:
+      - PriceComparison
+  /v1/price-comparison/recommendations/{list_external_id}:
+    get:
+      consumes:
+      - application/json
+      description: Get price comparison recommendations
+      parameters:
+      - description: List External ID
+        example: '"list_123"'
+        in: path
+        name: list_external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: OK
+          schema:
+            items:
+              $ref: '#/definitions/handlers.GetDefaultPriceComparisonResponse'
+            type: array
+        "400":
+          description: Bad Request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal Server Error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get price comparison recommendations
+      tags:
+      - PriceComparison
+  /v1/product:
+    get:
+      consumes:
+      - application/json
+      description: Get active products
+      parameters:
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Paginated list of products
+          schema:
+            $ref: '#/definitions/handlers.GetActiveProductsSuccessPaginatedResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get active products
+      tags:
+      - Product
+    post:
+      consumes:
+      - multipart/form-data
+      description: Create a new product
+      parameters:
+      - description: Product EAN
+        example: "1234567890123"
+        in: formData
+        name: ean
+        required: true
+        type: string
+      - description: Product name
+        example: Product Name
+        in: formData
+        name: name
+        required: true
+        type: string
+      - description: Product image file
+        in: formData
+        name: image
+        required: true
+        type: file
+      - description: Product categories
+        in: formData
+        name: categories
+        required: true
+        type: string
+      - description: Product brand
+        example: Brand XYZ
+        in: formData
+        name: brand
+        required: true
+        type: string
+      - description: Is 18+ product
+        example: false
+        in: formData
+        name: is_18_plus
+        type: boolean
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: Product created
+          schema:
+            $ref: '#/definitions/handlers.CreateProductSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Create a new product
+      tags:
+      - Product
+  /v1/product/{ean}:
+    get:
+      consumes:
+      - application/json
+      description: Get product by EAN
+      parameters:
+      - description: Product EAN
+        example: "1234567890123"
+        in: path
+        name: ean
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Product data
+          schema:
+            $ref: '#/definitions/handlers.GetActiveProductsSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get product by EAN
+      tags:
+      - Product
+  /v1/product/{externalID}:
+    patch:
+      consumes:
+      - multipart/form-data
+      description: Update product image
+      parameters:
+      - description: Product image file
+        in: formData
+        name: image
+        required: true
+        type: file
+      - description: External ID
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
+        in: path
+        name: externalID
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Product image updated
+          schema:
+            type: object
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update product image
+      tags:
+      - Product
+    put:
+      consumes:
+      - application/json
+      description: Update a product
+      parameters:
+      - description: Product data
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.UpdateProductDataRequest'
+      - description: External ID
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
+        in: path
+        name: externalID
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Product updated
+          schema:
+            type: object
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update a product
+      tags:
+      - Product
+  /v1/product/category/{externalID}:
+    get:
+      consumes:
+      - application/json
+      description: Get products grouped by category
+      parameters:
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      - description: Category External ID
+        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
+        in: path
+        name: externalID
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Paginated list of products
+          schema:
+            $ref: '#/definitions/handlers.GetActiveProductsGroupedByCategorySuccessPaginatedResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get products grouped by category
+      tags:
+      - Product
+  /v1/search/{query}:
+    get:
+      consumes:
+      - application/json
+      deprecated: true
+      description: Perform a full-text search (legacy endpoint)
+      parameters:
+      - description: Search query
+        in: path
+        name: query
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Search results
+          schema:
+            $ref: '#/definitions/common.SuccessResponse-array_handlers_SearchResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Legacy Search (deprecated)
+      tags:
+      - Search
+  /v1/search/category/{category_id}/{query}:
+    get:
+      consumes:
+      - application/json
+      description: Search products within a specific category
+      parameters:
+      - description: Category external ID
+        in: path
+        name: category_id
+        required: true
+        type: string
+      - description: Search query
+        in: path
+        name: query
+        required: true
+        type: string
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Search results with pagination
+          schema:
+            $ref: '#/definitions/handlers.SearchSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Category Product Search
+      tags:
+      - Search
+  /v1/search/company/{company_id}/{query}:
+    get:
+      consumes:
+      - application/json
+      description: Search products within a specific company
+      parameters:
+      - description: Company external ID
+        in: path
+        name: company_id
+        required: true
+        type: string
+      - description: Search query
+        in: path
+        name: query
+        required: true
+        type: string
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Search results with pagination
+          schema:
+            $ref: '#/definitions/handlers.SearchSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Company Product Search
+      tags:
+      - Search
+  /v1/search/company/{company_id}/category/{category_id}/{query}:
+    get:
+      consumes:
+      - application/json
+      description: Search products within a specific company and category
+      parameters:
+      - description: Company external ID
+        in: path
+        name: company_id
+        required: true
+        type: string
+      - description: Category external ID
+        in: path
+        name: category_id
+        required: true
+        type: string
+      - description: Search query
+        in: path
+        name: query
+        required: true
+        type: string
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Search results with pagination
+          schema:
+            $ref: '#/definitions/handlers.SearchSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Company Category Product Search
+      tags:
+      - Search
+  /v1/search/global/{query}:
+    get:
+      consumes:
+      - application/json
+      description: Search across all products in the system
+      parameters:
+      - description: Search query
+        in: path
+        name: query
+        required: true
+        type: string
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Search results with pagination
+          schema:
+            $ref: '#/definitions/handlers.SearchSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Global Product Search
+      tags:
+      - Search
+  /v1/user:
+    post:
+      consumes:
+      - application/json
+      description: Create a new user
+      parameters:
+      - description: User payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CreateUserRequest'
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.CreateUserSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Create a new user
+      tags:
+      - Users
+  /v1/user/address:
+    post:
+      consumes:
+      - application/json
+      description: Create or Update user address
+      parameters:
+      - description: User address payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/custom_models.AddressParams'
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.CreateUserAddressSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Upsert user address
+      tags:
+      - Users
+  /v1/user/address/{address_external_id}:
+    delete:
+      consumes:
+      - application/json
+      description: Delete user address
+      parameters:
+      - description: Address external id
+        in: path
+        name: address_external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Delete user address
+      tags:
+      - Users
+  /v1/user/exists/email:
+    post:
+      consumes:
+      - application/json
+      description: Check if email exists
+      parameters:
+      - description: User payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CheckIfEmailExistsRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.CheckIfEmailExistsSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Check if email exists
+      tags:
+      - Users
+  /v1/user/invoice:
+    get:
+      consumes:
+      - application/json
+      description: Get all invoices for the authenticated user with company information
+        and products
+      parameters:
+      - description: 'Page number (default: 1)'
+        in: query
+        name: page
+        type: integer
+      - description: 'Items per page (default: 10)'
+        in: query
+        name: limit
+        type: integer
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: User invoices retrieved successfully
+          schema:
+            $ref: '#/definitions/handlers.GetInvoicesSuccessResponse'
+        "400":
+          description: Bad request - invalid pagination parameters
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get user invoices
+      tags:
+      - Users
+  /v1/user/invoice/{order_id}/status:
+    put:
+      consumes:
+      - application/json
+      description: Update the status of a user's invoice (allows cancellation in specific
+        states)
+      parameters:
+      - description: Order ID
+        in: path
+        name: order_id
+        required: true
+        type: string
+      - description: Status update payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.UpdateStatusRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Invoice status updated successfully
+          schema:
+            $ref: '#/definitions/handlers.UpdateInvoiceStatusSuccessResponse'
+        "400":
+          description: Bad request - invalid status or transition
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "403":
+          description: Forbidden - user doesn't own invoice or invalid cancellation
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "404":
+          description: Invoice not found
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update user invoice status
+      tags:
+      - Users
+  /v1/user/list:
+    get:
+      consumes:
+      - application/json
+      description: Get user products list
+      parameters:
+      - description: Page
+        in: query
+        name: page
+        type: string
+      - description: Limit
+        in: query
+        name: limit
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get user products list
+      tags:
+      - Users
+    post:
+      consumes:
+      - application/json
+      description: Create or Update a products list
+      parameters:
+      - description: Products list payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.CreateProductsListRequest'
+      produces:
+      - application/json
+      responses:
+        "201":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.CreateProductsListSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Upsert a products list
+      tags:
+      - Users
+  /v1/user/list/{list_external_id}:
+    delete:
+      consumes:
+      - application/json
+      description: Delete user products list
+      parameters:
+      - description: List external id
+        in: path
+        name: list_external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Delete user products list
+      tags:
+      - Users
+    get:
+      consumes:
+      - application/json
+      description: Get user products list by external id
+      parameters:
+      - description: List external id
+        in: path
+        name: list_external_id
+        required: true
+        type: string
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get user products list by external id
+      tags:
+      - Users
+  /v1/user/list/icons:
+    get:
+      consumes:
+      - application/json
+      description: Get user products list icons
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            additionalProperties:
+              type: string
+            type: object
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get user products list icons
+      tags:
+      - Users
+  /v1/user/list/template:
+    get:
+      consumes:
+      - application/json
+      description: Get random template products list
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      summary: Get random template products list
+      tags:
+      - Users
+  /v1/user/me:
+    delete:
+      consumes:
+      - application/json
+      description: Delete user
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Delete user
+      tags:
+      - Users
+    get:
+      consumes:
+      - application/json
+      description: Get user info
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.GetMeSuccessResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Get user info
+      tags:
+      - Users
+  /v1/user/name:
+    patch:
+      consumes:
+      - application/json
+      description: Update name
+      parameters:
+      - description: User name payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.UpdateUserNameRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: ok
+          schema:
+            $ref: '#/definitions/handlers.EmptySuccessResponse'
+        "400":
+          description: bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Update name
+      tags:
+      - Users
+  /v1/user/role:
+    post:
+      consumes:
+      - application/json
+      description: Assign a role to a user
+      parameters:
+      - description: Assign role payload
+        in: body
+        name: payload
+        required: true
+        schema:
+          $ref: '#/definitions/handlers.AssignRoleToUserRequest'
+      produces:
+      - application/json
+      responses:
+        "200":
+          description: Role assigned successfully
+          schema:
+            $ref: '#/definitions/handlers.AssignRoleToUserSuccessResponse'
+        "400":
+          description: Bad request
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "401":
+          description: Unauthorized
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+        "500":
+          description: Internal server error
+          schema:
+            $ref: '#/definitions/common.ErrorResponse'
+      security:
+      - Bearer: []
+      summary: Assign role to user
+      tags:
+      - Users
+securityDefinitions:
+  ApiKeyAuth:
+    description: Type "Bearer" followed by a space and JWT token.
+    in: header
+    name: Authorization
+    type: apiKey
+  Bearer:
+    description: Type "Bearer" followed by a space and JWT token.
+    in: header
+    name: Authorization
+    type: apiKey
+swagger: "2.0"
