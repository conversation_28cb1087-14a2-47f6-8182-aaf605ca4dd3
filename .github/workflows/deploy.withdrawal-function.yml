name: Deploy Withdrawal Processor Function

on:
  push:
    branches:
      - main
    paths:
      - 'functions/withdrawal-processor/**'
  workflow_dispatch:

env:
  GCP_PROJECT_ID: axial-radius-455022-n2
  REGION: us-central1
  FUNCTION_NAME: withdrawal-processor

jobs:
  deploy-function:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: ${{ env.GCP_PROJECT_ID }}

    - name: Verify authentication
      run: gcloud auth list

    - name: Deploy Cloud Function
      working-directory: functions/withdrawal-processor
      run: |
        gcloud functions deploy ${{ env.FUNCTION_NAME }} \
          --gen2 \
          --runtime=go123 \
          --region=${{ env.REGION }} \
          --source=. \
          --entry-point=ProcessWithdrawalPayouts \
          --trigger-http \
          --allow-unauthenticated \
          --memory=256MB \
          --timeout=540s \
          --max-instances=10 \
          --min-instances=0 \
          --set-secrets="DATABASE_URL=DATABASE_URL:latest" \
          --vpc-connector=cloudsql-connector \
          --project=${{ env.GCP_PROJECT_ID }}

    - name: Get Function URL
      id: get-url
      run: |
        FUNCTION_URL=$(gcloud functions describe ${{ env.FUNCTION_NAME }} \
          --region=${{ env.REGION }} \
          --project=${{ env.GCP_PROJECT_ID }} \
          --format="value(serviceConfig.uri)")
        echo "function_url=$FUNCTION_URL" >> $GITHUB_OUTPUT
        echo "Function deployed at: $FUNCTION_URL"

    - name: Create/Update Cloud Scheduler Jobs
      run: |
        FUNCTION_URL="${{ steps.get-url.outputs.function_url }}"
        
        # Create payout updater job (every hour)
        gcloud scheduler jobs create http withdrawal-payout-updater \
          --location=${{ env.REGION }} \
          --schedule="0 * * * *" \
          --uri="$FUNCTION_URL" \
          --http-method=POST \
          --headers="Content-Type=application/json" \
          --message-body='{"action":"update_payouts"}' \
          --project=${{ env.GCP_PROJECT_ID }} \
          --quiet || \
        gcloud scheduler jobs update http withdrawal-payout-updater \
          --location=${{ env.REGION }} \
          --schedule="0 * * * *" \
          --uri="$FUNCTION_URL" \
          --http-method=POST \
          --headers="Content-Type=application/json" \
          --message-body='{"action":"update_payouts"}' \
          --project=${{ env.GCP_PROJECT_ID }}
        
        # Create balance refresher job (every 15 minutes)
        gcloud scheduler jobs create http withdrawal-balance-refresher \
          --location=${{ env.REGION }} \
          --schedule="*/15 * * * *" \
          --uri="$FUNCTION_URL" \
          --http-method=POST \
          --headers="Content-Type=application/json" \
          --message-body='{"action":"refresh_balance_view"}' \
          --project=${{ env.GCP_PROJECT_ID }} \
          --quiet || \
        gcloud scheduler jobs update http withdrawal-balance-refresher \
          --location=${{ env.REGION }} \
          --schedule="*/15 * * * *" \
          --uri="$FUNCTION_URL" \
          --http-method=POST \
          --headers="Content-Type=application/json" \
          --message-body='{"action":"refresh_balance_view"}' \
          --project=${{ env.GCP_PROJECT_ID }}

    - name: Test Function
      run: |
        FUNCTION_URL="${{ steps.get-url.outputs.function_url }}"
        echo "Testing function with update_payouts action..."
        curl -X POST "$FUNCTION_URL" \
          -H "Content-Type: application/json" \
          -d '{"action":"update_payouts"}' \
          --fail --silent --show-error || echo "Function test failed"

    - name: Deployment Summary
      run: |
        echo "🎉 Withdrawal Processor Function deployed successfully!"
        echo "📍 Function URL: ${{ steps.get-url.outputs.function_url }}"
        echo "⏰ Payout updates: Every hour"
        echo "🔄 Balance refresh: Every 15 minutes"
