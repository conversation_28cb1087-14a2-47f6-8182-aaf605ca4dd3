#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if .env file exists
if [ ! -f "metabase/.env" ]; then
    echo -e "${RED}❌ Error: metabase/.env file not found!${NC}"
    echo -e "${YELLOW}💡 Please copy metabase/.env.example to metabase/.env and configure your values${NC}"
    exit 1
fi

# Load environment variables
echo -e "${BLUE}📋 Loading environment variables...${NC}"
source metabase/.env

# Check authentication
echo -e "${BLUE}🔐 Checking Google Cloud authentication...${NC}"
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Error: Not authenticated with Google Cloud${NC}"
    echo -e "${YELLOW}💡 Please run: gcloud auth login${NC}"
    exit 1
fi

# Check project configuration
CURRENT_PROJECT=$(gcloud config get-value project 2>/dev/null)
if [ "$CURRENT_PROJECT" != "$PROJECT_ID" ]; then
    echo -e "${YELLOW}⚠️  Setting project to $PROJECT_ID${NC}"
    gcloud config set project $PROJECT_ID
fi

# Validate required variables
required_vars=("PROJECT_ID" "VM_NAME" "ZONE" "MACHINE_TYPE")
for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}❌ Error: $var is not set in metabase/.env${NC}"
        exit 1
    fi
done

# Set default domain if not provided
if [ -z "$METABASE_DOMAIN" ]; then
    METABASE_DOMAIN="metabase.yourdomain.com"
    echo -e "${YELLOW}⚠️  METABASE_DOMAIN not set, using default: $METABASE_DOMAIN${NC}"
fi

echo -e "${GREEN}🚀 Deploying Metabase VM...${NC}"
echo -e "${BLUE}📦 Project: $PROJECT_ID${NC}"
echo -e "${BLUE}🖥️  VM Name: $VM_NAME${NC}"
echo -e "${BLUE}📍 Zone: $ZONE${NC}"
echo -e "${BLUE}⚙️  Machine Type: $MACHINE_TYPE${NC}"
echo -e "${BLUE}🌐 Domain: $METABASE_DOMAIN${NC}"

# Check if VM already exists
if gcloud compute instances describe $VM_NAME --zone=$ZONE &>/dev/null; then
    echo -e "${YELLOW}⚠️  VM $VM_NAME already exists${NC}"
    read -p "Do you want to delete and recreate it? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🗑️  Deleting existing VM...${NC}"
        gcloud compute instances delete $VM_NAME --zone=$ZONE --quiet
    else
        echo -e "${BLUE}ℹ️  Updating existing VM...${NC}"
        # Create temporary files for metadata update
        TEMP_DIR=$(mktemp -d)
        cp metabase/.env "$TEMP_DIR/metabase-env"
        cp metabase/docker-compose.yml "$TEMP_DIR/docker-compose"

        # Update metadata and restart
        gcloud compute instances add-metadata $VM_NAME \
          --zone=$ZONE \
          --metadata-from-file startup-script=metabase/vm-startup.sh,metabase-env="$TEMP_DIR/metabase-env",docker-compose="$TEMP_DIR/docker-compose" \
          --metadata metabase-domain="$METABASE_DOMAIN"

        # Clean up temporary files
        rm -rf "$TEMP_DIR"

        echo -e "${YELLOW}🔄 Restarting VM...${NC}"
        gcloud compute instances stop $VM_NAME --zone=$ZONE --quiet
        gcloud compute instances start $VM_NAME --zone=$ZONE --quiet

        VM_IP=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(networkInterfaces[0].accessConfigs[0].natIP)')
        echo -e "${GREEN}✅ VM updated and restarted!${NC}"
        echo -e "${BLUE}🔗 Metabase URL (direct): http://$VM_IP:3000${NC}"
        echo -e "${BLUE}🔗 Metabase URL (domain): https://$METABASE_DOMAIN${NC}"
        exit 0
    fi
fi

# Create temporary files for metadata
echo -e "${YELLOW}📝 Preparing metadata files...${NC}"
TEMP_DIR=$(mktemp -d)
cp metabase/.env "$TEMP_DIR/metabase-env"
cp metabase/docker-compose.yml "$TEMP_DIR/docker-compose"

# Create VM with startup script
echo -e "${YELLOW}🖥️  Creating VM instance...${NC}"
gcloud compute instances create $VM_NAME \
  --zone=$ZONE \
  --machine-type=$MACHINE_TYPE \
  --network-interface=network-tier=PREMIUM,stack-type=IPV4_ONLY,subnet=default \
  --maintenance-policy=TERMINATE \
  --provisioning-model=SPOT \
  --instance-termination-action=STOP \
  --service-account=default \
  --scopes=https://www.googleapis.com/auth/cloud-platform \
  --create-disk=auto-delete=yes,boot=yes,device-name=$VM_NAME,image=projects/debian-cloud/global/images/family/debian-12,mode=rw,size=20,type=projects/$PROJECT_ID/zones/$ZONE/diskTypes/pd-standard \
  --create-disk=auto-delete=yes,device-name=metabase-data,mode=rw,size=10,type=projects/$PROJECT_ID/zones/$ZONE/diskTypes/pd-standard \
  --metadata-from-file startup-script=metabase/vm-startup.sh,metabase-env="$TEMP_DIR/metabase-env",docker-compose="$TEMP_DIR/docker-compose" \
  --metadata metabase-domain="$METABASE_DOMAIN" \
  --tags=metabase-server \
  --reservation-affinity=any

# Clean up temporary files
rm -rf "$TEMP_DIR"

# Create firewall rules for Metabase
echo -e "${YELLOW}🔥 Creating firewall rules...${NC}"

# HTTP/HTTPS for domain access
if ! gcloud compute firewall-rules describe metabase-allow-http &>/dev/null; then
    gcloud compute firewall-rules create metabase-allow-http \
      --allow tcp:80,tcp:443 \
      --source-ranges 0.0.0.0/0 \
      --target-tags metabase-server \
      --description "Allow HTTP/HTTPS for Metabase domain access"
    echo -e "${GREEN}✅ HTTP/HTTPS firewall rule created${NC}"
else
    echo -e "${GREEN}✅ HTTP/HTTPS firewall rule already exists${NC}"
fi

# Direct port access (optional, for troubleshooting)
if ! gcloud compute firewall-rules describe metabase-allow-3000 &>/dev/null; then
    gcloud compute firewall-rules create metabase-allow-3000 \
      --allow tcp:3000 \
      --source-ranges 0.0.0.0/0 \
      --target-tags metabase-server \
      --description "Allow direct Metabase access on port 3000"
    echo -e "${GREEN}✅ Direct access firewall rule created${NC}"
else
    echo -e "${GREEN}✅ Direct access firewall rule already exists${NC}"
fi

# Wait for VM to be ready
echo -e "${YELLOW}⏳ Waiting for VM to be ready...${NC}"
sleep 30

# Get VM IP
VM_IP=$(gcloud compute instances describe $VM_NAME --zone=$ZONE --format='get(networkInterfaces[0].accessConfigs[0].natIP)')

echo -e "${GREEN}✅ VM deployed successfully!${NC}"
echo ""
echo -e "${GREEN}🎉 Deployment completed!${NC}"
echo -e "${BLUE}🔗 VM IP: $VM_IP${NC}"
echo -e "${BLUE}🔗 Metabase URL (direct): http://$VM_IP:3000${NC}"
echo -e "${BLUE}🔗 Metabase URL (domain): https://$METABASE_DOMAIN${NC}"
echo ""
echo -e "${YELLOW}📋 Next steps:${NC}"
echo -e "1. Wait 2-3 minutes for the startup script to complete"
echo -e "2. Visit https://$METABASE_DOMAIN to access Metabase"
echo -e "3. Create admin account during first setup"
echo -e "4. Add your production database connection"
echo -e "5. Verify DNS propagation if domain doesn't work immediately"
echo ""
echo -e "${YELLOW}💡 Useful commands:${NC}"
echo -e "• SSH to VM: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE${NC}"
echo -e "• View startup logs: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo tail -f /var/log/metabase-startup.log'${NC}"
echo -e "• Check Metabase status: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='sudo systemctl status metabase'${NC}"
echo -e "• View Metabase logs: ${BLUE}gcloud compute ssh $VM_NAME --zone=$ZONE --command='cd /opt/metabase && sudo docker-compose logs -f'${NC}"
