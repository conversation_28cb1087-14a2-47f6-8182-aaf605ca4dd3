#!/bin/bash

# Metabase Setup Summary
# This script shows the current status and links for the Metabase installation

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="http://*************:3000"

echo -e "${BLUE}📊 METABASE ANALYTICS - RESUMO COMPLETO${NC}"
echo -e "${BLUE}===========================================${NC}"
echo ""

# Load environment variables
source metabase/.env

echo -e "${GREEN}🎯 INFORMAÇÕES GERAIS${NC}"
echo -e "• URL Principal: $METABASE_URL"
echo -e "• Domínio HTTP: http://$METABASE_DOMAIN"
echo -e "• Domínio HTTPS: https://$METABASE_DOMAIN"
echo -e "• VM IP: $(gcloud compute instances describe metabase --zone=us-central1-a --format='get(networkInterfaces[0].accessConfigs[0].natIP)' 2>/dev/null || echo 'N/A')"
echo -e "• Status VM: $(gcloud compute instances describe metabase --zone=us-central1-a --format='get(status)' 2>/dev/null || echo 'N/A')"
echo -e "• SSL: ✅ Configurado com Let's Encrypt"
echo ""

echo -e "${GREEN}📈 DASHBOARD E ANÁLISES${NC}"
echo -e "• Dashboard Principal: $METABASE_URL/dashboard/2"
echo -e "• Coleção Analytics: $METABASE_URL/collection/5"
echo -e "• Total de Questions: 10"
echo -e "• Database ID: 2 (Izy DB)"
echo ""

echo -e "${GREEN}📊 QUESTIONS CRIADAS${NC}"
echo -e "1. Resumo Executivo da Plataforma"
echo -e "2. Ticket Médio Geral"
echo -e "3. Evolução do Ticket Médio"
echo -e "4. Performance Mensal - Crescimento"
echo -e "5. Companies Top Vendedoras - Receita Líquida"
echo -e "6. Produtos Mais Vendidos - Análise Completa"
echo -e "7. Categorias Mais Vendidas - Análise Completa"
echo -e "8. Top Usuários por Quantidade de Pedidos"
echo -e "9. Top Usuários por Valor Gasto"
echo -e "10. Ticket Médio e Lucratividade por Empresa"
echo ""

echo -e "${GREEN}🔧 COMANDOS ÚTEIS${NC}"
echo -e "• Status da VM: make metabase-status"
echo -e "• Logs do Metabase: make metabase-logs"
echo -e "• SSH para VM: make metabase-ssh"
echo -e "• Restart Metabase: make metabase-restart"
echo -e "• Status do Nginx: make metabase-nginx-status"
echo -e "• Teste de domínio: make metabase-domain-test"
echo ""

echo -e "${GREEN}💾 ARQUIVOS DE CONFIGURAÇÃO${NC}"
echo -e "• Configuração: metabase/.env"
echo -e "• Sessão atual: metabase/session.env"
echo -e "• Queries SQL: metabase/analytics-queries.sql"
echo -e "• Scripts de setup: metabase/*.sh"
echo ""

echo -e "${GREEN}🗄️ BANCO DE DADOS${NC}"
echo -e "• Host: $PROD_DB_HOST"
echo -e "• Database: $PROD_DB_NAME"
echo -e "• Usuário: $PROD_DB_USER"
echo -e "• Porta: $PROD_DB_PORT"
echo ""

echo -e "${YELLOW}📋 PRÓXIMOS PASSOS${NC}"
echo -e "1. ✅ DNS configurado no Cloudflare (DNS only)"
echo -e "2. ✅ SSL configurado com Let's Encrypt"
echo -e "3. ✅ Domínio funcionando: https://$METABASE_DOMAIN"
echo -e "4. Configurar alertas e notificações"
echo -e "5. Criar dashboards adicionais conforme necessário"
echo -e "6. Configurar backup automático dos dados"
echo ""

echo -e "${BLUE}🎉 SETUP COMPLETO E FUNCIONAL!${NC}"
