# Guia de Configuração do Metabase - Métricas Izy Mercado

## 🎯 Métricas a Serem Criadas

1. **Produtos mais vendidos**
2. **Categorias de produtos mais vendidos**
3. **Companies que mais vendem**
4. **Usuários que mais compram (quantidade e valor)**
5. **Ticket médio dos pedidos**
6. **Resumo executivo geral** (com receita real da plataforma)
7. **Performance mensal comparativa**
8. **Receita da plataforma por mês** (comissão detalhada)
9. **Análise de impacto dos cupons na receita**
10. **Ranking de lucratividade das empresas**
11. **Análise de margem por faixa de ticket**

## ✨ **Melhorias Implementadas:**
- ✅ **Valores formatados em BRL** (R$ 1.234,56)
- ✅ **Nomes de campos em português** para facilitar análise
- ✅ **Datas formatadas** (DD/MM/YYYY e MM/YYYY)
- ✅ **Receita real da plataforma** calculada corretamente (comissão de 15%)
- ✅ **Receita líquida das empresas** (após desconto da comissão)
- ✅ **Regras de cupons** aplicadas corretamente (admin vs company)
- ✅ **Análise de lucratividade** completa para empresas, produtos e categorias
- ✅ **Margem líquida** calculada para diferentes faixas de ticket

## 📋 Passo a Passo

### **1. Acessar o Metabase**
- URL: http://***********:3000
- Criar conta de administrador no primeiro acesso

### **2. Conectar ao Banco de Produção**

1. **Ir para Admin > Databases**
2. **Clicar em "Add database"**
3. **Configurar conexão PostgreSQL:**
   - **Database type**: PostgreSQL
   - **Name**: Izy Mercado Production
   - **Host**: `***********` (IP interno da produção via VPC)
   - **Port**: `5432`
   - **Database name**: `izy-mercado-db`
   - **Username**: `metabase_readonly`
   - **Password**: `2Euk1e[X%A>P}<nN` (conforme .env)

4. **Testar conexão** e salvar

### **3. Criar as Questions (Métricas)**

Para cada métrica, siga estes passos:

#### **A. Produtos Mais Vendidos (com Lucratividade)**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 1)
3. **Nome**: "Produtos Mais Vendidos - Análise Completa"
4. **Descrição**: "Top 50 produtos com receita bruta, comissão e receita líquida"
5. **Salvar** na collection "Analytics"

#### **B. Categorias Mais Vendidas (com Lucratividade)**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 2)
3. **Nome**: "Categorias Mais Vendidas - Análise Completa"
4. **Descrição**: "Categorias com receita bruta, comissão gerada e receita líquida"
5. **Salvar** na collection "Analytics"

#### **C. Companies que Mais Vendem (com Lucratividade)**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 3)
3. **Nome**: "Companies Top Vendedoras - Receita Líquida"
4. **Descrição**: "Empresas ordenadas por receita líquida (após comissão)"
5. **Salvar** na collection "Analytics"

#### **D. Usuários que Mais Compram (Quantidade)**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 4)
3. **Nome**: "Top Usuários por Quantidade de Pedidos"
4. **Descrição**: "Usuários ordenados por número de pedidos realizados"
5. **Salvar** na collection "Analytics"

#### **E. Usuários que Mais Compram (Valor)**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 5)
3. **Nome**: "Top Usuários por Valor Gasto"
4. **Descrição**: "Usuários ordenados por valor total gasto"
5. **Salvar** na collection "Analytics"

#### **F. Ticket Médio Geral**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 6)
3. **Nome**: "Ticket Médio Geral"
4. **Descrição**: "Estatísticas gerais do ticket médio"
5. **Salvar** na collection "Analytics"

#### **G. Ticket Médio por Mês**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 7)
3. **Nome**: "Evolução do Ticket Médio"
4. **Descrição**: "Ticket médio mensal ao longo do tempo"
5. **Salvar** na collection "Analytics"

#### **H. Ticket Médio e Lucratividade por Empresa**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 8)
3. **Nome**: "Ticket Médio e Lucratividade por Empresa"
4. **Descrição**: "Análise completa de ticket e margem por empresa (mín. 5 pedidos)"
5. **Salvar** na collection "Analytics"

#### **I. Resumo Executivo Geral**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 9)
3. **Nome**: "Resumo Executivo da Plataforma"
4. **Descrição**: "Visão geral das principais métricas da plataforma"
5. **Salvar** na collection "Analytics"

#### **J. Performance Mensal Comparativa**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 10)
3. **Nome**: "Performance Mensal - Crescimento"
4. **Descrição**: "Comparativo mensal com percentuais de crescimento"
5. **Salvar** na collection "Analytics"

#### **K. Receita da Plataforma por Mês**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 11)
3. **Nome**: "Receita da Plataforma - Mensal"
4. **Descrição**: "Receita real da plataforma (comissão) mês a mês"
5. **Salvar** na collection "Analytics"

#### **L. Análise de Impacto dos Cupons**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 12)
3. **Nome**: "Impacto dos Cupons na Receita"
4. **Descrição**: "Como cupons admin vs company afetam a receita da plataforma"
5. **Salvar** na collection "Analytics"

#### **M. Ranking de Lucratividade das Empresas**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 13)
3. **Nome**: "Ranking de Lucratividade - Empresas"
4. **Descrição**: "Ranking completo das empresas por receita líquida e margem"
5. **Salvar** na collection "Analytics"

#### **N. Análise de Margem por Faixa de Ticket**
1. **New > Question > Native Query**
2. **Copiar a query** do arquivo `analytics-queries.sql` (seção 14)
3. **Nome**: "Análise de Margem por Ticket"
4. **Descrição**: "Margem das empresas segmentada por faixa de valor do pedido"
5. **Salvar** na collection "Analytics"

### **4. Criar Dashboard**

1. **New > Dashboard**
2. **Nome**: "Izy Mercado - Analytics Overview"
3. **Adicionar as questions** criadas acima
4. **Organizar em layout** apropriado:
   - **Linha 1**: Resumo Executivo + Receita da Plataforma Mensal
   - **Linha 2**: Ranking de Lucratividade + Performance Mensal
   - **Linha 3**: Top Companies (Receita Líquida) + Impacto dos Cupons
   - **Linha 4**: Ticket Médio Geral + Evolução do Ticket Médio
   - **Linha 5**: Top Produtos (Lucratividade) + Top Categorias (Lucratividade)
   - **Linha 6**: Top Usuários (Valor) + Top Usuários (Quantidade)
   - **Linha 7**: Ticket e Lucratividade por Empresa + Análise de Margem por Ticket

### **5. Configurar Visualizações**

Para cada question, configure a visualização mais apropriada:

- **Produtos/Categorias/Companies**: **Table** ou **Bar Chart**
- **Usuários**: **Table** com formatação de valores em BRL
- **Ticket Médio Geral**: **Number** cards
- **Evolução Temporal**: **Line Chart**

### **6. Formatação de Valores**

**✅ Valores já formatados automaticamente!**
- **Valores monetários**: Já aparecem como "R$ 1.234,56"
- **Datas**: Já formatadas como "DD/MM/YYYY" e "MM/YYYY"
- **Campos**: Todos em português para facilitar análise

**Não é necessário configurar formatação adicional** - as queries já entregam os dados prontos para visualização.

### **7. Filtros e Parâmetros**

Adicionar filtros úteis:
- **Período de data** (created_at)
- **Status do pedido** (padrão: completed)
- **Empresa específica**
- **Categoria específica**

## 🔄 Atualizações Automáticas

- **Questions**: Configurar para atualizar a cada 1 hora
- **Dashboard**: Configurar para atualizar a cada 30 minutos

## 📊 Métricas Adicionais Sugeridas

Após configurar as métricas básicas, considere adicionar:

1. **Taxa de conversão por empresa**
2. **Produtos com maior margem**
3. **Análise de sazonalidade**
4. **Cohort analysis de usuários**
5. **Análise geográfica por estado/cidade**
6. **Performance de campanhas/cupons**

## 🔒 Permissões

- **Admin**: Acesso total
- **Analistas**: Apenas visualização do dashboard
- **Empresas**: Apenas dados da própria empresa (filtro automático)
