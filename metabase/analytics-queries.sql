-- =====================================================
-- QUERIES PARA MÉTRICAS NO METABASE
-- =====================================================
-- Use estas queries no Metabase para criar as métricas solicitadas
-- <PERSON><PERSON> as queries consideram apenas pedidos completados (status = 'completed')
-- Valores formatados em BRL e campos em português para facilitar análise

-- =====================================================
-- 1. PRODUTOS MAIS VENDIDOS (COM ANÁLISE DE LUCRATIVIDADE)
-- =====================================================
-- Esta query mostra os produtos mais vendidos considerando receita bruta e líquida
WITH product_profitability AS (
    SELECT
        ip.product_name,
        ip.product_brand,
        ip.product_ean,
        ip.product_external_id,
        ip.quantity,
        ip.unit_price,
        i.id as invoice_id,
        c.commission_rate,
        -- Receita bruta do produto
        (ip.quantity * ip.unit_price) as gross_revenue,
        -- Comissão sobre este produto (proporcional)
        ROUND((ip.quantity * ip.unit_price) * (c.commission_rate / 10000.0)) as commission_on_product,
        -- Receita líquida do produto (após comissão)
        (ip.quantity * ip.unit_price) - ROUND((ip.quantity * ip.unit_price) * (c.commission_rate / 10000.0)) as net_revenue
    FROM invoice_products ip
    JOIN invoices i ON ip.invoice_id = i.id
    JOIN companies c ON i.company_id = c.id
    WHERE i.status = 'completed'
)
SELECT
    product_name as "Nome do Produto",
    product_brand as "Marca",
    product_ean as "Código EAN",
    product_external_id as "ID do Produto",
    SUM(quantity) as "Quantidade Total Vendida",
    COUNT(DISTINCT invoice_id) as "Número de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(gross_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Bruta Total",
    CONCAT('R$ ', TO_CHAR(SUM(commission_on_product) / 100.0, 'FM999G999G999D00')) as "Comissão Total Gerada",
    CONCAT('R$ ', TO_CHAR(SUM(net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida (Empresas)",
    CONCAT('R$ ', TO_CHAR(AVG(unit_price) / 100.0, 'FM999G999G999D00')) as "Preço Médio Unitário",
    CONCAT('R$ ', TO_CHAR(SUM(gross_revenue) / NULLIF(SUM(quantity), 0) / 100.0, 'FM999G999G999D00')) as "Receita Média por Unidade",
    CONCAT(ROUND(AVG(commission_on_product * 100.0 / NULLIF(gross_revenue, 0)), 2), '%') as "Taxa Média de Comissão"
FROM product_profitability
GROUP BY product_name, product_brand, product_ean, product_external_id
ORDER BY SUM(quantity) DESC
LIMIT 50;

-- =====================================================
-- 2. CATEGORIAS DE PRODUTOS MAIS VENDIDAS (COM LUCRATIVIDADE)
-- =====================================================
-- Esta query mostra as categorias mais vendidas considerando receita e comissão
WITH category_profitability AS (
    SELECT
        c.name as category_name,
        c.external_id as category_external_id,
        ip.quantity,
        ip.unit_price,
        ip.product_external_id,
        i.id as invoice_id,
        comp.commission_rate,
        -- Receita bruta da categoria
        (ip.quantity * ip.unit_price) as gross_revenue,
        -- Comissão sobre produtos desta categoria
        ROUND((ip.quantity * ip.unit_price) * (comp.commission_rate / 10000.0)) as commission_on_category,
        -- Receita líquida da categoria (após comissão)
        (ip.quantity * ip.unit_price) - ROUND((ip.quantity * ip.unit_price) * (comp.commission_rate / 10000.0)) as net_revenue
    FROM invoice_products ip
    JOIN invoices i ON ip.invoice_id = i.id
    JOIN companies comp ON i.company_id = comp.id
    JOIN products p ON ip.product_id = p.id
    JOIN products_categories pc ON p.id = pc.product_id
    JOIN categories c ON pc.category_id = c.id
    WHERE i.status = 'completed'
)
SELECT
    category_name as "Nome da Categoria",
    category_external_id as "ID da Categoria",
    SUM(quantity) as "Quantidade Total Vendida",
    COUNT(DISTINCT invoice_id) as "Número de Pedidos",
    COUNT(DISTINCT product_external_id) as "Produtos Únicos Vendidos",
    CONCAT('R$ ', TO_CHAR(SUM(gross_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Bruta Total",
    CONCAT('R$ ', TO_CHAR(SUM(commission_on_category) / 100.0, 'FM999G999G999D00')) as "Comissão Gerada para Plataforma",
    CONCAT('R$ ', TO_CHAR(SUM(net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida (Empresas)",
    CONCAT('R$ ', TO_CHAR(AVG(unit_price) / 100.0, 'FM999G999G999D00')) as "Preço Médio dos Produtos",
    CONCAT(ROUND(AVG(commission_on_category * 100.0 / NULLIF(gross_revenue, 0)), 2), '%') as "Taxa Média de Comissão"
FROM category_profitability
GROUP BY category_name, category_external_id
ORDER BY SUM(quantity) DESC;

-- =====================================================
-- 3. COMPANIES QUE MAIS VENDEM
-- =====================================================
-- Esta query mostra as empresas que mais vendem por receita e lucro líquido
-- Considera a comissão de 15% paga para a plataforma
WITH company_revenue AS (
    SELECT
        i.company_id,
        i.id as invoice_id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        comp.commission_rate,
        -- Valor dos produtos sem frete (base para comissão)
        (i.amount - i.shipping_fee) as product_value,
        -- Calcular comissão paga à plataforma
        CASE
            -- Sem cupom: comissão normal
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
            -- Cupom admin: comissão reduzida
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount)
            -- Cupom company: comissão normal (empresa arca com desconto)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
        END as commission_paid_to_platform,
        -- Calcular receita líquida da empresa
        CASE
            -- Sem cupom: produtos - comissão + frete
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                (i.amount - i.shipping_fee) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
            -- Cupom admin: produtos - comissão_reduzida + frete
            WHEN i.discount > 0 THEN
                (i.amount - i.shipping_fee) - GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount) + i.shipping_fee
            -- Cupom company: (produtos - cupom) - comissão + frete
            ELSE
                (i.amount - i.shipping_fee - i.discount) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
        END as net_revenue
    FROM invoices i
    JOIN companies comp ON i.company_id = comp.id
    WHERE i.status = 'completed'
)
SELECT
    comp.name as "Nome da Empresa",
    comp.external_id as "ID da Empresa",
    comp.cnpj as "CNPJ",
    COUNT(DISTINCT cr.invoice_id) as "Total de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(cr.product_value) / 100.0, 'FM999G999G999D00')) as "Receita de Produtos",
    CONCAT('R$ ', TO_CHAR(SUM(cr.shipping_fee) / 100.0, 'FM999G999G999D00')) as "Receita de Frete",
    CONCAT('R$ ', TO_CHAR(SUM(cr.commission_paid_to_platform) / 100.0, 'FM999G999G999D00')) as "Comissão Paga à Plataforma",
    CONCAT('R$ ', TO_CHAR(SUM(cr.net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida da Empresa",
    CONCAT('R$ ', TO_CHAR(AVG(cr.product_value) / 100.0, 'FM999G999G999D00')) as "Ticket Médio",
    COALESCE(SUM(ip_summary.total_items), 0) as "Total de Itens Vendidos",
    COUNT(DISTINCT i.user_id) as "Clientes Únicos",
    CONCAT(ROUND(AVG(cr.commission_paid_to_platform * 100.0 / NULLIF(cr.product_value, 0)), 2), '%') as "Taxa Efetiva de Comissão"
FROM company_revenue cr
JOIN companies comp ON cr.company_id = comp.id
JOIN invoices i ON cr.invoice_id = i.id
LEFT JOIN (
    SELECT
        invoice_id,
        SUM(quantity) as total_items
    FROM invoice_products
    GROUP BY invoice_id
) ip_summary ON cr.invoice_id = ip_summary.invoice_id
GROUP BY comp.name, comp.external_id, comp.cnpj
ORDER BY SUM(cr.net_revenue) DESC;

-- =====================================================
-- 4. USUÁRIOS QUE MAIS COMPRAM (POR QUANTIDADE DE COMPRAS)
-- =====================================================
-- Esta query mostra os usuários que mais fazem pedidos
SELECT
    u.name as "Nome do Cliente",
    u.email as "E-mail",
    u.external_id as "ID do Cliente",
    COUNT(DISTINCT i.id) as "Número de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(i.amount) / 100.0, 'FM999G999G999D00')) as "Total Gasto",
    CONCAT('R$ ', TO_CHAR(AVG(i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio",
    COALESCE(SUM(ip_summary.total_items), 0) as "Total de Itens Comprados",
    TO_CHAR(MIN(i.created_at), 'DD/MM/YYYY') as "Data do Primeiro Pedido",
    TO_CHAR(MAX(i.created_at), 'DD/MM/YYYY') as "Data do Último Pedido",
    COUNT(DISTINCT i.company_id) as "Empresas Diferentes Compradas"
FROM invoices i
JOIN users u ON i.user_id = u.id
LEFT JOIN (
    SELECT
        invoice_id,
        SUM(quantity) as total_items
    FROM invoice_products
    GROUP BY invoice_id
) ip_summary ON i.id = ip_summary.invoice_id
WHERE i.status = 'completed'
GROUP BY u.name, u.email, u.external_id
ORDER BY COUNT(DISTINCT i.id) DESC
LIMIT 100;

-- =====================================================
-- 5. USUÁRIOS QUE MAIS COMPRAM (POR VALOR)
-- =====================================================
-- Esta query mostra os usuários que mais gastam em valor
SELECT
    u.name as "Nome do Cliente",
    u.email as "E-mail",
    u.external_id as "ID do Cliente",
    COUNT(DISTINCT i.id) as "Número de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(i.amount) / 100.0, 'FM999G999G999D00')) as "Total Gasto",
    CONCAT('R$ ', TO_CHAR(AVG(i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio",
    COALESCE(SUM(ip_summary.total_items), 0) as "Total de Itens Comprados",
    TO_CHAR(MIN(i.created_at), 'DD/MM/YYYY') as "Data do Primeiro Pedido",
    TO_CHAR(MAX(i.created_at), 'DD/MM/YYYY') as "Data do Último Pedido",
    COUNT(DISTINCT i.company_id) as "Empresas Diferentes Compradas"
FROM invoices i
JOIN users u ON i.user_id = u.id
LEFT JOIN (
    SELECT
        invoice_id,
        SUM(quantity) as total_items
    FROM invoice_products
    GROUP BY invoice_id
) ip_summary ON i.id = ip_summary.invoice_id
WHERE i.status = 'completed'
GROUP BY u.name, u.email, u.external_id
ORDER BY SUM(i.amount) DESC
LIMIT 100;

-- =====================================================
-- 6. TICKET MÉDIO DOS PEDIDOS
-- =====================================================
-- Esta query calcula o ticket médio geral e estatísticas dos pedidos
SELECT
    COUNT(DISTINCT i.id) as "Total de Pedidos Completados",
    CONCAT('R$ ', TO_CHAR(SUM(i.amount) / 100.0, 'FM999G999G999D00')) as "Receita Total",
    CONCAT('R$ ', TO_CHAR(AVG(i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio",
    CONCAT('R$ ', TO_CHAR(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Mediano",
    CONCAT('R$ ', TO_CHAR(MIN(i.amount) / 100.0, 'FM999G999G999D00')) as "Menor Pedido",
    CONCAT('R$ ', TO_CHAR(MAX(i.amount) / 100.0, 'FM999G999G999D00')) as "Maior Pedido",
    CONCAT('R$ ', TO_CHAR(STDDEV(i.amount) / 100.0, 'FM999G999G999D00')) as "Desvio Padrão"
FROM invoices i
WHERE i.status = 'completed';

-- =====================================================
-- 7. TICKET MÉDIO POR MÊS (TENDÊNCIA TEMPORAL)
-- =====================================================
-- Esta query mostra a evolução do ticket médio ao longo do tempo
SELECT
    TO_CHAR(DATE_TRUNC('month', i.created_at), 'MM/YYYY') as "Mês/Ano",
    DATE_TRUNC('month', i.created_at) as "Data do Mês",
    COUNT(DISTINCT i.id) as "Total de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(i.amount) / 100.0, 'FM999G999G999D00')) as "Receita Total",
    CONCAT('R$ ', TO_CHAR(AVG(i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio",
    COUNT(DISTINCT i.user_id) as "Clientes Únicos",
    COUNT(DISTINCT i.company_id) as "Empresas Ativas"
FROM invoices i
WHERE i.status = 'completed'
GROUP BY DATE_TRUNC('month', i.created_at)
ORDER BY DATE_TRUNC('month', i.created_at) DESC;

-- =====================================================
-- 8. TICKET MÉDIO E LUCRATIVIDADE POR EMPRESA
-- =====================================================
-- Esta query mostra o ticket médio e lucratividade de cada empresa (mínimo 5 pedidos)
WITH company_profitability AS (
    SELECT
        i.company_id,
        i.id as invoice_id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        comp.commission_rate,
        comp.name as company_name,
        comp.external_id as company_external_id,
        -- Valor dos produtos sem frete
        (i.amount - i.shipping_fee) as product_value,
        -- Comissão paga à plataforma
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
        END as commission_paid,
        -- Receita líquida da empresa
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                (i.amount - i.shipping_fee) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
            WHEN i.discount > 0 THEN
                (i.amount - i.shipping_fee) - GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount) + i.shipping_fee
            ELSE
                (i.amount - i.shipping_fee - i.discount) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
        END as net_revenue
    FROM invoices i
    JOIN companies comp ON i.company_id = comp.id
    WHERE i.status = 'completed'
)
SELECT
    company_name as "Nome da Empresa",
    company_external_id as "ID da Empresa",
    COUNT(DISTINCT invoice_id) as "Total de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(amount) / 100.0, 'FM999G999G999D00')) as "Volume Total de Vendas",
    CONCAT('R$ ', TO_CHAR(SUM(commission_paid) / 100.0, 'FM999G999G999D00')) as "Comissão Paga",
    CONCAT('R$ ', TO_CHAR(SUM(net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida",
    CONCAT('R$ ', TO_CHAR(AVG(amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Bruto",
    CONCAT('R$ ', TO_CHAR(AVG(net_revenue) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Líquido",
    CONCAT('R$ ', TO_CHAR(PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY amount) / 100.0, 'FM999G999G999D00')) as "Ticket Mediano Bruto",
    CONCAT(ROUND(AVG(commission_paid * 100.0 / NULLIF(product_value, 0)), 2), '%') as "Taxa Efetiva de Comissão",
    CONCAT(ROUND(AVG(net_revenue * 100.0 / NULLIF(amount, 0)), 2), '%') as "Margem Líquida (%)"
FROM company_profitability
GROUP BY company_name, company_external_id
HAVING COUNT(DISTINCT invoice_id) >= 5  -- Apenas empresas com pelo menos 5 pedidos
ORDER BY SUM(net_revenue) DESC;

-- =====================================================
-- 9. RESUMO EXECUTIVO GERAL
-- =====================================================
-- Esta query fornece um resumo executivo com as principais métricas
-- A receita da plataforma é calculada baseada na comissão real (15% sobre valor dos pedidos)
-- considerando as regras de cupons admin vs company
WITH platform_revenue AS (
    SELECT
        i.id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        c.commission_rate,
        -- Valor do pedido sem frete (base para cálculo da comissão)
        (i.amount - i.shipping_fee) as order_value_without_shipping,
        -- Calcular comissão baseada no tipo de cupom
        CASE
            -- Se não há cupom, comissão normal
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
            -- Se há cupom, precisamos verificar se é admin ou company
            -- Para simplificar, assumimos que cupons com desconto afetam a comissão (admin)
            -- e cupons sem desconto registrado são company
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0)) - i.discount)
            -- Cupom company (não afeta comissão)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
        END as platform_commission
    FROM invoices i
    JOIN companies c ON i.company_id = c.id
    WHERE i.status = 'completed'
)
SELECT
    'Resumo Geral da Plataforma' as "Métrica",
    COUNT(DISTINCT i.id) as "Total de Pedidos",
    COUNT(DISTINCT i.user_id) as "Total de Clientes",
    COUNT(DISTINCT i.company_id) as "Total de Empresas",
    COUNT(DISTINCT ip.product_external_id) as "Total de Produtos Vendidos",
    CONCAT('R$ ', TO_CHAR(SUM(i.amount) / 100.0, 'FM999G999G999D00')) as "Volume Total de Vendas",
    CONCAT('R$ ', TO_CHAR(SUM(pr.platform_commission) / 100.0, 'FM999G999G999D00')) as "Receita da Plataforma (Comissão)",
    CONCAT('R$ ', TO_CHAR(AVG(i.amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Geral",
    COALESCE(SUM(ip.quantity), 0) as "Total de Itens Vendidos",
    CONCAT(ROUND(AVG(pr.platform_commission * 100.0 / NULLIF(pr.order_value_without_shipping, 0)), 2), '%') as "Taxa Média de Comissão"
FROM invoices i
LEFT JOIN invoice_products ip ON i.id = ip.invoice_id
LEFT JOIN platform_revenue pr ON i.id = pr.id
WHERE i.status = 'completed';

-- =====================================================
-- 10. PERFORMANCE MENSAL COMPARATIVA
-- =====================================================
-- Esta query compara a performance do mês atual vs mês anterior
WITH monthly_stats AS (
    SELECT
        DATE_TRUNC('month', i.created_at) as month,
        COUNT(DISTINCT i.id) as orders,
        SUM(i.amount) / 100.0 as revenue,
        COUNT(DISTINCT i.user_id) as customers
    FROM invoices i
    WHERE i.status = 'completed'
    GROUP BY DATE_TRUNC('month', i.created_at)
),
current_vs_previous AS (
    SELECT
        TO_CHAR(month, 'MM/YYYY') as "Mês",
        orders as "Pedidos",
        CONCAT('R$ ', TO_CHAR(revenue, 'FM999G999G999D00')) as "Receita",
        customers as "Clientes",
        LAG(orders) OVER (ORDER BY month) as prev_orders,
        LAG(revenue) OVER (ORDER BY month) as prev_revenue,
        LAG(customers) OVER (ORDER BY month) as prev_customers
    FROM monthly_stats
)
SELECT
    "Mês",
    "Pedidos",
    "Receita",
    "Clientes",
    CASE
        WHEN prev_orders IS NOT NULL THEN
            CONCAT(ROUND(((("Pedidos"::numeric - prev_orders) / prev_orders) * 100), 1), '%')
        ELSE 'N/A'
    END as "Crescimento Pedidos (%)",
    CASE
        WHEN prev_revenue IS NOT NULL THEN
            CONCAT(ROUND((((REPLACE(REPLACE("Receita", 'R$ ', ''), '.', '')::numeric / 100 - prev_revenue) / prev_revenue) * 100), 1), '%')
        ELSE 'N/A'
    END as "Crescimento Receita (%)"
FROM current_vs_previous
ORDER BY "Mês" DESC
LIMIT 12;

-- =====================================================
-- 11. RECEITA DA PLATAFORMA POR MÊS (COMISSÃO DETALHADA)
-- =====================================================
-- Esta query mostra a receita real da plataforma (comissão) mês a mês
WITH monthly_platform_revenue AS (
    SELECT
        DATE_TRUNC('month', i.created_at) as month,
        i.id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        c.commission_rate,
        -- Valor do pedido sem frete (base para cálculo da comissão)
        (i.amount - i.shipping_fee) as order_value_without_shipping,
        -- Calcular comissão baseada no tipo de cupom
        CASE
            -- Se não há cupom, comissão normal
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
            -- Se há cupom com desconto, assumimos que é admin (reduz comissão)
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0)) - i.discount)
            -- Cupom company (não afeta comissão)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
        END as platform_commission
    FROM invoices i
    JOIN companies c ON i.company_id = c.id
    WHERE i.status = 'completed'
)
SELECT
    TO_CHAR(month, 'MM/YYYY') as "Mês/Ano",
    month as "Data do Mês",
    COUNT(DISTINCT id) as "Total de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(amount) / 100.0, 'FM999G999G999D00')) as "Volume Total de Vendas",
    CONCAT('R$ ', TO_CHAR(SUM(platform_commission) / 100.0, 'FM999G999G999D00')) as "Receita da Plataforma",
    CONCAT('R$ ', TO_CHAR(SUM(shipping_fee) / 100.0, 'FM999G999G999D00')) as "Volume de Frete (Empresas)",
    CONCAT('R$ ', TO_CHAR(SUM(coupon_discount) / 100.0, 'FM999G999G999D00')) as "Total de Descontos",
    CONCAT(ROUND(AVG(platform_commission * 100.0 / NULLIF(order_value_without_shipping, 0)), 2), '%') as "Taxa Média de Comissão",
    ROUND(SUM(platform_commission) * 100.0 / NULLIF(SUM(amount), 0), 2) as "% Receita Plataforma do Volume Total"
FROM monthly_platform_revenue
GROUP BY month
ORDER BY month DESC
LIMIT 12;

-- =====================================================
-- 12. ANÁLISE DE IMPACTO DOS CUPONS NA RECEITA
-- =====================================================
-- Esta query analisa como os cupons afetam a receita da plataforma
WITH coupon_impact AS (
    SELECT
        i.id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        c.commission_rate,
        (i.amount - i.shipping_fee) as order_value_without_shipping,
        -- Comissão sem cupom (teórica)
        ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0)) as theoretical_commission,
        -- Comissão real considerando cupons
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0)) - i.discount)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (c.commission_rate / 10000.0))
        END as actual_commission,
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN 'Sem Cupom'
            WHEN i.discount > 0 THEN 'Cupom Admin (Reduz Comissão)'
            ELSE 'Cupom Company (Não Afeta Comissão)'
        END as coupon_type
    FROM invoices i
    JOIN companies c ON i.company_id = c.id
    WHERE i.status = 'completed'
)
SELECT
    coupon_type as "Tipo de Cupom",
    COUNT(*) as "Número de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(order_value_without_shipping) / 100.0, 'FM999G999G999D00')) as "Volume de Vendas",
    CONCAT('R$ ', TO_CHAR(SUM(theoretical_commission) / 100.0, 'FM999G999G999D00')) as "Comissão Teórica",
    CONCAT('R$ ', TO_CHAR(SUM(actual_commission) / 100.0, 'FM999G999G999D00')) as "Comissão Real",
    CONCAT('R$ ', TO_CHAR(SUM(theoretical_commission - actual_commission) / 100.0, 'FM999G999G999D00')) as "Perda de Receita",
    CONCAT('R$ ', TO_CHAR(SUM(coupon_discount) / 100.0, 'FM999G999G999D00')) as "Total de Descontos",
    ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as "% dos Pedidos"
FROM coupon_impact
GROUP BY coupon_type
ORDER BY COUNT(*) DESC;

-- =====================================================
-- 13. RANKING DE LUCRATIVIDADE DAS EMPRESAS
-- =====================================================
-- Esta query mostra o ranking das empresas por lucratividade líquida
WITH company_detailed_profitability AS (
    SELECT
        comp.id as company_id,
        comp.name as company_name,
        comp.external_id as company_external_id,
        comp.cnpj,
        comp.commission_rate,
        i.id as invoice_id,
        i.amount,
        i.shipping_fee,
        i.discount as coupon_discount,
        i.coupon,
        i.user_id,
        -- Valor dos produtos (base para comissão)
        (i.amount - i.shipping_fee) as product_value,
        -- Comissão paga à plataforma
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
            WHEN i.discount > 0 THEN
                GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount)
            ELSE
                ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0))
        END as commission_paid,
        -- Receita líquida da empresa
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                (i.amount - i.shipping_fee) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
            WHEN i.discount > 0 THEN
                (i.amount - i.shipping_fee) - GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount) + i.shipping_fee
            ELSE
                (i.amount - i.shipping_fee - i.discount) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
        END as net_revenue,
        -- Desconto de cupom company (pago pela empresa)
        CASE
            WHEN i.coupon IS NOT NULL AND i.coupon != '' AND i.discount = 0 THEN i.discount
            WHEN i.coupon IS NOT NULL AND i.coupon != '' AND i.discount > 0 THEN 0  -- Cupom admin
            ELSE 0
        END as company_coupon_cost
    FROM invoices i
    JOIN companies comp ON i.company_id = comp.id
    WHERE i.status = 'completed'
)
SELECT
    company_name as "Nome da Empresa",
    company_external_id as "ID da Empresa",
    cnpj as "CNPJ",
    COUNT(DISTINCT invoice_id) as "Total de Pedidos",
    COUNT(DISTINCT user_id) as "Clientes Únicos",
    CONCAT('R$ ', TO_CHAR(SUM(amount) / 100.0, 'FM999G999G999D00')) as "Volume Total de Vendas",
    CONCAT('R$ ', TO_CHAR(SUM(product_value) / 100.0, 'FM999G999G999D00')) as "Receita de Produtos",
    CONCAT('R$ ', TO_CHAR(SUM(shipping_fee) / 100.0, 'FM999G999G999D00')) as "Receita de Frete",
    CONCAT('R$ ', TO_CHAR(SUM(commission_paid) / 100.0, 'FM999G999G999D00')) as "Comissão Paga à Plataforma",
    CONCAT('R$ ', TO_CHAR(SUM(company_coupon_cost) / 100.0, 'FM999G999G999D00')) as "Custo de Cupons Próprios",
    CONCAT('R$ ', TO_CHAR(SUM(net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida Final",
    CONCAT('R$ ', TO_CHAR(AVG(net_revenue) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Líquido",
    CONCAT(ROUND(AVG(commission_paid * 100.0 / NULLIF(product_value, 0)), 2), '%') as "Taxa Efetiva de Comissão",
    CONCAT(ROUND(SUM(net_revenue) * 100.0 / NULLIF(SUM(amount), 0), 2), '%') as "Margem Líquida (%)",
    CONCAT(ROUND(SUM(commission_paid) * 100.0 / NULLIF(SUM(product_value), 0), 2), '%') as "% Comissão do Faturamento"
FROM company_detailed_profitability
GROUP BY company_name, company_external_id, cnpj
HAVING COUNT(DISTINCT invoice_id) >= 3  -- Mínimo 3 pedidos para análise
ORDER BY SUM(net_revenue) DESC
LIMIT 50;

-- =====================================================
-- 14. ANÁLISE DE MARGEM POR FAIXA DE TICKET
-- =====================================================
-- Esta query analisa a margem das empresas por faixa de valor de pedido
WITH ticket_analysis AS (
    SELECT
        i.id as invoice_id,
        i.amount,
        i.shipping_fee,
        i.discount,
        comp.commission_rate,
        comp.name as company_name,
        -- Classificação por faixa de ticket
        CASE
            WHEN i.amount <= 3000 THEN 'Até R$ 30,00'
            WHEN i.amount <= 5000 THEN 'R$ 30,01 - R$ 50,00'
            WHEN i.amount <= 10000 THEN 'R$ 50,01 - R$ 100,00'
            WHEN i.amount <= 20000 THEN 'R$ 100,01 - R$ 200,00'
            ELSE 'Acima de R$ 200,00'
        END as ticket_range,
        -- Receita líquida
        CASE
            WHEN i.coupon IS NULL OR i.coupon = '' THEN
                (i.amount - i.shipping_fee) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
            WHEN i.discount > 0 THEN
                (i.amount - i.shipping_fee) - GREATEST(0, ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) - i.discount) + i.shipping_fee
            ELSE
                (i.amount - i.shipping_fee - i.discount) - ROUND((i.amount - i.shipping_fee) * (comp.commission_rate / 10000.0)) + i.shipping_fee
        END as net_revenue
    FROM invoices i
    JOIN companies comp ON i.company_id = comp.id
    WHERE i.status = 'completed'
)
SELECT
    ticket_range as "Faixa de Ticket",
    COUNT(DISTINCT invoice_id) as "Número de Pedidos",
    CONCAT('R$ ', TO_CHAR(SUM(amount) / 100.0, 'FM999G999G999D00')) as "Volume Total",
    CONCAT('R$ ', TO_CHAR(SUM(net_revenue) / 100.0, 'FM999G999G999D00')) as "Receita Líquida Total",
    CONCAT('R$ ', TO_CHAR(AVG(amount) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Bruto",
    CONCAT('R$ ', TO_CHAR(AVG(net_revenue) / 100.0, 'FM999G999G999D00')) as "Ticket Médio Líquido",
    CONCAT(ROUND(AVG(net_revenue * 100.0 / NULLIF(amount, 0)), 2), '%') as "Margem Média (%)",
    ROUND(COUNT(DISTINCT invoice_id) * 100.0 / SUM(COUNT(DISTINCT invoice_id)) OVER(), 2) as "% do Total de Pedidos"
FROM ticket_analysis
GROUP BY ticket_range
ORDER BY MIN(amount);
