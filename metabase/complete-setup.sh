#!/bin/bash

# Complete Metabase Setup Script
# This is the ONLY script needed to setup Metabase from scratch
# It handles everything: VM creation, database connection, questions, dashboard, and visualizations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="http://*************:3000"

echo -e "${BLUE}🚀 COMPLETE METABASE SETUP - FROM ZERO TO HERO${NC}"
echo -e "${BLUE}================================================${NC}"

# Step 1: Check if VM exists and deploy if needed
echo -e "${YELLOW}📋 Step 1: Checking VM status...${NC}"
if ! gcloud compute instances describe metabase --zone=us-central1-a &>/dev/null; then
    echo -e "${YELLOW}🔧 VM not found. Deploying new VM...${NC}"
    cd metabase && ./deploy.sh
    echo -e "${YELLOW}⏳ Waiting 3 minutes for VM to be ready...${NC}"
    sleep 180
else
    echo -e "${GREEN}✅ VM already exists${NC}"
fi

# Step 2: Wait for Metabase to be ready
echo -e "${YELLOW}📋 Step 2: Waiting for Metabase to be ready...${NC}"
max_attempts=30
attempt=0
while [ $attempt -lt $max_attempts ]; do
    if curl -s "$METABASE_URL/api/health" &>/dev/null; then
        echo -e "${GREEN}✅ Metabase is ready!${NC}"
        break
    fi
    echo -e "${YELLOW}⏳ Waiting for Metabase... (attempt $((attempt+1))/$max_attempts)${NC}"
    sleep 10
    ((attempt++))
done

if [ $attempt -eq $max_attempts ]; then
    echo -e "${RED}❌ Metabase failed to start${NC}"
    exit 1
fi

# Step 3: Setup admin user and database
echo -e "${YELLOW}📋 Step 3: Setting up admin user and database...${NC}"
echo -e "${BLUE}Please follow these steps:${NC}"
echo -e "1. Open $METABASE_URL in your browser"
echo -e "2. Create admin account with:"
echo -e "   - Email: <EMAIL>"
echo -e "   - Password: IzyMetabase2024!"
echo -e "3. Skip the database setup for now"
echo -e "4. Open browser developer tools (F12)"
echo -e "5. Go to Application/Storage > Cookies"
echo -e "6. Find 'metabase.SESSION' cookie and copy its value"
echo ""
read -p "Enter your Metabase session token: " SESSION_TOKEN

if [ -z "$SESSION_TOKEN" ]; then
    echo -e "${RED}❌ Session token is required${NC}"
    exit 1
fi

# Save session
echo "export METABASE_SESSION_ID=\"$SESSION_TOKEN\"" > metabase/session.env

# Load environment
source metabase/.env
AUTH_HEADER="X-Metabase-Session: $SESSION_TOKEN"

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    curl -s -X "$method" \
         -H "Content-Type: application/json" \
         -H "$AUTH_HEADER" \
         -d "$data" \
         "$METABASE_URL$endpoint"
}

# Step 4: Add production database
echo -e "${YELLOW}📋 Step 4: Adding production database...${NC}"
database_response=$(api_call "POST" "/api/database" "{
    \"engine\": \"postgres\",
    \"name\": \"Izy Mercado Production\",
    \"details\": {
        \"host\": \"$PROD_DB_HOST\",
        \"port\": $PROD_DB_PORT,
        \"dbname\": \"$PROD_DB_NAME\",
        \"user\": \"$PROD_DB_USER\",
        \"password\": \"$PROD_DB_PASSWORD\",
        \"ssl\": false,
        \"tunnel-enabled\": false,
        \"schema-filters-type\": \"all\",
        \"json-unfolding\": false
    },
    \"auto_run_queries\": true,
    \"is_full_sync\": true
}")

database_id=$(echo "$database_response" | jq -r '.id' 2>/dev/null || echo "null")
if [ "$database_id" = "null" ]; then
    echo -e "${RED}❌ Failed to add database${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Database added (ID: $database_id)${NC}"

# Step 5: Create Analytics collection
echo -e "${YELLOW}📋 Step 5: Creating Analytics collection...${NC}"
collection_response=$(api_call "POST" "/api/collection" "{
    \"name\": \"Analytics\",
    \"description\": \"Análises de negócio do Izy Mercado\",
    \"color\": \"#509EE3\"
}")

collection_id=$(echo "$collection_response" | jq -r '.id' 2>/dev/null || echo "null")
if [ "$collection_id" = "null" ]; then
    echo -e "${RED}❌ Failed to create collection${NC}"
    exit 1
fi
echo -e "${GREEN}✅ Collection created (ID: $collection_id)${NC}"

# Update session file
echo "export METABASE_DATABASE_ID=\"$database_id\"" >> metabase/session.env
echo "export METABASE_COLLECTION_ID=\"$collection_id\"" >> metabase/session.env

echo -e "${YELLOW}⏳ Waiting for database sync...${NC}"
sleep 30

# Step 6: Create all questions and dashboard
echo -e "${YELLOW}📋 Step 6: Creating questions and dashboard...${NC}"
./metabase/create-analytics-questions.sh
./metabase/add-questions-to-dashboard.sh
./metabase/format-dashboard-visualizations.sh
./metabase/reorganize-dashboard-layout.sh

echo -e "${GREEN}🎉 COMPLETE SETUP FINISHED!${NC}"
echo -e "${BLUE}📊 Your Metabase is ready at: $METABASE_URL/dashboard/2${NC}"
