#!/bin/bash

# Format Dashboard with Better Visualizations
# This script updates each question with appropriate chart types and formatting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="http://*************:3000"

# Load session variables
source metabase/session.env

AUTH_HEADER="X-Metabase-Session: $METABASE_SESSION_ID"

echo -e "${BLUE}🎨 Formatting Dashboard Visualizations...${NC}"

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    curl -s -X "$method" \
         -H "Content-Type: application/json" \
         -H "$AUTH_HEADER" \
         -d "$data" \
         "$METABASE_URL$endpoint"
}

# Function to update question visualization
update_visualization() {
    local question_id="$1"
    local display_type="$2"
    local viz_settings="$3"
    local name="$4"
    
    echo -e "${YELLOW}🎨 Updating visualization for: $name${NC}"
    
    # Get current question
    current=$(api_call "GET" "/api/card/$question_id")
    
    # Update with new visualization
    response=$(api_call "PUT" "/api/card/$question_id" "{
        \"name\": \"$name\",
        \"description\": $(echo "$current" | jq -r '.description // ""' | jq -R .),
        \"collection_id\": $METABASE_COLLECTION_ID,
        \"dataset_query\": $(echo "$current" | jq '.dataset_query'),
        \"display\": \"$display_type\",
        \"visualization_settings\": $viz_settings
    }")
    
    updated_id=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "null")
    
    if [ "$updated_id" = "null" ]; then
        echo -e "${RED}❌ Failed to update visualization for: $name${NC}"
        return 1
    else
        echo -e "${GREEN}✅ Updated visualization for: $name${NC}"
        return 0
    fi
}

echo -e "${YELLOW}📊 Configuring visualizations for each question...${NC}"

# 1. Resumo Executivo (ID: 46) - Scalar/Number cards
update_visualization 46 "scalar" '{
    "scalar.field": "Total de Pedidos",
    "card.title": "📊 Resumo Executivo",
    "card.description": "Principais métricas da plataforma"
}' "📊 Resumo Executivo da Plataforma"

# 2. Produtos Mais Vendidos (ID: 38) - Horizontal Bar Chart
update_visualization 38 "bar" '{
    "graph.dimensions": ["Nome do Produto"],
    "graph.metrics": ["Quantidade Total Vendida"],
    "graph.x_axis.title_text": "Quantidade Vendida",
    "graph.y_axis.title_text": "Produtos",
    "graph.show_values": true,
    "card.title": "🏆 Top Produtos Mais Vendidos"
}' "🏆 Top Produtos Mais Vendidos"

# 3. Categorias Mais Vendidas (ID: 39) - Pie Chart
update_visualization 39 "pie" '{
    "pie.dimension": "Categoria",
    "pie.metric": "Quantidade Total Vendida",
    "pie.show_legend": true,
    "pie.show_total": true,
    "card.title": "🥧 Categorias Mais Vendidas"
}' "🥧 Categorias Mais Vendidas"

# 4. Companies Top Vendedoras (ID: 40) - Bar Chart
update_visualization 40 "bar" '{
    "graph.dimensions": ["Nome da Empresa"],
    "graph.metrics": ["Total de Pedidos"],
    "graph.x_axis.title_text": "Número de Pedidos",
    "graph.y_axis.title_text": "Empresas",
    "graph.show_values": true,
    "card.title": "🏢 Top Empresas Vendedoras"
}' "🏢 Top Empresas Vendedoras"

# 5. Top Usuários por Quantidade (ID: 41) - Table (mais apropriado para dados de usuários)
update_visualization 41 "table" '{
    "table.pivot_column": null,
    "table.cell_column": null,
    "card.title": "👥 Top Usuários por Pedidos"
}' "👥 Top Usuários por Pedidos"

# 6. Top Usuários por Valor (ID: 42) - Table
update_visualization 42 "table" '{
    "table.pivot_column": null,
    "table.cell_column": null,
    "card.title": "💰 Top Usuários por Valor"
}' "💰 Top Usuários por Valor"

# 7. Ticket Médio Geral (ID: 43) - Scalar/Number
update_visualization 43 "scalar" '{
    "scalar.field": "Ticket Médio",
    "card.title": "🎯 Ticket Médio Geral"
}' "🎯 Ticket Médio Geral"

# 8. Evolução do Ticket Médio (ID: 44) - Line Chart
update_visualization 44 "line" '{
    "graph.dimensions": ["Mês"],
    "graph.metrics": ["Total de Pedidos"],
    "graph.x_axis.title_text": "Mês",
    "graph.y_axis.title_text": "Pedidos",
    "graph.show_values": false,
    "line.interpolate": "linear",
    "card.title": "📈 Evolução Mensal"
}' "📈 Evolução Mensal"

# 9. Ticket Médio por Empresa (ID: 45) - Table (dados complexos)
update_visualization 45 "table" '{
    "table.pivot_column": null,
    "table.cell_column": null,
    "card.title": "💼 Análise por Empresa"
}' "💼 Análise por Empresa"

# 10. Performance Mensal (ID: 47) - Line Chart
update_visualization 47 "line" '{
    "graph.dimensions": ["Mês"],
    "graph.metrics": ["Pedidos"],
    "graph.x_axis.title_text": "Mês",
    "graph.y_axis.title_text": "Crescimento",
    "graph.show_values": true,
    "line.interpolate": "linear",
    "card.title": "📊 Performance Mensal"
}' "📊 Performance Mensal"

echo -e "${GREEN}🎉 All visualizations updated!${NC}"
echo -e "${BLUE}🔗 Dashboard: $METABASE_URL/dashboard/2${NC}"
