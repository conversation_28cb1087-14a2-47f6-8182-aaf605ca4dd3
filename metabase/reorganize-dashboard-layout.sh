#!/bin/bash

# Reorganize Dashboard Layout for Better UX
# This script creates an intuitive layout for non-technical users

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="http://*************:3000"
DASHBOARD_ID="2"

# Load session variables
source metabase/session.env

AUTH_HEADER="X-Metabase-Session: $METABASE_SESSION_ID"

echo -e "${BLUE}🎨 Reorganizing Dashboard Layout...${NC}"

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    curl -s -X "$method" \
         -H "Content-Type: application/json" \
         -H "$AUTH_HEADER" \
         -d "$data" \
         "$METABASE_URL$endpoint"
}

echo -e "${YELLOW}📐 Creating optimized layout for business users...${NC}"

# New dashboard layout optimized for business insights
# Layout: 12 columns wide, responsive design
dashcards='[
    {
        "id": -1,
        "card_id": 46,
        "row": 0,
        "col": 0,
        "size_x": 12,
        "size_y": 3,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "📊 RESUMO EXECUTIVO",
            "card.description": "Principais métricas da plataforma"
        }
    },
    {
        "id": -2,
        "card_id": 43,
        "row": 3,
        "col": 0,
        "size_x": 4,
        "size_y": 3,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "🎯 TICKET MÉDIO"
        }
    },
    {
        "id": -3,
        "card_id": 44,
        "row": 3,
        "col": 4,
        "size_x": 8,
        "size_y": 3,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "📈 EVOLUÇÃO MENSAL"
        }
    },
    {
        "id": -4,
        "card_id": 38,
        "row": 6,
        "col": 0,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "🏆 TOP PRODUTOS"
        }
    },
    {
        "id": -5,
        "card_id": 39,
        "row": 6,
        "col": 6,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "🥧 CATEGORIAS POPULARES"
        }
    },
    {
        "id": -6,
        "card_id": 40,
        "row": 10,
        "col": 0,
        "size_x": 12,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "🏢 EMPRESAS TOP VENDEDORAS"
        }
    },
    {
        "id": -7,
        "card_id": 41,
        "row": 14,
        "col": 0,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "👥 CLIENTES MAIS ATIVOS"
        }
    },
    {
        "id": -8,
        "card_id": 42,
        "row": 14,
        "col": 6,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "💰 MAIORES COMPRADORES"
        }
    },
    {
        "id": -9,
        "card_id": 45,
        "row": 18,
        "col": 0,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "💼 ANÁLISE POR EMPRESA"
        }
    },
    {
        "id": -10,
        "card_id": 47,
        "row": 18,
        "col": 6,
        "size_x": 6,
        "size_y": 4,
        "parameter_mappings": [],
        "visualization_settings": {
            "card.title": "📊 CRESCIMENTO MENSAL"
        }
    }
]'

echo -e "${YELLOW}📊 Updating dashboard layout...${NC}"

# Update the dashboard with new layout and titles
response=$(api_call "PUT" "/api/dashboard/$DASHBOARD_ID" "{
    \"name\": \"📈 Izy Mercado - Dashboard Executivo\",
    \"description\": \"Painel executivo com as principais métricas de negócio da plataforma Izy Mercado. Atualizado automaticamente com dados em tempo real.\",
    \"dashcards\": $dashcards,
    \"parameters\": []
}")

# Check if the update was successful
dashboard_name=$(echo "$response" | jq -r '.name' 2>/dev/null || echo "null")

if [ "$dashboard_name" = "null" ]; then
    echo -e "${RED}❌ Failed to update dashboard layout${NC}"
    echo "Response: $response"
    exit 1
fi

echo -e "${GREEN}✅ Dashboard layout updated successfully!${NC}"

# Count the number of dashcards
dashcard_count=$(echo "$response" | jq '.dashcards | length' 2>/dev/null || echo "0")

echo -e "${GREEN}🎉 Dashboard reorganization completed!${NC}"
echo -e "${BLUE}📋 New Layout Summary:${NC}"
echo -e "- Dashboard Name: $dashboard_name"
echo -e "- Total Cards: $dashcard_count"
echo -e "- Layout: Optimized for business users"
echo ""
echo -e "${YELLOW}📊 Dashboard Structure:${NC}"
echo -e "┌─ Row 1: 📊 RESUMO EXECUTIVO (full width)"
echo -e "├─ Row 2: 🎯 TICKET MÉDIO | 📈 EVOLUÇÃO MENSAL"
echo -e "├─ Row 3: 🏆 TOP PRODUTOS | 🥧 CATEGORIAS POPULARES"
echo -e "├─ Row 4: 🏢 EMPRESAS TOP VENDEDORAS (full width)"
echo -e "├─ Row 5: 👥 CLIENTES MAIS ATIVOS | 💰 MAIORES COMPRADORES"
echo -e "└─ Row 6: 💼 ANÁLISE POR EMPRESA | 📊 CRESCIMENTO MENSAL"
echo ""
echo -e "${BLUE}🔗 Access your dashboard at: $METABASE_URL/dashboard/$DASHBOARD_ID${NC}"
