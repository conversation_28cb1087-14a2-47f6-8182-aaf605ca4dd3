#!/bin/bash

# Configure SSL for Metabase with Let's Encrypt
# This script sets up HTTPS with automatic certificate renewal

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN="data.izymercado.com.br"
EMAIL="<EMAIL>"

echo -e "${BLUE}🔒 Configuring SSL for Metabase...${NC}"

# Function to run commands on the VM
run_on_vm() {
    gcloud compute ssh metabase --zone=us-central1-a --command="$1"
}

echo -e "${YELLOW}📋 Step 1: Installing Certbot...${NC}"
run_on_vm "sudo apt update && sudo apt install -y certbot python3-certbot-nginx"

echo -e "${YELLOW}📋 Step 2: Obtaining SSL certificate...${NC}"
run_on_vm "sudo certbot --nginx -d $DOMAIN --email $EMAIL --agree-tos --non-interactive --redirect"

echo -e "${YELLOW}📋 Step 3: Testing certificate renewal...${NC}"
run_on_vm "sudo certbot renew --dry-run"

echo -e "${YELLOW}📋 Step 4: Configuring automatic renewal...${NC}"
run_on_vm "sudo systemctl enable certbot.timer && sudo systemctl start certbot.timer"

echo -e "${YELLOW}📋 Step 5: Testing Nginx configuration...${NC}"
run_on_vm "sudo nginx -t && sudo systemctl reload nginx"

echo -e "${GREEN}🎉 SSL configuration completed!${NC}"
echo -e "${BLUE}🔗 Your site is now available at: https://$DOMAIN${NC}"
echo -e "${YELLOW}📋 Certificate will auto-renew every 90 days${NC}"
