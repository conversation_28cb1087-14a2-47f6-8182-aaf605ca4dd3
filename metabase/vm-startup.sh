#!/bin/bash

# VM Startup Script for Metabase
# This script will be executed when the VM starts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting Metabase VM initialization...${NC}"

# Update system
echo -e "${BLUE}📦 Updating system packages...${NC}"
apt-get update
apt-get upgrade -y

# Install Docker
echo -e "${BLUE}🐳 Installing Docker...${NC}"
if ! command -v docker &> /dev/null; then
    # Install Docker
    apt-get install -y ca-certificates curl gnupg lsb-release
    mkdir -p /etc/apt/keyrings
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null
    apt-get update
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
    
    # Start and enable Docker
    systemctl start docker
    systemctl enable docker
    
    echo -e "${GREEN}✅ Docker installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker already installed${NC}"
fi

# Install Docker Compose (standalone)
echo -e "${BLUE}🔧 Installing Docker Compose...${NC}"
if ! command -v docker-compose &> /dev/null; then
    curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    chmod +x /usr/local/bin/docker-compose
    echo -e "${GREEN}✅ Docker Compose installed successfully${NC}"
else
    echo -e "${GREEN}✅ Docker Compose already installed${NC}"
fi

# Install additional tools
echo -e "${BLUE}🛠️ Installing additional tools...${NC}"
apt-get install -y curl wget htop nano git unzip nginx

# Create metabase directory
echo -e "${BLUE}📁 Setting up Metabase directory...${NC}"
mkdir -p /opt/metabase
cd /opt/metabase

# Download configuration files from metadata (if available)
echo -e "${BLUE}📥 Downloading configuration files...${NC}"

# Create .env file from metadata
if curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/metabase-env > /opt/metabase/.env 2>/dev/null; then
    echo -e "${GREEN}✅ Environment file downloaded from metadata${NC}"
else
    echo -e "${YELLOW}⚠️ Creating default environment file${NC}"
    cat > /opt/metabase/.env << 'EOF'
# Metabase Database Configuration
METABASE_DB_PASSWORD=secure_metabase_password_change_me

# Production Database Configuration (read-only access)
PROD_DB_HOST=your-production-db-host
PROD_DB_NAME=izy_mercado
PROD_DB_USER=metabase_readonly
PROD_DB_PASSWORD=your-readonly-password
PROD_DB_PORT=5432
EOF
fi

# Download docker-compose.yml from metadata or create default
if curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/docker-compose > /opt/metabase/docker-compose.yml 2>/dev/null; then
    echo -e "${GREEN}✅ Docker Compose file downloaded from metadata${NC}"
else
    echo -e "${YELLOW}⚠️ Docker Compose file not found in metadata${NC}"
    echo -e "${YELLOW}💡 Please upload docker-compose.yml manually${NC}"
fi

# Set proper permissions
chown -R root:root /opt/metabase
chmod 600 /opt/metabase/.env

# Create systemd service for auto-start
echo -e "${BLUE}⚙️ Creating systemd service...${NC}"
cat > /etc/systemd/system/metabase.service << 'EOF'
[Unit]
Description=Metabase Analytics
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/opt/metabase
ExecStart=/usr/local/bin/docker-compose up -d
ExecStop=/usr/local/bin/docker-compose down
TimeoutStartSec=0

[Install]
WantedBy=multi-user.target
EOF

# Enable and start the service
systemctl daemon-reload
systemctl enable metabase.service

# Configure Nginx reverse proxy
echo -e "${BLUE}🌐 Configuring Nginx reverse proxy...${NC}"

# Get domain from metadata or use default
METABASE_DOMAIN=$(curl -f -H "Metadata-Flavor: Google" http://metadata.google.internal/computeMetadata/v1/instance/attributes/metabase-domain 2>/dev/null || echo "metabase.yourdomain.com")

# Create Nginx configuration
cat > /etc/nginx/sites-available/metabase << EOF
server {
    listen 80;
    server_name ${METABASE_DOMAIN};

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";

        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
ln -sf /etc/nginx/sites-available/metabase /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
nginx -t

# Start and enable Nginx
systemctl start nginx
systemctl enable nginx

echo -e "${GREEN}✅ Nginx configured for domain: ${METABASE_DOMAIN}${NC}"

# Configure firewall
echo -e "${BLUE}🔥 Configuring firewall...${NC}"
ufw --force enable
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp
ufw allow 3000/tcp  # Keep for direct access if needed
ufw --force reload

echo -e "${GREEN}🎉 VM initialization completed!${NC}"
echo -e "${BLUE}📋 Next steps:${NC}"
echo -e "1. Configure /opt/metabase/.env with your database credentials"
echo -e "2. Upload docker-compose.yml to /opt/metabase/"
echo -e "3. Start Metabase: systemctl start metabase"
echo -e "4. Check status: systemctl status metabase"
echo -e "5. View logs: docker-compose -f /opt/metabase/docker-compose.yml logs -f"
echo -e "6. Access via: https://${METABASE_DOMAIN}"
echo -e "7. Check Nginx: systemctl status nginx"

# Log completion
echo "$(date): VM startup script completed" >> /var/log/metabase-startup.log
