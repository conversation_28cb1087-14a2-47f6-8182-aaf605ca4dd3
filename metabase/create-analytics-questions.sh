#!/bin/bash

# Create Analytics Questions in Metabase via API
# This script creates all the analytics questions from the setup guide

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
METABASE_URL="http://*************:3000"

# Load session variables
if [ ! -f "metabase/session.env" ]; then
    echo -e "${RED}❌ Session file not found. Run setup-metabase-api.sh first${NC}"
    exit 1
fi

source metabase/session.env

AUTH_HEADER="X-Metabase-Session: $METABASE_SESSION_ID"

echo -e "${BLUE}🚀 Creating Analytics Questions...${NC}"

# Function to make API calls
api_call() {
    local method=$1
    local endpoint=$2
    local data=$3
    
    curl -s -X "$method" \
         -H "Content-Type: application/json" \
         -H "$AUTH_HEADER" \
         -d "$data" \
         "$METABASE_URL$endpoint"
}

# Function to create a native question
create_question() {
    local name="$1"
    local description="$2"
    local sql="$3"
    
    echo -e "${YELLOW}📊 Creating question: $name${NC}"
    
    response=$(api_call "POST" "/api/card" "{
        \"name\": \"$name\",
        \"description\": \"$description\",
        \"collection_id\": $METABASE_COLLECTION_ID,
        \"dataset_query\": {
            \"type\": \"native\",
            \"native\": {
                \"query\": \"$sql\"
            },
            \"database\": $METABASE_DATABASE_ID
        },
        \"display\": \"table\",
        \"visualization_settings\": {}
    }")
    
    question_id=$(echo "$response" | jq -r '.id' 2>/dev/null || echo "null")
    
    if [ "$question_id" = "null" ]; then
        echo -e "${RED}❌ Failed to create question: $name${NC}"
        echo "Response: $response"
        return 1
    else
        echo -e "${GREEN}✅ Created question: $name (ID: $question_id)${NC}"
        return 0
    fi
}

# Read SQL queries from file
QUERIES_FILE="metabase/analytics-queries.sql"

if [ ! -f "$QUERIES_FILE" ]; then
    echo -e "${RED}❌ Analytics queries file not found: $QUERIES_FILE${NC}"
    exit 1
fi

# Function to extract SQL query from file
extract_query() {
    local start_pattern="$1"
    local end_pattern="$2"

    # Extract the query between start and end patterns, remove comments and format for JSON
    sed -n "/$start_pattern/,/$end_pattern/p" "$QUERIES_FILE" | \
    sed '1d;$d' | \
    sed '/^--/d' | \
    sed '/^$/d' | \
    sed 's/"/\\"/g' | \
    tr '\n' ' ' | \
    sed 's/  */ /g' | \
    sed 's/^ *//;s/ *$//'
}

# Extract and create each query
echo -e "${YELLOW}📋 Reading queries from $QUERIES_FILE${NC}"

# Question 1: Produtos Mais Vendidos
echo -e "${YELLOW}🔍 Extracting query 1...${NC}"
sql_1=$(extract_query "-- 1\. PRODUTOS MAIS VENDIDOS" "-- =====")
create_question "Produtos Mais Vendidos - Análise Completa" "Top 50 produtos com receita bruta, comissão e receita líquida" "$sql_1"

# Question 2: Categorias Mais Vendidas
echo -e "${YELLOW}🔍 Extracting query 2...${NC}"
sql_2=$(extract_query "-- 2\. CATEGORIAS DE PRODUTOS MAIS VENDIDAS" "-- =====")
create_question "Categorias Mais Vendidas - Análise Completa" "Categorias com receita bruta, comissão gerada e receita líquida" "$sql_2"

# Question 3: Companies que Mais Vendem
echo -e "${YELLOW}🔍 Extracting query 3...${NC}"
sql_3=$(extract_query "-- 3\. COMPANIES QUE MAIS VENDEM" "-- =====")
create_question "Companies Top Vendedoras - Receita Líquida" "Empresas ordenadas por receita líquida (após comissão)" "$sql_3"

# Question 4: Usuários que Mais Compram (Quantidade)
echo -e "${YELLOW}🔍 Extracting query 4...${NC}"
sql_4=$(extract_query "-- 4\. USUÁRIOS QUE MAIS COMPRAM (POR QUANTIDADE" "-- =====")
create_question "Top Usuários por Quantidade de Pedidos" "Usuários ordenados por número de pedidos realizados" "$sql_4"

# Question 5: Usuários que Mais Compram (Valor)
echo -e "${YELLOW}🔍 Extracting query 5...${NC}"
sql_5=$(extract_query "-- 5\. USUÁRIOS QUE MAIS COMPRAM (POR VALOR)" "-- =====")
create_question "Top Usuários por Valor Gasto" "Usuários ordenados por valor total gasto" "$sql_5"

# Question 6: Ticket Médio Geral
echo -e "${YELLOW}🔍 Extracting query 6...${NC}"
sql_6=$(extract_query "-- 6\. TICKET MÉDIO DOS PEDIDOS" "-- =====")
create_question "Ticket Médio Geral" "Estatísticas gerais do ticket médio" "$sql_6"

# Question 7: Evolução do Ticket Médio
echo -e "${YELLOW}🔍 Extracting query 7...${NC}"
sql_7=$(extract_query "-- 7\. TICKET MÉDIO POR MÊS" "-- =====")
create_question "Evolução do Ticket Médio" "Ticket médio mensal ao longo do tempo" "$sql_7"

# Question 8: Ticket e Lucratividade por Empresa
echo -e "${YELLOW}🔍 Extracting query 8...${NC}"
sql_8=$(extract_query "-- 8\. TICKET MÉDIO E LUCRATIVIDADE POR EMPRESA" "-- =====")
create_question "Ticket Médio e Lucratividade por Empresa" "Análise completa de ticket e margem por empresa (mín. 5 pedidos)" "$sql_8"

# Question 9: Resumo Executivo
echo -e "${YELLOW}🔍 Extracting query 9...${NC}"
sql_9=$(extract_query "-- 9\. RESUMO EXECUTIVO GERAL" "-- =====")
create_question "Resumo Executivo da Plataforma" "Visão geral das principais métricas da plataforma" "$sql_9"

# Question 10: Performance Mensal
echo -e "${YELLOW}🔍 Extracting query 10...${NC}"
sql_10=$(extract_query "-- 10\. PERFORMANCE MENSAL COMPARATIVA" "-- =====")
create_question "Performance Mensal - Crescimento" "Comparativo mensal com percentuais de crescimento" "$sql_10"

echo -e "${GREEN}🎉 All analytics questions created successfully!${NC}"
echo -e "${BLUE}🔗 Access your questions at: $METABASE_URL/collection/$METABASE_COLLECTION_ID${NC}"
