# Partner Balance Withdrawal Feature

## Current Implementation Status

The partner balance withdrawal feature has been fully implemented on the frontend, but the backend API endpoints are **not yet implemented**. The frontend currently uses mock data for development and testing purposes.

## Frontend Implementation ✅

### Components Implemented:
- **WithdrawalModal**: Complete two-step withdrawal flow (amount selection → confirmation)
- **CompanyBillingTab**: Comprehensive billing management with balance display, withdrawal history, and statistics
- **Partner Dashboard Integration**: Updated revenue card to show withdrawal totals

### Features:
- ✅ Brazilian currency formatting (R$ X.XXX,XX)
- ✅ Real-time form validation with visual feedback
- ✅ Minimum withdrawal amount validation (R$ 500,00)
- ✅ Balance checking to prevent overdrafts
- ✅ Responsive design for all screen sizes
- ✅ Portuguese localization
- ✅ Error handling with fallback to mock data
- ✅ Loading states and user feedback

## Backend Implementation Required ❌

The following API endpoints need to be implemented in the backend:

### 1. GET /v1/company/balance
**Purpose**: Get current company balance and PIX information

**Response Format**:
```json
{
  "data": {
    "balance": 225000,        // Value in centavos (R$ 2.250,00)
    "name": "Company Name",   // Company/Partner name
    "pix_key": "74312689000126"  // PIX key for withdrawals
  }
}
```

**Security**: Requires authentication, partners can only access their own company balance

### 2. POST /v1/company/withdraw
**Purpose**: Process a withdrawal request

**Request Format**:
```json
{
  "amount": 50000  // Value in centavos (minimum 50000 = R$ 500,00)
}
```

**Response Format**:
```json
{
  "data": {
    "message": "Saque realizado com sucesso! O valor será processado em até 24 horas."
  }
}
```

**Validation**:
- Amount must be >= 50000 centavos (R$ 500,00)
- Amount must not exceed available balance
- User must be authenticated and own the company

### 3. GET /v1/company/withdrawals
**Purpose**: Get paginated withdrawal history

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

**Response Format**:
```json
{
  "data": {
    "withdrawals": [
      {
        "status": "CONFIRMED",           // Status: 'CREATED', 'CONFIRMED', 'FAILED'
        "value": 50000,                  // Value in centavos
        "correlation_id": "WITHDRAW_001", // Unique transaction ID
        "destination_alias": "74312689000126", // PIX destination
        "comment": "Saque realizado",    // Optional comment
        "end_to_end_id": "E18236120202012032010s01345689XBY", // PIX end-to-end ID
        "time": "2024-01-15T10:30:00.000Z",     // Transaction time
        "created_at": "2024-01-15T10:30:00.000Z", // Creation time
        "finished_at": "2024-01-15T10:35:00.000Z" // Optional: when withdrawal was 100% processed
      }
    ],
    "total_amount": 225000             // Total amount withdrawn in centavos
  },
  "limit": 10,
  "pageNumber": 1,
  "totalItems": 25,
  "totalPages": 3
}
```

**Note**: The `status` field is now implemented with values: 'CREATED', 'CONFIRMED', 'FAILED'. The `finished_at` field is optional and shows when a withdrawal was 100% processed.

## Current Mock Data Behavior

The frontend implementation includes intelligent fallback behavior:

1. **API Call Attempt**: Each method first tries to call the actual backend endpoint
2. **Error Handling**: If the endpoint returns 404 or 500, it falls back to mock data
3. **Console Warnings**: Developers see clear warnings when mock data is being used
4. **Realistic Simulation**: Mock data includes realistic delays and data structures

## Migration Path

When the backend endpoints are implemented:

1. **Test Endpoints**: Verify the endpoints work with tools like Postman
2. **Update Frontend**: Remove the try-catch blocks in `src/services/api.ts`
3. **Verify Integration**: Test the complete flow with real backend data
4. **Remove Mock Data**: Clean up mock data and console warnings

## Files Modified

### Core Implementation:
- `src/services/api.ts` - API service methods with fallback logic
- `src/types/api.ts` - TypeScript interfaces for API responses
- `src/components/WithdrawalModal.tsx` - Main withdrawal interface
- `src/components/CompanyBillingTab.tsx` - Billing management tab

### Integration Points:
- `src/pages/CompanyDetails.tsx` - Admin company details with billing tab
- `src/pages/PartnerCompanyDetails.tsx` - Partner company details with billing tab
- `src/pages/PartnerDashboard.tsx` - Updated revenue card

## Testing

The feature can be fully tested in development mode:

1. **Balance Display**: Shows mock balance of R$ 2.250,00
2. **Withdrawal Flow**: Complete two-step process with validation
3. **History Display**: Shows 3 mock completed withdrawals
4. **Statistics**: Calculates totals and averages from mock data
5. **Error Handling**: Graceful fallback when endpoints don't exist

## Security Considerations

When implementing the backend:

1. **Authentication**: All endpoints require valid JWT tokens
2. **Authorization**: Partners can only access their own company data
3. **Validation**: Server-side validation of all withdrawal amounts and limits
4. **Rate Limiting**: Consider implementing rate limits for withdrawal requests
5. **Audit Trail**: Log all withdrawal attempts for security monitoring

## Next Steps

1. **Backend Team**: Implement the three required API endpoints
2. **Database Schema**: Design tables for balance tracking and withdrawal history
3. **PIX Integration**: Integrate with PIX payment system for actual transfers
4. **Testing**: Comprehensive testing of the complete withdrawal flow
5. **Monitoring**: Set up monitoring and alerting for withdrawal operations
