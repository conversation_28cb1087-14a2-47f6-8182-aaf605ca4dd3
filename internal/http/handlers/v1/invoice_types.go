package handlers

import (
	"time"

	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
)

// UpdateStatusRequest represents the request body for status updates
type UpdateStatusRequest struct {
	Status string `json:"status" example:"processing"`
	Reason string `json:"reason,omitempty" example:"Customer requested cancellation"`
}

// InvoiceProductResponse represents a product in an invoice
type InvoiceProductResponse struct {
	Quantity   int32                    `json:"quantity" example:"2"`
	Price      int32                    `json:"price" example:"750"`
	Discount   int32                    `json:"discount" example:"50"`
	Name       string                   `json:"name" example:"Product Name"`
	Ean        string                   `json:"ean" example:"1234567890123"`
	ExternalID string                   `json:"external_id" example:"prod_abc123"`
	Brand      *string                  `json:"brand" example:"Brand Name"`
	Image      *string                  `json:"image" example:"https://example.com/image.jpg"`
	Categories []custom_models.Category `json:"categories" example:"[{\"name\": \"Category Name\", \"image\": \"https://example.com/category.jpg\", \"external_id\": \"cat_abc123\"}]"`
}

// InvoiceResponse represents an invoice in API responses
type InvoiceResponse struct {
	OrderID             string                      `json:"order_id" example:"order_abc123"`
	Status              string                      `json:"status" example:"processing"`
	StatusDescription   string                      `json:"status_description" example:"Pagamento aprovado - Aguardando aceite do supermercado"`
	PaymentMethod       string                      `json:"payment_method" example:"pix"`
	Amount              int32                       `json:"amount" example:"1500"`
	Discount            int32                       `json:"discount" example:"100"`
	ShippingFee         int32                       `json:"shipping_fee" example:"500"`
	DeliveryMode        string                      `json:"delivery_mode" example:"delivery"`
	Coupon              string                      `json:"coupon,omitempty" example:"SAVE10"`
	UserName            string                      `json:"user_name,omitempty" example:"Customer Name"`
	UserEmail           string                      `json:"user_email,omitempty" example:"<EMAIL>"`
	UserCpf             string                      `json:"user_cpf,omitempty" example:"79220436191"`
	UserAddress         string                      `json:"user_address,omitempty" example:"Rua Example, 123"`
	UserPhoneNumber     string                      `json:"user_phone_number,omitempty" example:"+5511999999999"`
	CompanyName         string                      `json:"company_name,omitempty" example:"Store Name"`
	CompanyCnpj         string                      `json:"company_cnpj,omitempty" example:"12345678000100"`
	CompanyAddress      custom_models.AddressParams `json:"company_address,omitempty"`
	CompanyBio          string                      `json:"company_bio,omitempty" example:"Store description"`
	CompanyPicture      string                      `json:"company_picture,omitempty" example:"https://example.com/store.jpg"`
	CompanyPhoneNumbers []string                    `json:"company_phone_numbers,omitempty" example:"[\"+5511999999999\"]"`
	CompanyPixKey       string                      `json:"company_pix_key,omitempty" example:"12345678000100"`
	CompanyExternalID   string                      `json:"company_external_id,omitempty" example:"comp_abc123"`
	Info                string                      `json:"info,omitempty" example:"Additional information"`
	BrCode              *string                     `json:"br_code,omitempty" example:"00020126580014br.gov.bcb.pix..."`
	QrCodeImage         *string                     `json:"qr_code_image,omitempty" example:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."`
	FinishedAt          *time.Time                  `json:"finished_at,omitempty" example:"2024-01-01T15:30:00Z"`
	CreatedAt           time.Time                   `json:"created_at" example:"2024-01-01T10:00:00Z"`
	UpdatedAt           time.Time                   `json:"updated_at" example:"2024-01-01T10:30:00Z"`
	Products            []InvoiceProductResponse    `json:"products"`
}

// GetInvoicesResponse represents the response for getting invoices
type GetInvoicesResponse struct {
	Invoices []InvoiceResponse `json:"invoices"`
	Count    int               `json:"count" example:"5"`
}

// UpdateInvoiceStatusResponse represents the response for updating invoice status
type UpdateInvoiceStatusResponse struct {
	Message           string `json:"message" example:"Invoice status updated successfully"`
	OrderID           string `json:"order_id" example:"order_abc123"`
	Status            string `json:"status" example:"processing"`
	StatusDescription string `json:"status_description" example:"Pagamento aprovado - Aguardando aceite do supermercado"`
}

type GetInvoicesSuccessResponse = common.SuccessResponseWithPagination[[]InvoiceResponse]
type GetInvoiceByOrderIDSuccessResponse = common.SuccessResponse[InvoiceResponse]
type UpdateInvoiceStatusSuccessResponse = common.SuccessResponse[UpdateInvoiceStatusResponse]
