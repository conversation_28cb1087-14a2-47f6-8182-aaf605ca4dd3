package handlers

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"slices"
	"strconv"
	"strings"
	"time"

	"log"

	"github.com/go-chi/chi/v5"
	"github.com/izy-mercado/backend/internal/config"
	"github.com/izy-mercado/backend/internal/fcm"
	"github.com/izy-mercado/backend/internal/helpers"
	"github.com/izy-mercado/backend/internal/http/common"
	"github.com/izy-mercado/backend/internal/integrations/payment/woovi"
	"github.com/izy-mercado/backend/internal/integrations/storage"
	"github.com/izy-mercado/backend/internal/middlewares"
	"github.com/izy-mercado/backend/pkg/storage/postgres"
	"github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
	"github.com/jackc/pgx/v4/pgxpool"
)

const (
	// MaxDistanceKm defines the maximum distance in kilometers for location-based company filtering
	MaxDistanceKm = 10.0
)

type CompanyHandler struct {
	queries    *postgres.Queries
	storage    *storage.Storage
	woovi      *woovi.Woovi
	pool       *pgxpool.Pool
	env        *config.Environment
	fcmService *fcm.Service
}

type CreateCompanyRequest struct {
	Name           string                      `form:"name" validate:"required" example:"Mercadinho Caicó"`
	Cnpj           string                      `form:"cnpj" validate:"required" example:"74312689000126"`
	Image          *multipart.FileHeader       `form:"image" validate:"required" example:"https://images.unsplash.com/photo-1612838320302-7b3b3b3b3b3b"`
	Bio            string                      `form:"bio" validate:"required" example:"Mercadinho Caicó é o melhor mercadinho da região"`
	DeliveryModes  []string                    `form:"delivery_modes[]" example:"delivery,pickup" validate:"required"`
	ShippingFee    int32                       `form:"shipping_fee" example:"500"`
	CommissionRate int32                       `form:"commission_rate" example:"10"`
	CashbackRate   int32                       `form:"cashback_rate" example:"10"`
	Address        custom_models.AddressParams `form:"address" validate:"required"`
	PhoneNumbers   []string                    `form:"phone_numbers" example:"***********" validate:"required,dive,e164"`
	PixKey         string                      `form:"pix_key" example:"74312689000126" validate:"required"`
}

type UpdateCompanyDataRequest struct {
	Bio           string                      `json:"bio" validate:"required" example:"Mercadinho Caicó é o melhor mercadinho da região"`
	DeliveryModes []string                    `json:"delivery_modes" example:"delivery,pickup" validate:"required,dive,oneof=delivery pickup"`
	ShippingFee   int32                       `json:"shipping_fee" example:"500"`
	PhoneNumbers  []string                    `json:"phone_numbers" example:"***********" validate:"required,dive,e164"`
	Address       custom_models.AddressParams `json:"address" validate:"required"`
}

type AddProductsToCompanyRequest struct {
	*custom_models.CompanyProduct `validate:"required"`
}

type CreateSubAccountRequest struct {
	Name   string `json:"name" validate:"required"`
	PixKey string `json:"pix_key" validate:"required"`
}

type GetActiveCompaniesResponse struct {
	Name          string                      `json:"name"`
	Cnpj          string                      `json:"cnpj"`
	Bio           string                      `json:"bio"`
	Picture       string                      `json:"picture"`
	Address       custom_models.AddressParams `json:"address"`
	PhoneNumbers  []string                    `json:"phone_numbers"`
	Products      []custom_models.Product     `json:"products"`
	ExternalID    string                      `json:"external_id"`
	IsActive      bool                        `json:"is_active"`
	DeliveryModes []string                    `json:"delivery_modes"`
	ShippingFee   int32                       `json:"shipping_fee"`
	Rating        float64                     `json:"rating"`
	PixKey        string                      `json:"pix_key"`
	CreatedAt     string                      `json:"created_at"`
	UpdatedAt     string                      `json:"updated_at"`
}

type CreateCompanyResponse struct {
	ExternalID string `json:"external_id"`
}

type GetCompanyProductsByCategoryResponse struct {
	CategoryName       string                  `json:"CategoryName"`
	CategoryImage      string                  `json:"CategoryImage"`
	CategoryExternalID string                  `json:"CategoryExternalID"`
	CompanyName        string                  `json:"CompanyName"`
	CompanyExternalID  string                  `json:"CompanyExternalID"`
	Products           []custom_models.Product `json:"Products"`
}

type ActivateCompanyResponse struct {
	ExternalID string `json:"external_id"`
	Name       string `json:"name"`
	IsActive   bool   `json:"is_active"`
}

type LinkUserToCompanyRequest struct {
	UserExternalID string `json:"user_external_id" validate:"required" example:"01JR0X8RNYFJECMV1NBNVK9921"`
}

type UpdateCompanyStatusRequest struct {
	Activate *bool `json:"activate" validate:"required" example:"true"`
}

type LinkUserToCompanyResponse struct {
	CompanyExternalID string `json:"company_external_id"`
	CompanyName       string `json:"company_name"`
	UserExternalID    string `json:"user_external_id"`
	UserName          string `json:"user_name"`
}

type GetMyCompaniesResponse struct {
	CompanyExternalIDs []string `json:"company_external_ids"`
	OwnerExternalID    string   `json:"owner_external_id"`
	DashboardURL       string   `json:"dashboard_url"`
}

type CompanyOwnerDetails struct {
	ExternalID string `json:"external_id"`
	Name       string `json:"name"`
	Email      string `json:"email"`
}

type GetCompanyDetailsResponse struct {
	ID               int32                   `json:"id"`
	Name             string                  `json:"name"`
	Cnpj             string                  `json:"cnpj"`
	Bio              string                  `json:"bio"`
	Picture          string                  `json:"picture"`
	PhoneNumbers     []string                `json:"phone_numbers"`
	PixKey           string                  `json:"pix_key"`
	SubscriptionID   *int32                  `json:"subscription_id"`
	Rating           float64                 `json:"rating"`
	ShippingFee      int32                   `json:"shipping_fee"`
	AffiliateBalance int32                   `json:"affiliate_balance"`
	DeliveryModes    []string                `json:"delivery_modes"`
	ExternalID       string                  `json:"external_id"`
	IsActive         bool                    `json:"is_active"`
	CommissionRate   int32                   `json:"commission_rate"`
	CashbackRate     int32                   `json:"cashback_rate"`
	CreatedAt        string                  `json:"created_at"`
	UpdatedAt        string                  `json:"updated_at"`
	Owner            *CompanyOwnerDetails    `json:"owner"`
	Addresses        []interface{}           `json:"addresses"`
	Products         []custom_models.Product `json:"products"`
}

type CreateCompanySuccessResponse = common.SuccessResponse[CreateCompanyResponse]
type GetActiveCompaniesSuccessResponse = common.SuccessResponseWithPagination[GetActiveCompaniesResponse]
type GetAllCompaniesSuccessResponse = common.SuccessResponseWithPagination[GetActiveCompaniesResponse]
type GetActiveCompanySuccessResponse = common.SuccessResponse[GetActiveCompaniesResponse]
type GetCompanyProductsByCategorySuccessResponse = common.SuccessResponseWithPagination[GetCompanyProductsByCategoryResponse]
type ActivateCompanySuccessResponse = common.SuccessResponse[ActivateCompanyResponse]
type UpdateCompanyStatusSuccessResponse = common.SuccessResponse[ActivateCompanyResponse]
type LinkUserToCompanySuccessResponse = common.SuccessResponse[LinkUserToCompanyResponse]
type GetMyCompaniesSuccessResponse = common.SuccessResponse[GetMyCompaniesResponse]
type GetCompanyDetailsSuccessResponse = common.SuccessResponse[GetCompanyDetailsResponse]

// Balance endpoint types
type GetBalanceResponse struct {
	Balance int32  `json:"balance" example:"15000"`
	Name    string `json:"name" example:"Mercadinho Caicó"`
	PixKey  string `json:"pix_key" example:"74312689000126"`
}

type GetBalanceSuccessResponse = common.SuccessResponse[GetBalanceResponse]

// Withdraw endpoint types
type WithdrawRequest struct {
	Amount int32 `json:"amount" validate:"required,min=50000" example:"50000"`
}

type WithdrawResponse struct {
	Status           string `json:"status" example:"CREATED"`
	Value            int32  `json:"value" example:"5000"`
	CorrelationID    string `json:"correlation_id" example:"TESTING1323"`
	DestinationAlias string `json:"destination_alias" example:"<EMAIL>"`
	Comment          string `json:"comment" example:"testing-transaction"`
	EndToEndId       string `json:"end_to_end_id,omitempty" example:"E18236120202012032010s01345689XBY"`
	Time             string `json:"time,omitempty" example:"2021-03-03T12:33:00.536Z"`
}

type WithdrawSuccessResponse = common.SuccessResponse[WithdrawResponse]

// Withdrawal history endpoint types
type GetWithdrawalsResponse struct {
	ID                string `json:"id" example:"123"`
	Amount            int32  `json:"amount" example:"50000"`
	Status            string `json:"status" example:"CREATED"`
	CreatedAt         string `json:"created_at" example:"2024-01-15T10:30:00Z"`
	CorrelationID     string `json:"correlation_id,omitempty" example:"TESTING1323"`
	DestinationPixKey string `json:"destination_pix_key,omitempty" example:"<EMAIL>"`
	Comment           string `json:"comment,omitempty" example:"testing-transaction"`
	EndToEndId        string `json:"end_to_end_id,omitempty" example:"E18236120202012032010s01345689XBY"`
}

type GetWithdrawalsListResponse struct {
	Withdrawals []GetWithdrawalsResponse `json:"withdrawals"`
	TotalAmount int32                    `json:"total_amount" example:"150000"`
}

// MovementEvent represents the webhook payload for OPENPIX:MOVEMENT_CONFIRMED events
type MovementEvent struct {
	Event   string `json:"event"`
	Evento  string `json:"evento"`
	Account struct {
		AccountID string `json:"accountId"`
	} `json:"account"`
	Payment struct {
		Value            int    `json:"value"`
		Status           string `json:"status"`
		DestinationAlias string `json:"destinationAlias"`
		CorrelationID    string `json:"correlationID"`
	} `json:"payment"`
	Transaction struct {
		CorrelationID string    `json:"correlationID"`
		Value         int       `json:"value"`
		EndToEndID    string    `json:"endToEndId"`
		Time          time.Time `json:"time"`
	} `json:"transaction"`
	Destination struct {
		Name  string `json:"name"`
		TaxID struct {
			TaxID string `json:"taxID"`
			Type  string `json:"type"`
		} `json:"taxID"`
		PixKey   string `json:"pixKey"`
		BankName string `json:"bankName"`
		Account  string `json:"account"`
		Branch   string `json:"branch"`
	} `json:"destination"`
}

func NewCompanyHandler(env *config.Environment, queries *postgres.Queries, storage *storage.Storage, pool *pgxpool.Pool, fcmService *fcm.Service) *chi.Mux {
	h := &CompanyHandler{
		queries: queries,
		storage: storage,
		woovi: woovi.New(woovi.WooviConfig{
			APIKey: env.Woovi.API_KEY,
			Url:    env.Woovi.URL,
			PixKey: env.Woovi.PIX_KEY,
		}),
		pool:       pool,
		env:        env,
		fcmService: fcmService,
	}
	router := chi.NewRouter()
	m := middlewares.New(env, queries)
	adminRouter := router.With(m.AdminPermissions)
	partnerRouter := router.With(m.PartnerPermissions)

	// Admin-only routes
	adminRouter.Post("/", h.Create)
	adminRouter.Put("/{externalID}", h.UpdateCompanyData)
	adminRouter.Patch("/{externalID}", h.UpdateCompanyImage)
	adminRouter.Post("/{externalID}/status", h.UpdateCompanyStatus)
	adminRouter.Put("/{externalID}/owner", h.LinkUserToCompany)
	adminRouter.Get("/{external_id}/details", h.GetCompanyDetails)
	adminRouter.Get("/all", h.GetAllCompanies)

	// Partner routes (company owners can access these)
	partnerRouter.Put("/{externalID}/products", h.AddProducts)
	partnerRouter.Delete("/{externalID}/products", h.RemoveProducts)
	partnerRouter.Get("/invoice", h.GetInvoices)
	partnerRouter.Put("/invoice/{order_id}/status", h.UpdateInvoiceStatus)
	partnerRouter.Get("/my-companies", h.GetMyCompanies)
	partnerRouter.Get("/balance", h.GetBalance)
	partnerRouter.Post("/withdraw", h.Withdraw)
	partnerRouter.Get("/withdrawals", h.GetWithdrawals)

	router.Get("/", h.GetActiveCompanies)
	router.Get("/{externalID}", h.GetOne)
	router.Get("/{company_external_id}/category/{category_external_id}", h.GetCompanyProductsByCategory)

	// Webhook routes (no authentication required)
	router.Post("/withdraw-callback-confirmed", h.ProcessWithdrawCallbackConfirmed)

	return router
}

// Create godoc
// @Summary Create a new company
// @Description Create a new company
// @Tags Company
// @Security Bearer
// @Accept miltipart/form-data
// @Produce json
// @Param name formData string true "Company name"
// @Param cnpj formData string true "Company CNPJ"
// @Param bio formData string true "Company bio"
// @Param image formData file true "Company image"
// @Param pix_key formData string true "Pix key"
// @Param delivery_modes formData string true "Delivery modes"
// @Param shipping_fee formData int true "Shipping fee"
// @Param commission_rate formData int true "Commission rate"
// @Param cashback_rate formData int true "Cashback rate"
// @Param phone_numbers formData string true "Phone numbers"
// @Param address_name formData string true "Address name"
// @Param address_street formData string true "Address street"
// @Param address_number formData string true "Address number"
// @Param address_neighborhood formData string true "Address neighborhood"
// @Param address_city formData string true "Address city"
// @Param address_state formData string true "Address state"
// @Param address_zip_code formData string true "Address zip code"
// @Param address_location_latitude formData float64 true "Address location latitude"
// @Param address_location_longitude formData float64 true "Address location longitude"
// @Param address_complement formData string false "Address complement"
// @Param owner_external_id formData string true "Owner external ID"
// @Success 201 {object} CreateCompanySuccessResponse "Company created"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company [post]
func (h *CompanyHandler) Create(w http.ResponseWriter, r *http.Request) {
	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, formValues, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error getting form file: %v\n", err)
		common.RespondError(w, fmt.Errorf("error getting form file: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file or file header is nil"), http.StatusBadRequest)
		return
	}

	latitude, err := strconv.ParseFloat(formValues["address_location_latitude"], 64)
	if err != nil {
		log.Printf("Error parsing latitude: %v\n", err)
		common.RespondError(w, fmt.Errorf("error parsing latitude: %v", err), http.StatusBadRequest)
		return
	}
	longitude, err := strconv.ParseFloat(formValues["address_location_longitude"], 64)
	if err != nil {
		log.Printf("Error parsing longitude: %v\n", err)
		common.RespondError(w, fmt.Errorf("error parsing longitude: %v", err), http.StatusBadRequest)
		return
	}

	shippingFee, err := strconv.ParseInt(formValues["shipping_fee"], 10, 32)
	if err != nil {
		log.Printf("Error parsing shipping_fee: %v\n", err)
		common.RespondError(w, fmt.Errorf("error parsing shipping_fee: %v", err), http.StatusBadRequest)
		return
	}
	commissionRate, err := strconv.ParseInt(formValues["commission_rate"], 10, 32)
	if err != nil {
		log.Printf("Error parsing commission_rate: %v\n", err)
		common.RespondError(w, fmt.Errorf("error parsing commission_rate: %v", err), http.StatusBadRequest)
		return
	}

	cashbackRate, err := strconv.ParseInt(formValues["cashback_rate"], 10, 32)
	if err != nil {
		log.Printf("Error parsing cashback_rate: %v\n", err)
		common.RespondError(w, fmt.Errorf("error parsing cashback_rate: %v", err), http.StatusBadRequest)
		return
	}

	payload := CreateCompanyRequest{
		Name:           formValues["name"],
		Cnpj:           formValues["cnpj"],
		Bio:            formValues["bio"],
		Image:          fileHeader,
		PhoneNumbers:   strings.Split(formValues["phone_numbers"], ","),
		PixKey:         formValues["pix_key"],
		DeliveryModes:  strings.Split(formValues["delivery_modes[]"], ","),
		ShippingFee:    int32(shippingFee),
		CommissionRate: int32(commissionRate),
		CashbackRate:   int32(cashbackRate),
		Address: custom_models.AddressParams{
			Name:         formValues["address_name"],
			Street:       formValues["address_street"],
			Number:       formValues["address_number"],
			Neighborhood: formValues["address_neighborhood"],
			City:         formValues["address_city"],
			State:        formValues["address_state"],
			ZipCode:      formValues["address_zip_code"],
			Location: custom_models.Location{
				Latitude:  latitude,
				Longitude: longitude,
			},
			Complement: formValues["address_complement"],
		},
	}

	helper := helpers.New()
	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	if !helper.IsValidCNPJ(payload.Cnpj) {
		common.RespondError(w, fmt.Errorf("invalid cnpj"), 400)
		return
	}

	//start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	companyExternalID := helper.GenerateULIDV2()

	// Upload image
	filename := fmt.Sprintf("%s%s", companyExternalID, filepath.Ext(fileHeader.Filename))
	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, fmt.Errorf("error uploading image: %v", err), http.StatusInternalServerError)
		return
	}

	companyID, err := qtx.CreateCompany(r.Context(), postgres.CreateCompanyParams{
		Name:           payload.Name,
		Cnpj:           payload.Cnpj,
		Bio:            payload.Bio,
		Picture:        imageURL,
		PhoneNumbers:   payload.PhoneNumbers,
		PixKey:         payload.PixKey,
		DeliveryModes:  payload.DeliveryModes,
		ShippingFee:    payload.ShippingFee,
		CommissionRate: payload.CommissionRate,
		CashbackRate:   payload.CashbackRate,
		ExternalID:     companyExternalID,
		IsActive:       false, // Companies start inactive and require admin activation
	})
	if err != nil {
		log.Printf("Error creating company: %v\n", err)
		// Remove uploaded image if product creation fails
		if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
			log.Printf("Error deleting image: %v\n", err)
		}
		common.RespondError(w, fmt.Errorf("error creating company: %v", err), http.StatusInternalServerError)
		return
	}

	err = qtx.CreateCompanyAddress(r.Context(), postgres.CreateCompanyAddressParams{
		CompanyID:    companyID,
		Street:       payload.Address.Street,
		Number:       payload.Address.Number,
		Neighborhood: payload.Address.Neighborhood,
		City:         payload.Address.City,
		State:        payload.Address.State,
		ZipCode:      payload.Address.ZipCode,
		Complement:   sql.NullString{String: payload.Address.Complement, Valid: payload.Address.Complement != ""},
		Name:         payload.Name,
		Latitude: sql.NullFloat64{
			Float64: payload.Address.Location.Latitude,
			Valid:   payload.Address.Location.Latitude != 0,
		},
		Longitude: sql.NullFloat64{
			Float64: payload.Address.Location.Longitude,
			Valid:   payload.Address.Location.Longitude != 0,
		},
		IsDefault:  true,
		ExternalID: helper.GenerateULIDV2(),
	})
	if err != nil {
		log.Printf("Error creating company address: %v\n", err)
		// Remove uploaded image if product creation fails
		if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
			log.Printf("Error deleting image: %v\n", err)
		}
		common.RespondError(w, fmt.Errorf("error creating company address: %v", err), http.StatusInternalServerError)
		return
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess(w, companyExternalID, http.StatusCreated)
}

// UpdateCompanyData godoc
// @Summary Update company data
// @Description Update company data
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param externalID path string true "External ID"
// @Param payload body UpdateCompanyDataRequest true "Company data"
// @Success 200 {object} EmptySuccessResponse "Company data updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID} [put]
func (h *CompanyHandler) UpdateCompanyData(w http.ResponseWriter, r *http.Request) {
	companyExternalID := chi.URLParam(r, "externalID")
	if companyExternalID == "" {
		log.Println("CompanyExternalID is empty")
		common.RespondError(w, fmt.Errorf("invalid companyExternalID"), 400)
		return
	}

	payload := UpdateCompanyDataRequest{}
	helper := helpers.New()

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	//start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	err = qtx.UpdateCompanyData(r.Context(), postgres.UpdateCompanyDataParams{
		ExternalID:    companyExternalID,
		Bio:           payload.Bio,
		PhoneNumbers:  payload.PhoneNumbers,
		DeliveryModes: payload.DeliveryModes,
		ShippingFee:   payload.ShippingFee,
	})
	if err != nil {
		log.Printf("Error updating company: %v\n", err)
		common.RespondError(w, err)
		return
	}

	err = qtx.UpdateCompanyAddress(r.Context(), postgres.UpdateCompanyAddressParams{
		ExternalID:   payload.Address.ExternalID,
		Name:         payload.Address.Name,
		Street:       payload.Address.Street,
		Number:       payload.Address.Number,
		Complement:   sql.NullString{String: payload.Address.Complement, Valid: payload.Address.Complement != ""},
		Neighborhood: payload.Address.Neighborhood,
		City:         payload.Address.City,
		State:        payload.Address.State,
		ZipCode:      payload.Address.ZipCode,
		Latitude: sql.NullFloat64{
			Float64: payload.Address.Location.Latitude,
			Valid:   payload.Address.Location.Latitude != 0,
		},
		Longitude: sql.NullFloat64{
			Float64: payload.Address.Location.Longitude,
			Valid:   payload.Address.Location.Longitude != 0,
		},
	})
	if err != nil {
		log.Printf("Error updating company address: %v\n", err)
		common.RespondError(w, err)
		return
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// UpdateCompanyImage godoc
// @Summary Update company image
// @Description Update company image
// @Tags Company
// @Security Bearer
// @Accept miltipart/form-data
// @Produce json
// @Param externalID path string true "External ID"
// @Param image formData file true "Company image"
// @Success 200 {object} EmptySuccessResponse "Company image updated"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID} [patch]
func (h *CompanyHandler) UpdateCompanyImage(w http.ResponseWriter, r *http.Request) {
	r.Body = http.MaxBytesReader(w, r.Body, MultipartFormMaxSize)
	if r.Body != nil {
		defer r.Body.Close()
	}

	companyExternalID := chi.URLParam(r, "externalID")
	if companyExternalID == "" {
		log.Println("CompanyExternalID is empty")
		common.RespondError(w, fmt.Errorf("invalid companyExternalID"), 400)
		return
	}

	mr, err := r.MultipartReader()
	if err != nil {
		log.Printf("Error creating multipart reader: %v\n", err)
		common.RespondError(w, fmt.Errorf("could not create multipart reader: %v", err), http.StatusBadRequest)
		return
	}

	file, fileHeader, _, err := helpers.DeferParts(mr)
	if err != nil {
		log.Printf("Error getting form file: %v\n", err)
		common.RespondError(w, fmt.Errorf("error getting form file: %v", err), http.StatusBadRequest)
		return
	}

	if file == nil || fileHeader == nil {
		log.Println("File or file header is nil")
		common.RespondError(w, fmt.Errorf("file or file header is nil"), http.StatusBadRequest)
		return
	}

	//start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	// Upload image
	filename := fmt.Sprintf("%s%s", companyExternalID, filepath.Ext(fileHeader.Filename))
	imageURL, err := h.storage.UploadImage(r.Context(), filename, file)
	if err != nil {
		log.Printf("Error uploading image: %v\n", err)
		common.RespondError(w, fmt.Errorf("error uploading image: %v", err), http.StatusInternalServerError)
		return
	}

	// Update company image
	err = qtx.UpdateCompanyImage(r.Context(), postgres.UpdateCompanyImageParams{
		ExternalID: companyExternalID,
		Picture:    imageURL,
	})
	if err != nil {
		log.Printf("Error updating company image: %v\n", err)
		// Remove uploaded image if product creation fails
		if err := h.storage.DeleteImage(r.Context(), filename); err != nil {
			log.Printf("Error deleting image: %v\n", err)
		}
		common.RespondError(w, fmt.Errorf("error updating company image: %v", err), http.StatusInternalServerError)
		return
	}
	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}
	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// UpdateCompanyStatus godoc
// @Summary Update company status (activate/deactivate)
// @Description Update company status by setting is_active to true or false. Requires admin role and validates company has valid owner when activating.
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param externalID path string true "Company External ID"
// @Param payload body UpdateCompanyStatusRequest true "Company status data"
// @Success 200 {object} UpdateCompanyStatusSuccessResponse "Company status updated successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - Company cannot be activated without valid owner"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Admin role required"
// @Failure 404 {object} common.ErrorResponse "Company not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID}/status [post]
func (h *CompanyHandler) UpdateCompanyStatus(w http.ResponseWriter, r *http.Request) {
	companyExternalID := chi.URLParam(r, "externalID")
	if companyExternalID == "" {
		log.Println("CompanyExternalID is empty")
		common.RespondError(w, fmt.Errorf("invalid companyExternalID"), http.StatusBadRequest)
		return
	}

	// Parse request body
	payload := UpdateCompanyStatusRequest{}
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Printf("Failed to decode request body: %v", err)
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	helper := helpers.New()
	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), http.StatusBadRequest)
		return
	}

	// Start transaction for validation and status update
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Printf("Failed to begin transaction: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	// First, get the company to validate it exists and check owner (only when activating)
	company, err := qtx.GetCompanyByExternalID(r.Context(), companyExternalID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Company not found: %s", companyExternalID)
			common.RespondError(w, fmt.Errorf("company not found"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting company: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Only validate owner when activating the company
	if *payload.Activate {
		// Validate company has a valid owner_id
		if !company.OwnerID.Valid {
			log.Printf("Company %s has no owner_id", companyExternalID)
			common.RespondError(w, fmt.Errorf("company cannot be activated without a valid owner"), http.StatusBadRequest)
			return
		}

		// Validate the owner exists and is active
		ownerInfo, err := qtx.GetUserByID(r.Context(), company.OwnerID.Int32)
		if err != nil {
			if err == sql.ErrNoRows {
				log.Printf("Owner user not found for company %s", companyExternalID)
				common.RespondError(w, fmt.Errorf("company cannot be activated without a valid owner"), http.StatusBadRequest)
				return
			}
			log.Printf("Error getting owner user: %v", err)
			common.RespondError(w, err, http.StatusInternalServerError)
			return
		}

		if !ownerInfo.IsActive {
			log.Printf("Owner user is not active for company %s", companyExternalID)
			common.RespondError(w, fmt.Errorf("company cannot be activated without a valid owner"), http.StatusBadRequest)
			return
		}
	}

	// Update the company status
	updatedCompany, err := qtx.UpdateCompanyStatus(r.Context(), postgres.UpdateCompanyStatusParams{
		ExternalID: companyExternalID,
		IsActive:   *payload.Activate,
	})
	if err != nil {
		log.Printf("Error updating company status: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Printf("Failed to commit transaction: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	response := ActivateCompanyResponse{
		ExternalID: updatedCompany.ExternalID,
		Name:       updatedCompany.Name,
		IsActive:   updatedCompany.IsActive,
	}

	statusAction := "deactivated"
	if *payload.Activate {
		statusAction = "activated"
	}
	log.Printf("Successfully %s company %s", statusAction, companyExternalID)

	common.RespondSuccess(w, response, http.StatusOK)
}

// LinkUserToCompany godoc
// @Summary Link a user to company as owner
// @Description Link an active user to a company by setting the user as the company owner. Requires admin role and validates user is active.
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param externalID path string true "Company External ID"
// @Param payload body LinkUserToCompanyRequest true "User external ID to link as owner"
// @Success 200 {object} LinkUserToCompanySuccessResponse "User linked to company successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - User not found or not active"
// @Failure 403 {object} common.ErrorResponse "Forbidden - Admin role required"
// @Failure 404 {object} common.ErrorResponse "Company not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID}/owner [put]
func (h *CompanyHandler) LinkUserToCompany(w http.ResponseWriter, r *http.Request) {
	companyExternalID := chi.URLParam(r, "externalID")
	if companyExternalID == "" {
		log.Println("CompanyExternalID is empty")
		common.RespondError(w, fmt.Errorf("invalid companyExternalID"), http.StatusBadRequest)
		return
	}

	payload := LinkUserToCompanyRequest{}
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Printf("Failed to decode request body: %v", err)
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	helper := helpers.New()
	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), http.StatusBadRequest)
		return
	}

	// Start transaction for validation and ownership update
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Printf("Failed to begin transaction: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	// First, validate the company exists and get current owner info
	currentCompany, err := qtx.GetCompanyByExternalID(r.Context(), companyExternalID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Company not found: %s", companyExternalID)
			common.RespondError(w, fmt.Errorf("company not found"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting company: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Store previous owner ID for role management
	var previousOwnerID sql.NullInt32
	if currentCompany.OwnerID.Valid {
		previousOwnerID = currentCompany.OwnerID
	}

	// Validate the user exists and is active
	userInfo, err := qtx.GetUserByExternalID(r.Context(), payload.UserExternalID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("User not found: %s", payload.UserExternalID)
			common.RespondError(w, fmt.Errorf("user not found"), http.StatusBadRequest)
			return
		}
		log.Printf("Error getting user: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	if !userInfo.IsActive {
		log.Printf("User is not active: %s", payload.UserExternalID)
		common.RespondError(w, fmt.Errorf("user must be active to be linked as company owner"), http.StatusBadRequest)
		return
	}

	// Update the company owner
	updatedCompany, err := qtx.UpdateCompanyOwner(r.Context(), postgres.UpdateCompanyOwnerParams{
		ExternalID: companyExternalID,
		OwnerID:    sql.NullInt32{Int32: userInfo.ID, Valid: true},
	})
	if err != nil {
		log.Printf("Error updating company owner: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Role management: Assign partner role to new owner if they don't have it
	hasPartnerRole, err := qtx.CheckIfUserHasRole(r.Context(), postgres.CheckIfUserHasRoleParams{
		UserID: userInfo.ID,
		Name:   "partner",
	})
	if err != nil {
		log.Printf("Error checking if user has partner role: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	if !hasPartnerRole {
		err = qtx.AssignRoleToUser(r.Context(), postgres.AssignRoleToUserParams{
			UserID: userInfo.ID,
			Name:   "partner",
		})
		if err != nil {
			log.Printf("Error assigning partner role to user %s: %v", payload.UserExternalID, err)
			common.RespondError(w, err, http.StatusInternalServerError)
			return
		}
		log.Printf("Successfully assigned partner role to user %s", payload.UserExternalID)
	}

	// Role management: Remove partner role from previous owner if they have no remaining companies
	if previousOwnerID.Valid && previousOwnerID.Int32 != userInfo.ID {
		companyCount, err := qtx.CountCompaniesByOwnerID(r.Context(), previousOwnerID)
		if err != nil {
			log.Printf("Error counting companies for previous owner %d: %v", previousOwnerID.Int32, err)
			common.RespondError(w, err, http.StatusInternalServerError)
			return
		}

		if companyCount == 0 {
			// Previous owner has no remaining companies, remove partner role
			err = qtx.RemoveRoleFromUser(r.Context(), postgres.RemoveRoleFromUserParams{
				UserID: previousOwnerID.Int32,
				Name:   "partner",
			})
			if err != nil {
				log.Printf("Error removing partner role from previous owner %d: %v", previousOwnerID.Int32, err)
				common.RespondError(w, err, http.StatusInternalServerError)
				return
			}
			log.Printf("Successfully removed partner role from previous owner %d (no remaining companies)", previousOwnerID.Int32)
		} else {
			log.Printf("Previous owner %d still has %d companies, keeping partner role", previousOwnerID.Int32, companyCount)
		}
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Printf("Failed to commit transaction: %v", err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	response := LinkUserToCompanyResponse{
		CompanyExternalID: updatedCompany.ExternalID,
		CompanyName:       updatedCompany.Name,
		UserExternalID:    payload.UserExternalID,
		UserName:          userInfo.Name,
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// AddProducts godoc
// @Summary Add products to a company
// @Description Add products to a company
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param externalID path string true "External ID"
// @Param payload body AddProductsToCompanyRequest true "Products data"
// @Success 200 {object} EmptySuccessResponse "Products added successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID}/products [put]
func (h *CompanyHandler) AddProducts(w http.ResponseWriter, r *http.Request) {
	CompanyExternalID := chi.URLParam(r, "externalID")
	if CompanyExternalID == "" {
		common.RespondError(w, fmt.Errorf("invalid CompanyExternalID"), 400)
		return
	}
	payload := AddProductsToCompanyRequest{}
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helpers.New().ValidateRequest(payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	//start transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Println("Failed to begin transaction: ", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	//JSONB
	var products pgtype.JSONB
	err = products.Set(payload)
	if err != nil {
		log.Printf("Error setting JSONB: %v\n", err)
		common.RespondError(w, err)
		return
	}

	qtx := h.queries.WithTx(tx)
	err = qtx.AddProductsToCompany(r.Context(), postgres.AddProductsToCompanyParams{
		CompanyExternalID: CompanyExternalID,
		ProductExternalID: payload.ProductExternalID,
		Price:             payload.Price,
		Stock:             payload.Stock,
		Discount:          payload.Discount,
	})
	if err != nil {
		log.Printf("Error adding products to company: %v\n", err)
		common.RespondError(w, err)
		return
	}
	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Println("Failed to commit transaction: ", err)
		common.RespondError(w, err)
		return
	}
	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// RemoveProducts godoc
// @Summary Remove products from a company
// @Description Remove products from a company
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param externalID path string true "External ID"
// @Param payload body AddProductsToCompanyRequest true "Products data"
// @Success 200 {object} EmptySuccessResponse "Products removed successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{externalID}/products [delete]
func (h *CompanyHandler) RemoveProducts(w http.ResponseWriter, r *http.Request) {
	CompanyExternalID := chi.URLParam(r, "externalID")
	if CompanyExternalID == "" {
		common.RespondError(w, fmt.Errorf("invalid CompanyExternalID"), 400)
		return
	}
	payload := AddProductsToCompanyRequest{}
	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Println("Failed to decode request body: ", err)
		common.RespondError(w, err)
		return
	}

	validationErrors := helpers.New().ValidateRequest(payload)
	if validationErrors != nil {
		common.RespondError(w, fmt.Errorf("%v", validationErrors), 400)
		return
	}

	err = h.queries.RemoveProductsFromCompany(r.Context(), postgres.RemoveProductsFromCompanyParams{
		CompanyExternalID: CompanyExternalID,
		ProductExternalID: payload.ProductExternalID,
	})
	if err != nil {
		log.Printf("Error removing products from company: %v\n", err)
		common.RespondError(w, err)
		return
	}

	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}

// GetActiveCompanies godoc
// @Summary Get active companies
// @Description Get active companies with optional location-based filtering
// @Tags Company
// @Accept json
// @Produce json
// @Param page query string false "Page"
// @Param limit query string false "Limit"
// @Param lat query number false "Latitude for location-based filtering"
// @Param long query number false "Longitude for location-based filtering"
// @Success 200 {object} GetActiveCompaniesSuccessResponse "List of companies"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/company [get]
func (h *CompanyHandler) GetActiveCompanies(w http.ResponseWriter, r *http.Request) {
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")
	latStr := r.URL.Query().Get("lat")
	longStr := r.URL.Query().Get("long")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	// Check if location parameters are provided
	hasLocationParams := latStr != "" && longStr != ""

	// If only one coordinate is provided, return empty array (fast fail)
	if (latStr != "" && longStr == "") || (latStr == "" && longStr != "") {
		log.Printf("Incomplete location parameters: lat=%s, long=%s", latStr, longStr)
		common.RespondSuccessWithPagination(w, []GetActiveCompaniesResponse{}, page, limit, 0)
		return
	}

	var lat, long float64
	var err error

	// Parse and validate coordinates if provided
	if hasLocationParams {
		lat, err = strconv.ParseFloat(latStr, 64)
		if err != nil {
			log.Printf("Invalid latitude: %v\n", err)
			common.RespondSuccessWithPagination(w, []GetActiveCompaniesResponse{}, page, limit, 0)
			return
		}

		long, err = strconv.ParseFloat(longStr, 64)
		if err != nil {
			log.Printf("Invalid longitude: %v\n", err)
			common.RespondSuccessWithPagination(w, []GetActiveCompaniesResponse{}, page, limit, 0)
			return
		}

		// Validate coordinate ranges
		if lat < -90 || lat > 90 {
			log.Printf("Latitude out of range: %f\n", lat)
			common.RespondSuccessWithPagination(w, []GetActiveCompaniesResponse{}, page, limit, 0)
			return
		}

		if long < -180 || long > 180 {
			log.Printf("Longitude out of range: %f\n", long)
			common.RespondSuccessWithPagination(w, []GetActiveCompaniesResponse{}, page, limit, 0)
			return
		}
	}

	offset := (page - 1) * limit

	var totalItems int
	var response []GetActiveCompaniesResponse

	if hasLocationParams {
		// Use location-based query
		distanceMeters := MaxDistanceKm * 1000 // Convert km to meters for PostGIS

		companiesWithLocation, err := h.queries.GetActiveCompaniesWithLocation(r.Context(), postgres.GetActiveCompaniesWithLocationParams{
			Limit:   int32(limit),
			Offset:  int32(offset),
			Column3: long, // longitude first for PostGIS
			Column4: lat,  // latitude second for PostGIS
			Column5: int32(distanceMeters),
		})
		if err != nil {
			log.Printf("Error getting active companies with location: %v\n", err)
			common.RespondError(w, err)
			return
		}

		response = make([]GetActiveCompaniesResponse, len(companiesWithLocation))
		for i, company := range companiesWithLocation {
			//Check if products bytes is empty
			if company.Products.Bytes == nil {
				company.Products.Bytes = []byte("[]")
			}

			products, err := helpers.ParseJSONB[custom_models.Product](company.Products.Bytes)
			if err != nil {
				log.Printf("Error parsing products: %v\n", err)
				common.RespondError(w, err)
				return
			}

			address, err := helpers.ParseJSONB[custom_models.AddressParams](company.Addresses.Bytes)
			if err != nil {
				log.Printf("Error parsing address: %v\n", err)
				common.RespondError(w, err)
				return
			}

			totalItems = int(company.TotalCount)
			response[i] = GetActiveCompaniesResponse{
				Name:          company.Name,
				Cnpj:          company.Cnpj,
				Bio:           company.Bio,
				Picture:       company.Picture,
				Address:       address[0],
				PhoneNumbers:  company.PhoneNumbers,
				Products:      products,
				ExternalID:    company.ExternalID,
				IsActive:      company.IsActive,
				ShippingFee:   company.ShippingFee,
				DeliveryModes: company.DeliveryModes,
				Rating:        company.Rating,
				PixKey:        company.PixKey,
				CreatedAt:     company.CreatedAt.String(),
				UpdatedAt:     company.UpdatedAt.String(),
			}
		}
	} else {
		// Use regular query without location filtering
		companies, err := h.queries.GetActiveCompanies(r.Context(), postgres.GetActiveCompaniesParams{
			Offset: int32(offset),
			Limit:  int32(limit),
		})
		if err != nil {
			log.Printf("Error getting active companies: %v\n", err)
			common.RespondError(w, err)
			return
		}

		response = make([]GetActiveCompaniesResponse, len(companies))
		for i, company := range companies {
			//Check if products bytes is empty
			if company.Products.Bytes == nil {
				company.Products.Bytes = []byte("[]")
			}

			products, err := helpers.ParseJSONB[custom_models.Product](company.Products.Bytes)
			if err != nil {
				log.Printf("Error parsing products: %v\n", err)
				common.RespondError(w, err)
				return
			}

			address, err := helpers.ParseJSONB[custom_models.AddressParams](company.Addresses.Bytes)
			if err != nil {
				log.Printf("Error parsing address: %v\n", err)
				common.RespondError(w, err)
				return
			}

			totalItems = int(company.TotalCount)
			response[i] = GetActiveCompaniesResponse{
				Name:          company.Name,
				Cnpj:          company.Cnpj,
				Bio:           company.Bio,
				Picture:       company.Picture,
				Address:       address[0],
				PhoneNumbers:  company.PhoneNumbers,
				Products:      products,
				ExternalID:    company.ExternalID,
				IsActive:      company.IsActive,
				ShippingFee:   company.ShippingFee,
				DeliveryModes: company.DeliveryModes,
				Rating:        company.Rating,
				PixKey:        company.PixKey,
				CreatedAt:     company.CreatedAt.String(),
				UpdatedAt:     company.UpdatedAt.String(),
			}
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// GetAllCompanies godoc
// @Summary Get all companies (admin only)
// @Description Get all companies including both active and inactive ones. Admin access required.
// @Tags Company
// @Security Bearer
// @Accept json
// @Produce json
// @Param page query string false "Page"
// @Param limit query string false "Limit"
// @Success 200 {object} GetAllCompaniesSuccessResponse "List of all companies"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 401 {object} common.ErrorResponse  "Unauthorized"
// @Failure 403 {object} common.ErrorResponse  "Forbidden - admin role required"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/company/all [get]
func (h *CompanyHandler) GetAllCompanies(w http.ResponseWriter, r *http.Request) {
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	companies, err := h.queries.GetAllCompanies(r.Context(), postgres.GetAllCompaniesParams{
		Offset: int32(offset),
		Limit:  int32(limit),
	})
	if err != nil {
		log.Printf("Error getting all companies: %v\n", err)
		common.RespondError(w, err)
		return
	}

	totalItems := 0
	response := make([]GetActiveCompaniesResponse, len(companies))
	for i, company := range companies {
		//Check if products bytes is empty
		if company.Products.Bytes == nil {
			company.Products.Bytes = []byte("[]")
		}

		products, err := helpers.ParseJSONB[custom_models.Product](company.Products.Bytes)
		if err != nil {
			log.Printf("Error parsing products: %v\n", err)
			common.RespondError(w, err)
			return
		}

		address, err := helpers.ParseJSONB[custom_models.AddressParams](company.Addresses.Bytes)
		if err != nil {
			log.Printf("Error parsing address: %v\n", err)
			common.RespondError(w, err)
			return
		}

		totalItems = int(company.TotalCount)
		response[i] = GetActiveCompaniesResponse{
			Name:          company.Name,
			Cnpj:          company.Cnpj,
			Bio:           company.Bio,
			Picture:       company.Picture,
			Address:       address[0],
			PhoneNumbers:  company.PhoneNumbers,
			Products:      products,
			ExternalID:    company.ExternalID,
			IsActive:      company.IsActive,
			ShippingFee:   company.ShippingFee,
			DeliveryModes: company.DeliveryModes,
			Rating:        company.Rating,
			PixKey:        company.PixKey,
			CreatedAt:     company.CreatedAt.String(),
			UpdatedAt:     company.UpdatedAt.String(),
		}
	}

	common.RespondSuccessWithPagination(w, response, page, limit, totalItems)
}

// GetOne godoc
// @Summary Get one company
// @Description Get one company
// @Tags Company
// @Accept json
// @Produce json
// @Param externalID path string true "External ID"
// @Success 200 {object} GetActiveCompanySuccessResponse "Company"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/company/{externalID} [get]
func (h *CompanyHandler) GetOne(w http.ResponseWriter, r *http.Request) {
	externalID := chi.URLParam(r, "externalID")
	if externalID == "" {
		common.RespondError(w, fmt.Errorf("invalid externalID"), 400)
		return
	}

	company, err := h.queries.GetCompanyByExternalID(r.Context(), externalID)
	if err != nil {
		if err.Error() == "no rows in result set" {
			common.RespondError(w, fmt.Errorf("company not found"), 404)
			return
		}
		log.Printf("Error getting company: %v\n", err)
		common.RespondError(w, err)
		return
	}

	//Check if products bytes is empty
	if company.Products.Bytes == nil {
		company.Products.Bytes = []byte("[]")
	}

	products, err := helpers.ParseJSONB[custom_models.Product](company.Products.Bytes)
	if err != nil {
		log.Printf("Error parsing products: %v\n", err)
		common.RespondError(w, err)
		return
	}

	address, err := helpers.ParseJSONB[custom_models.AddressParams](company.Addresses.Bytes)
	if err != nil {
		log.Printf("Error parsing address: %v\n", err)
		common.RespondError(w, err)
		return
	}

	res := GetActiveCompaniesResponse{
		Name:          company.Name,
		Cnpj:          company.Cnpj,
		Bio:           company.Bio,
		Picture:       company.Picture,
		Address:       address[0],
		PhoneNumbers:  company.PhoneNumbers,
		Products:      products,
		ExternalID:    company.ExternalID,
		IsActive:      company.IsActive,
		ShippingFee:   company.ShippingFee,
		DeliveryModes: company.DeliveryModes,
		Rating:        company.Rating,
		PixKey:        company.PixKey,
		CreatedAt:     company.CreatedAt.String(),
		UpdatedAt:     company.UpdatedAt.String(),
	}

	common.RespondSuccess(w, res, http.StatusOK)
}

// GetBalance godoc
// @Summary Get partner available balance
// @Description Retrieves the current available balance from invoice payouts for withdrawal
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} GetBalanceSuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 403 {object} common.ErrorResponse
// @Failure 404 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/company/balance [get]
func (h *CompanyHandler) GetBalance(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get companies owned by this user to find their PIX key
	companyExternalIDs, err := h.queries.GetCompaniesByOwnerID(ctx, sql.NullInt32{Int32: user.ID, Valid: true})
	if err != nil {
		log.Printf("Failed to get companies for owner %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Check if user owns any companies
	if len(companyExternalIDs) == 0 {
		log.Printf("User %d does not own any companies\n", user.ID)
		common.RespondError(w, fmt.Errorf("user does not own any companies"), http.StatusForbidden)
		return
	}

	// For now, use the first company (in the future, we might need to specify which company)
	// Get company details to retrieve PIX key
	company, err := h.queries.GetCompanyByExternalID(ctx, companyExternalIDs[0])
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Company not found: %s\n", companyExternalIDs[0])
			common.RespondError(w, fmt.Errorf("company not found"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting company details for %s: %v\n", companyExternalIDs[0], err)
		common.RespondError(w, err)
		return
	}

	// Check if company is active
	if !company.IsActive {
		log.Printf("Company %s is not active\n", company.ExternalID)
		common.RespondError(w, fmt.Errorf("company is not active"), http.StatusForbidden)
		return
	}

	// Get available balance from invoice payouts
	balanceInfo, err := h.queries.GetCompanyAvailableBalanceFast(ctx, company.ID)
	if err != nil {
		if strings.Contains(sql.ErrNoRows.Error(), err.Error()) {
			log.Printf("No available balance found for company %s\n", company.ExternalID)
			partialResponse := GetBalanceResponse{
				Balance: 0,
				Name:    company.Name,
				PixKey:  company.PixKey,
			}
			common.RespondSuccess(w, partialResponse, http.StatusOK)
			return
		}
		log.Printf("Failed to get available balance for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, err, http.StatusInternalServerError)
		return
	}

	// Convert balance to int32 (it comes as interface{} from SQL aggregate)
	var balance int32
	if balanceInfo.AvailableBalance != nil {
		if val, ok := balanceInfo.AvailableBalance.(int64); ok {
			balance = int32(val)
		}
	}

	// Prepare response
	response := GetBalanceResponse{
		Balance: balance,
		Name:    company.Name,
		PixKey:  company.PixKey,
	}

	log.Printf("Successfully retrieved balance for company %s: %d cents\n", company.ExternalID, balance)
	common.RespondSuccess(w, response, http.StatusOK)
}

// Withdraw godoc
// @Summary Request withdrawal from partner payouts
// @Description Initiates a withdrawal request using available invoice payouts with R$500 minimum and 3-day holding period validation
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Param request body WithdrawRequest true "Withdrawal request"
// @Success 200 {object} WithdrawSuccessResponse
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 403 {object} common.ErrorResponse
// @Failure 404 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/company/withdraw [post]
func (h *CompanyHandler) Withdraw(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Parse request body
	var payload WithdrawRequest
	if err := json.NewDecoder(r.Body).Decode(&payload); err != nil {
		log.Printf("Error decoding withdraw request: %v\n", err)
		common.RespondError(w, fmt.Errorf("invalid request body"), http.StatusBadRequest)
		return
	}

	// Validate request
	helper := helpers.New()
	validationErrors := helper.ValidateRequest(&payload)
	if validationErrors != nil {
		log.Printf("Withdraw request validation failed: %v\n", validationErrors)
		common.RespondError(w, fmt.Errorf("%v", validationErrors), http.StatusBadRequest)
		return
	}

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get companies owned by this user
	companyExternalIDs, err := h.queries.GetCompaniesByOwnerID(ctx, sql.NullInt32{Int32: user.ID, Valid: true})
	if err != nil {
		log.Printf("Failed to get companies for owner %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Check if user owns any companies
	if len(companyExternalIDs) == 0 {
		log.Printf("User %d does not own any companies\n", user.ID)
		common.RespondError(w, fmt.Errorf("user does not own any companies"), http.StatusForbidden)
		return
	}

	// For now, use the first company (in the future, we might need to specify which company)
	// Get company details
	company, err := h.queries.GetCompanyByExternalID(ctx, companyExternalIDs[0])
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Company not found: %s\n", companyExternalIDs[0])
			common.RespondError(w, fmt.Errorf("company not found"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting company details for %s: %v\n", companyExternalIDs[0], err)
		common.RespondError(w, err)
		return
	}

	// Check if company is active
	if !company.IsActive {
		log.Printf("Company %s is not active\n", company.ExternalID)
		common.RespondError(w, fmt.Errorf("company is not active"), http.StatusForbidden)
		return
	}

	// STEP 1: Validate withdrawal eligibility WITHOUT starting database transaction
	// This allows us to fail fast before calling Woovi API
	eligibility, err := h.queries.ValidateWithdrawalEligibility(ctx, postgres.ValidateWithdrawalEligibilityParams{
		ExternalID: company.ExternalID,
		Amount:     payload.Amount,
	})
	if err != nil {
		log.Printf("Failed to validate withdrawal eligibility for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to validate withdrawal eligibility"), http.StatusInternalServerError)
		return
	}

	if !eligibility.IsEligible {
		// Convert available balance to int32 for comparison
		var availableBalance int32
		if eligibility.AvailableBalance != nil {
			if val, ok := eligibility.AvailableBalance.(int64); ok {
				availableBalance = int32(val)
			}
		}

		log.Printf("Withdrawal not eligible for company %s: requested %d, available %d\n",
			company.ExternalID, payload.Amount, availableBalance)
		common.RespondError(w, fmt.Errorf("insufficient available balance. Available: R$%.2f, Requested: R$%.2f",
			float64(availableBalance)/100, float64(payload.Amount)/100), http.StatusBadRequest)
		return
	}

	// STEP 2: Get eligible payouts to verify we have enough (without locking yet)
	eligiblePayouts, err := h.queries.GetEligiblePayoutsForWithdrawal(ctx, company.ID)
	if err != nil {
		log.Printf("Failed to get eligible payouts for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to retrieve eligible payouts"), http.StatusInternalServerError)
		return
	}

	// Calculate total available and select payouts to cover the withdrawal amount
	var selectedPayouts []int64
	var totalSelected int32
	for _, payout := range eligiblePayouts {
		selectedPayouts = append(selectedPayouts, payout.ID)
		totalSelected += payout.Amount
		if totalSelected >= payload.Amount {
			break
		}
	}

	// Double-check we have enough (should not happen due to earlier validation)
	if totalSelected < payload.Amount {
		log.Printf("Insufficient payouts selected for company %s: selected %d, requested %d\n",
			company.ExternalID, totalSelected, payload.Amount)
		common.RespondError(w, fmt.Errorf("insufficient available payouts"), http.StatusBadRequest)
		return
	}

	// STEP 3: Call Woovi API FIRST - before any database changes
	// This ensures we only create database records if the actual withdrawal succeeds
	log.Printf("Initiating Woovi withdrawal for company %s: %d cents\n", company.ExternalID, payload.Amount)
	withdrawResponse, err := h.woovi.WithdrawFromSubAccount(ctx, company.PixKey, payload.Amount)
	if err != nil {
		log.Printf("Woovi withdrawal failed for company %s (PIX: %s): %v\n", company.ExternalID, company.PixKey, err)
		common.RespondError(w, fmt.Errorf("withdrawal request failed: %v", err), http.StatusInternalServerError)
		return
	}

	log.Printf("Woovi withdrawal successful for company %s: status=%s, correlation_id=%s\n",
		company.ExternalID, withdrawResponse.Transaction.Status, withdrawResponse.Transaction.CorrelationID)

	// STEP 4: NOW start database transaction - only after Woovi withdrawal succeeds
	// This ensures we only create database records if the actual withdrawal was successful
	tx, err := h.pool.Begin(ctx)
	if err != nil {
		log.Printf("Failed to start transaction for company %s after successful Woovi withdrawal: %v\n", company.ExternalID, err)
		// NOTE: At this point Woovi withdrawal succeeded but we can't record it in DB
		// This should trigger an alert for manual reconciliation
		common.RespondError(w, fmt.Errorf("withdrawal succeeded but failed to record in database - contact support"), http.StatusInternalServerError)
		return
	}
	defer tx.Rollback(ctx)

	qtx := h.queries.WithTx(tx)

	// Re-lock and verify eligible payouts are still available (race condition protection)
	currentEligiblePayouts, err := qtx.GetEligiblePayoutsForWithdrawal(ctx, company.ID)
	if err != nil {
		log.Printf("Failed to re-verify eligible payouts for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to verify payouts after withdrawal"), http.StatusInternalServerError)
		return
	}

	// Verify we still have the same payouts available (prevent race conditions)
	currentPayoutMap := make(map[int64]bool)
	for _, payout := range currentEligiblePayouts {
		currentPayoutMap[payout.ID] = true
	}

	for _, selectedID := range selectedPayouts {
		if !currentPayoutMap[selectedID] {
			log.Printf("Payout %d no longer available for company %s - race condition detected\n", selectedID, company.ExternalID)
			common.RespondError(w, fmt.Errorf("payout availability changed during withdrawal - please retry"), http.StatusConflict)
			return
		}
	}

	// Mark selected payouts as withdrawn
	err = qtx.AllocatePayoutsForWithdrawal(ctx, postgres.AllocatePayoutsForWithdrawalParams{
		Column1:   selectedPayouts,
		CompanyID: company.ID,
	})
	if err != nil {
		log.Printf("Failed to allocate payouts for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to allocate payouts"), http.StatusInternalServerError)
		return
	}

	// Store Woovi response for audit trail
	wooviResponseJSON, err := json.Marshal(withdrawResponse)
	if err != nil {
		log.Printf("Failed to marshal Woovi response for company %s: %v\n", company.ExternalID, err)
		// Continue execution - this is not critical enough to fail the request
		wooviResponseJSON = []byte("{}")
	}

	// Create withdrawal record with payout links using Woovi correlation ID
	withdrawalRecord, err := qtx.CreateWithdrawalWithPayouts(ctx, postgres.CreateWithdrawalWithPayoutsParams{
		CompanyID:         int64(company.ID),
		Amount:            payload.Amount,
		CorrelationID:     sql.NullString{String: withdrawResponse.Transaction.CorrelationID, Valid: withdrawResponse.Transaction.CorrelationID != ""},
		DestinationPixKey: sql.NullString{String: withdrawResponse.Transaction.DestinationAlias, Valid: withdrawResponse.Transaction.DestinationAlias != ""},
		Comment:           sql.NullString{String: withdrawResponse.Transaction.Comment, Valid: withdrawResponse.Transaction.Comment != ""},
		EndToEndID:        sql.NullString{String: withdrawResponse.Transaction.EndToEndId, Valid: withdrawResponse.Transaction.EndToEndId != ""},
		WooviResponse:     pgtype.JSONB{Bytes: wooviResponseJSON, Status: pgtype.Present},
		Column8:           selectedPayouts,
	})
	if err != nil {
		log.Printf("Failed to create withdrawal record for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to create withdrawal record"), http.StatusInternalServerError)
		return
	}

	// Commit transaction - withdrawal is now fully recorded
	if err := tx.Commit(ctx); err != nil {
		log.Printf("Failed to commit withdrawal transaction for company %s: %v\n", company.ExternalID, err)
		common.RespondError(w, fmt.Errorf("failed to complete withdrawal"), http.StatusInternalServerError)
		return
	}

	// Prepare response using Woovi withdrawal data
	response := WithdrawResponse{
		Status:           withdrawResponse.Transaction.Status,
		Value:            withdrawResponse.Transaction.Value,
		CorrelationID:    withdrawResponse.Transaction.CorrelationID,
		DestinationAlias: withdrawResponse.Transaction.DestinationAlias,
		Comment:          withdrawResponse.Transaction.Comment,
		EndToEndId:       withdrawResponse.Transaction.EndToEndId,
		Time:             withdrawResponse.Transaction.Time,
	}

	log.Printf("Successfully completed withdrawal for company %s: %d cents, %d payouts used, Woovi status: %s, DB record ID: %d\n",
		company.ExternalID, payload.Amount, len(selectedPayouts), withdrawResponse.Transaction.Status, withdrawalRecord.ID)
	common.RespondSuccess(w, response, http.StatusOK)
}

// GetWithdrawals godoc
// @Summary Get partner withdrawal history
// @Description Retrieves paginated withdrawal history for the partner's companies with period filtering
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Param period query string false "Period filter" Enums(1m,3m,6m,1y) default(30d)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} common.SuccessResponseWithPagination[GetWithdrawalsListResponse]
// @Failure 400 {object} common.ErrorResponse
// @Failure 401 {object} common.ErrorResponse
// @Failure 403 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/company/withdrawals [get]
func (h *CompanyHandler) GetWithdrawals(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")
	period := r.URL.Query().Get("period")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	// Validate period parameter
	validPeriods := []string{"1m", "3m", "6m", "1y"}
	if period != "" && !slices.Contains(validPeriods, period) {
		log.Printf("Invalid period: %s\n", period)
		common.RespondError(w, fmt.Errorf("invalid period. Valid values: 1m, 3m, 6m, 1y"), http.StatusBadRequest)
		return
	}

	// Default to 30 days if no period specified
	if period == "" {
		period = "30d" // This will be handled as default in the SQL query
	}

	offset := (page - 1) * limit

	// Get companies owned by this user to find their external IDs
	companyExternalIDs, err := h.queries.GetCompaniesByOwnerID(ctx, sql.NullInt32{Int32: user.ID, Valid: true})
	if err != nil {
		log.Printf("Failed to get companies for owner %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Check if user owns any companies
	if len(companyExternalIDs) == 0 {
		log.Printf("User %d does not own any companies\n", user.ID)
		// Return empty result instead of error for better UX
		response := GetWithdrawalsListResponse{
			Withdrawals: []GetWithdrawalsResponse{},
			TotalAmount: 0,
		}
		common.RespondSuccessWithPagination(w, response, page, limit, 0)
		return
	}

	// For now, use the first company (in the future, we might need to specify which company)
	companyExternalID := companyExternalIDs[0]

	// Get withdrawals with payout details and period filtering
	withdrawals, err := h.queries.GetWithdrawalHistoryWithPayouts(ctx, postgres.GetWithdrawalHistoryWithPayoutsParams{
		ExternalID: companyExternalID,
		Limit:      int32(limit),
		Offset:     int32(offset),
		Column4:    period,
	})
	if err != nil {
		log.Printf("Failed to get withdrawal history for company %s: %v\n", companyExternalID, err)
		common.RespondError(w, err)
		return
	}

	// Convert to response format
	withdrawalResponses := make([]GetWithdrawalsResponse, len(withdrawals))
	var totalCount int64
	var totalAmount int64

	for i, withdrawal := range withdrawals {
		// Handle null string fields safely
		correlationID := ""
		if withdrawal.CorrelationID.Valid {
			correlationID = withdrawal.CorrelationID.String
		}

		destinationPixKey := ""
		if withdrawal.DestinationPixKey.Valid {
			destinationPixKey = withdrawal.DestinationPixKey.String
		}

		comment := ""
		if withdrawal.Comment.Valid {
			comment = withdrawal.Comment.String
		}

		endToEndId := ""
		if withdrawal.EndToEndID.Valid {
			endToEndId = withdrawal.EndToEndID.String
		}

		withdrawalResponses[i] = GetWithdrawalsResponse{
			ID:                strconv.FormatInt(withdrawal.ID, 10),
			Amount:            withdrawal.Amount,
			Status:            withdrawal.Status,
			CreatedAt:         withdrawal.CreatedAt.Format(time.RFC3339),
			CorrelationID:     correlationID,
			DestinationPixKey: destinationPixKey,
			Comment:           comment,
			EndToEndId:        endToEndId,
		}

		// Get totals from first row (they're the same for all rows due to window function)
		if i == 0 {
			totalCount = withdrawal.TotalCount
			if totalAmountVal, ok := withdrawal.TotalAmount.(int64); ok {
				totalAmount = totalAmountVal
			}
		}
	}

	response := GetWithdrawalsListResponse{
		Withdrawals: withdrawalResponses,
		TotalAmount: int32(totalAmount),
	}

	log.Printf("Successfully retrieved %d withdrawals for company %s (total: %d)\n",
		len(withdrawalResponses), companyExternalID, totalCount)
	common.RespondSuccessWithPagination(w, response, page, limit, int(totalCount))
}

// GetCompanyProductsByCategory godoc
// @Summary Get company products by category
// @Description Get company products by category
// @Tags Company
// @Accept json
// @Produce json
// @Param company_external_id path string true "Company external ID"
// @Param category_external_id path string true "Category external ID"
// @Param page query string false "Page"
// @Param limit query string false "Limit"
// @Success 200 {object} GetCompanyProductsByCategorySuccessResponse "Company products"
// @Failure 400 {object} common.ErrorResponse  "Bad request"
// @Failure 500 {object} common.ErrorResponse  "Internal server error"
// @Router /v1/company/{company_external_id}/category/{category_external_id} [get]
func (h *CompanyHandler) GetCompanyProductsByCategory(w http.ResponseWriter, r *http.Request) {
	companyExternalID := chi.URLParam(r, "company_external_id")
	if companyExternalID == "" {
		common.RespondError(w, fmt.Errorf("invalid companyExternalID"), 400)
		return
	}

	categoryExternalID := chi.URLParam(r, "category_external_id")
	if categoryExternalID == "" {
		common.RespondError(w, fmt.Errorf("invalid categoryExternalID"), 400)
		return
	}

	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	company, err := h.queries.GetCompanyProductsByCategory(r.Context(), postgres.GetCompanyProductsByCategoryParams{
		CompanyExternalID:  companyExternalID,
		CategoryExternalID: categoryExternalID,
		Limit:              int32(limit),
		Offset:             int32(offset),
	})
	if err != nil {
		log.Printf("Error getting company products by category: %v\n", err)
		common.RespondError(w, err)
		return
	}

	if company.Products.Bytes == nil {
		company.Products.Bytes = []byte("[]")
	}

	// Parse products JSONB to []custom_models.Product
	products, err := helpers.ParseJSONB[custom_models.Product](company.Products.Bytes)
	if err != nil {
		log.Printf("Error parsing products: %v\n", err)
		common.RespondError(w, err)
		return
	}

	response := GetCompanyProductsByCategoryResponse{
		CategoryName:       company.CategoryName,
		CategoryImage:      company.CategoryImage,
		CategoryExternalID: company.CategoryExternalID,
		CompanyName:        company.CompanyName,
		CompanyExternalID:  company.CompanyExternalID,
		Products:           products,
	}

	common.RespondSuccessWithPagination(w, response, page, limit, int(company.TotalCount))
}

// GetInvoices godoc
// @Summary Get company invoices
// @Description Get all invoices for companies owned by the authenticated user with user information and products
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Param page query int false "Page number (default: 1)"
// @Param limit query int false "Items per page (default: 10)"
// @Success 200 {object} GetInvoicesSuccessResponse "Company invoices retrieved successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid pagination parameters"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/invoice [get]
func (h *CompanyHandler) GetInvoices(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Parse pagination parameters
	pageStr := r.URL.Query().Get("page")
	limitStr := r.URL.Query().Get("limit")

	page := 1
	limit := 10

	if pageStr != "" {
		p, err := strconv.Atoi(pageStr)
		if err != nil || p < 1 {
			log.Printf("Invalid page: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid page"), http.StatusBadRequest)
			return
		}
		page = p
	}

	if limitStr != "" {
		l, err := strconv.Atoi(limitStr)
		if err != nil || l < 1 {
			log.Printf("Invalid limit: %v\n", err)
			common.RespondError(w, fmt.Errorf("invalid limit"), http.StatusBadRequest)
			return
		}
		limit = l
	}

	offset := (page - 1) * limit

	// Get company invoices with pagination (where user is the company owner)
	invoices, err := h.queries.GetCompanyInvoices(ctx, postgres.GetCompanyInvoicesParams{
		OwnerID: sql.NullInt32{Int32: user.ID, Valid: true},
		Limit:   int32(limit),
		Offset:  int32(offset),
	})
	if err != nil {
		log.Printf("Failed to get company invoices for user %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// Convert to response format
	invoiceResponses := make([]InvoiceResponse, len(invoices))
	totalItems := 0
	for i, invoice := range invoices {
		totalItems = int(invoice.TotalCount)
		var products []InvoiceProductResponse
		if invoice.Products != nil {
			// Convert interface{} to JSON bytes first, then use ParseJSONB
			productsBytes, err := json.Marshal(invoice.Products)
			if err != nil {
				log.Printf("Failed to marshal invoice products for invoice %d: %v\n", invoice.ID, err)
				products = []InvoiceProductResponse{}
			} else {
				// Use ParseJSONB helper to parse the products
				rawProducts, err := helpers.ParseJSONB[map[string]interface{}](productsBytes)
				if err != nil {
					log.Printf("Failed to parse invoice products for invoice %d: %v\n", invoice.ID, err)
					products = []InvoiceProductResponse{}
				} else {
					products = make([]InvoiceProductResponse, len(rawProducts))
					for j, p := range rawProducts {
						// Parse product categories
						var categories []custom_models.Category
						if categoriesData, exists := p["product_categories"]; exists && categoriesData != nil {
							// Convert to JSON bytes and parse
							categoriesBytes, err := json.Marshal(categoriesData)
							if err != nil {
								log.Printf("Failed to marshal product categories for product %s: %v\n", p["product_external_id"], err)
								categories = []custom_models.Category{}
							} else {
								err := json.Unmarshal(categoriesBytes, &categories)
								if err != nil {
									log.Printf("Failed to parse product categories for product %s: %v\n", p["product_external_id"], err)
									categories = []custom_models.Category{}
								}
							}
						} else {
							categories = []custom_models.Category{}
						}

						products[j] = InvoiceProductResponse{
							Quantity:   int32(p["quantity"].(float64)),
							Price:      int32(p["unit_price"].(float64)),
							Discount:   int32(p["discount"].(float64)),
							Name:       p["product_name"].(string),
							Ean:        p["product_ean"].(string),
							ExternalID: p["product_external_id"].(string),
							Brand:      helpers.NullStringToPtr(p["product_brand"]),
							Image:      helpers.NullStringToPtr(p["product_image"]),
							Categories: categories,
						}
					}
				}
			}
		} else {
			products = []InvoiceProductResponse{}
		}

		// Parse payment data from info_details
		var brCode, qrCodeImage *string
		if invoice.InfoDetails.Status == pgtype.Present {
			paymentData, err := helpers.ParsePaymentData(invoice.InfoDetails.Bytes)
			if err != nil {
				log.Printf("Error parsing payment data for invoice %s: %v\n", invoice.OrderID, err)
			} else if paymentData != nil {
				if paymentData.BrCode != "" {
					brCode = &paymentData.BrCode
				}
				if paymentData.QrCodeImage != "" {
					qrCodeImage = &paymentData.QrCodeImage
				}
			}
		}

		invoiceResponses[i] = InvoiceResponse{
			OrderID:           invoice.OrderID,
			Status:            invoice.Status,
			StatusDescription: helpers.GetStatusDescription(invoice.Status),
			PaymentMethod:     invoice.PaymentMethod,
			UserName:          invoice.UserName.String,
			UserEmail:         invoice.UserEmail.String,
			UserAddress:       invoice.UserAddress.String,
			UserPhoneNumber:   invoice.UserPhoneNumber.String,
			Amount:            invoice.Amount,
			Discount:          invoice.Discount,
			ShippingFee:       invoice.ShippingFee,
			CompanyAmount:     invoice.CompanyAmount,
			DeliveryMode:      invoice.DeliveryMode,
			Coupon:            invoice.Coupon.String,
			CompanyExternalID: invoice.CompanyExternalID,
			Info:              invoice.Info.String,
			BrCode:            brCode,
			QrCodeImage:       qrCodeImage,
			FinishedAt: func() *time.Time {
				if invoice.FinishedAt.Valid {
					return &invoice.FinishedAt.Time
				} else {
					return nil
				}
			}(),
			CreatedAt: invoice.CreatedAt,
			UpdatedAt: invoice.UpdatedAt,
			Products:  products,
		}
	}

	log.Printf("Successfully retrieved %d company invoices for owner id %s (page %d, limit %d)\n", len(invoices), user.ExternalID, page, limit)

	common.RespondSuccessWithPagination(w, invoiceResponses, page, limit, totalItems)
}

// UpdateInvoiceStatus godoc
// @Summary Update company invoice status
// @Description Update the status of an invoice for a company owned by the authenticated user (allows cancellation in specific states). When cancelling an invoice, a reason must be provided in the request body.
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Param order_id path string true "Order ID"
// @Param payload body UpdateStatusRequest true "Status update payload (reason required when status is 'cancelled')"
// @Success 200 {object} UpdateInvoiceStatusSuccessResponse "Invoice status updated successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid status, transition, or missing reason for cancellation"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - user doesn't own company or invalid cancellation"
// @Failure 404 {object} common.ErrorResponse "Invoice not found"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/invoice/{order_id}/status [put]
func (h *CompanyHandler) UpdateInvoiceStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get order_id from URL
	orderID := chi.URLParam(r, "order_id")
	if orderID == "" {
		common.RespondError(w, fmt.Errorf("missing order_id parameter"), http.StatusBadRequest)
		return
	}

	// Parse request body
	var req UpdateStatusRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("Failed to decode request body: %v\n", err)
		common.RespondError(w, fmt.Errorf("invalid request body"), http.StatusBadRequest)
		return
	}

	// Validate reason field for cancelled status
	if req.Status == "cancelled" {
		if strings.TrimSpace(req.Reason) == "" {
			log.Printf("Missing reason for cancellation request for order %s\n", orderID)
			common.RespondError(w, fmt.Errorf("reason is required when cancelling an invoice"), http.StatusBadRequest)
			return
		}
	}

	// Get current invoice
	invoice, err := h.queries.GetInvoiceByOrderID(ctx, orderID)
	if err != nil {
		log.Printf("Failed to get invoice with order_id %s: %v\n", orderID, err)
		common.RespondError(w, fmt.Errorf("invoice not found"), http.StatusNotFound)
		return
	}

	// Verify user owns the company for this invoice
	company, err := h.queries.GetCompanyByID(ctx, invoice.CompanyID)
	if err != nil {
		log.Printf("Failed to get company %d: %v\n", invoice.CompanyID, err)
		common.RespondError(w, err)
		return
	}

	if !company.OwnerID.Valid || company.OwnerID.Int32 != user.ID {
		log.Printf("User %d attempted to update invoice for company %d they don't own\n", user.ID, invoice.CompanyID)
		common.RespondError(w, fmt.Errorf("you don't have permission to update this invoice"), http.StatusForbidden)
		return
	}

	// Validate status update
	if err := helpers.ValidateStatusUpdate(invoice.Status, req.Status, false, true); err != nil {
		log.Printf("Invalid status update attempt from %s to %s for order %s: %v\n", invoice.Status, req.Status, orderID, err)
		common.RespondError(w, err, http.StatusBadRequest)
		return
	}

	// Set finished_at = NOW() when completed
	finishedAt := sql.NullTime{}
	if req.Status == "completed" {
		finishedAt = sql.NullTime{Time: time.Now(), Valid: true}
	}

	// Prepare reason field - only set for cancelled status, empty otherwise
	var reason sql.NullString
	if req.Status == "cancelled" && strings.TrimSpace(req.Reason) != "" {
		reason = sql.NullString{String: strings.TrimSpace(req.Reason), Valid: true}
	}

	// Update invoice status with reason (empty for non-cancelled statuses)
	updatedInvoice, err := h.queries.UpdateInvoiceStatusByOrderID(ctx, postgres.UpdateInvoiceStatusByOrderIDParams{
		OrderID:    orderID,
		Status:     req.Status,
		Info:       reason,
		FinishedAt: finishedAt,
	})
	if err != nil {
		log.Printf("Failed to update invoice status for order %s: %v\n", orderID, err)
		common.RespondError(w, err)
		return
	}

	logMsg := fmt.Sprintf("Company owner %d updated invoice %d status from %s to %s (order: %s)", user.ID, updatedInvoice.ID, invoice.Status, req.Status, orderID)
	if req.Status == "cancelled" && strings.TrimSpace(req.Reason) != "" {
		logMsg += fmt.Sprintf(" with reason: %s", req.Reason)
	}
	log.Printf("%s\n", logMsg)

	// Get user external ID for notification
	invoiceUser, err := h.queries.GetMe(ctx, invoice.UserID)
	if err != nil {
		log.Printf("Error getting user info for notification: %v", err)
		// Continue without notification rather than failing the whole request
	} else {
		// Send FCM notification
		if h.fcmService != nil {
			fcmPayload := fcm.NotificationPayload{
				Type:              "invoice_status_update",
				OrderID:           orderID,
				NewStatus:         req.Status,
				OldStatus:         invoice.Status,
				StatusDescription: helpers.GetStatusDescription(req.Status),
				Message:           fmt.Sprintf("Status do pedido atualizado para %s pela empresa", helpers.GetStatusDescription(req.Status)),
				UpdatedBy:         "company",
				UpdaterName:       user.Name,
				UserExternalID:    invoiceUser.ExternalID,
				CompanyExternalID: company.ExternalID,
				CompanyName:       company.Name,
				Timestamp:         time.Now(),
			}
			result, err := h.fcmService.SendInvoiceStatusNotification(ctx, fcmPayload)
			if err != nil {
				log.Printf("Error sending FCM notification for invoice status update: %v", err)
			} else if result.Success {
				log.Printf("FCM notification sent for invoice status update: OrderID=%s, UserExternalID=%s, MessageID=%s",
					orderID, invoiceUser.ExternalID, result.MessageID)
			} else {
				log.Printf("FCM notification failed for invoice status update: OrderID=%s, Error=%s",
					orderID, result.Error)
			}
		}
	}

	response := UpdateInvoiceStatusResponse{
		Message:           "Invoice status updated successfully",
		OrderID:           orderID,
		Status:            req.Status,
		StatusDescription: helpers.GetStatusDescription(req.Status),
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// GetMyCompanies godoc
// @Summary Get my companies
// @Description Get all company external IDs that the authenticated user owns
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Success 200 {object} GetMyCompaniesSuccessResponse "List of company external IDs owned by the user"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/my-companies [get]
func (h *CompanyHandler) GetMyCompanies(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get user from context (set by authentication middleware)
	user, ok := ctx.Value(middlewares.UserContextKey).(middlewares.User)
	if !ok {
		log.Println("User not found in context")
		common.RespondError(w, fmt.Errorf("user not authenticated"), http.StatusUnauthorized)
		return
	}

	// Get companies owned by this user
	companyExternalIDs, err := h.queries.GetCompaniesByOwnerID(ctx, sql.NullInt32{Int32: user.ID, Valid: true})
	if err != nil {
		log.Printf("Failed to get companies for owner %d: %v\n", user.ID, err)
		common.RespondError(w, err)
		return
	}

	// If no companies found, return empty array
	if companyExternalIDs == nil {
		companyExternalIDs = []string{}
	}

	//Se role for admin, retorna /dashboard/admin senao retorna /dashboard/partner
	if slices.Contains(user.Roles, "admin") {
		response := GetMyCompaniesResponse{
			CompanyExternalIDs: companyExternalIDs,
			OwnerExternalID:    user.ExternalID,
			DashboardURL:       "/admin/dashboard",
		}
		common.RespondSuccess(w, response, http.StatusOK)
		return
	}

	response := GetMyCompaniesResponse{
		CompanyExternalIDs: companyExternalIDs,
		OwnerExternalID:    user.ExternalID,
		DashboardURL:       "/partner/dashboard",
	}

	common.RespondSuccess(w, response, http.StatusOK)
}

// GetCompanyDetails godoc
// @Summary Get detailed company information with owner details
// @Description Get comprehensive company information including owner details, addresses, and all company data. Admin access required.
// @Tags Company
// @Accept json
// @Produce json
// @Security Bearer
// @Param external_id path string true "Company External ID" minlength(26) maxlength(26)
// @Success 200 {object} GetCompanyDetailsSuccessResponse "Company details retrieved successfully"
// @Failure 400 {object} common.ErrorResponse "Bad request - invalid external_id format"
// @Failure 401 {object} common.ErrorResponse "Unauthorized"
// @Failure 403 {object} common.ErrorResponse "Forbidden - admin role required"
// @Failure 404 {object} common.ErrorResponse "Company not found or inactive"
// @Failure 500 {object} common.ErrorResponse "Internal server error"
// @Router /v1/company/{external_id}/details [get]
func (h *CompanyHandler) GetCompanyDetails(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	// Get external_id parameter from URL path
	externalID := chi.URLParam(r, "external_id")
	if externalID == "" {
		log.Println("External ID is empty")
		common.RespondError(w, fmt.Errorf("external_id is required"), http.StatusBadRequest)
		return
	}

	// Validate external_id format (ULID should be 26 characters)
	if len(externalID) != 26 {
		log.Printf("Invalid external_id format: %s (length: %d)\n", externalID, len(externalID))
		common.RespondError(w, fmt.Errorf("invalid external_id format"), http.StatusBadRequest)
		return
	}

	// Get company details with owner information
	companyDetails, err := h.queries.GetCompanyDetailsWithOwner(ctx, externalID)
	if err != nil {
		if err == sql.ErrNoRows {
			log.Printf("Company not found or inactive: %s\n", externalID)
			common.RespondError(w, fmt.Errorf("company not found or inactive"), http.StatusNotFound)
			return
		}
		log.Printf("Error getting company details for %s: %v\n", externalID, err)
		common.RespondError(w, err)
		return
	}

	// Handle nullable subscription_id
	var subscriptionID *int32
	if companyDetails.SubscriptionID.Valid {
		subscriptionID = &companyDetails.SubscriptionID.Int32
	}

	// Handle owner details (nullable)
	var owner *CompanyOwnerDetails
	if companyDetails.OwnerExternalID.Valid && companyDetails.OwnerName.Valid && companyDetails.OwnerEmail.Valid {
		owner = &CompanyOwnerDetails{
			ExternalID: companyDetails.OwnerExternalID.String,
			Name:       companyDetails.OwnerName.String,
			Email:      companyDetails.OwnerEmail.String,
		}
	}

	// Parse addresses JSON
	var addresses []interface{}
	if companyDetails.Addresses.Bytes != nil {
		if err := json.Unmarshal(companyDetails.Addresses.Bytes, &addresses); err != nil {
			log.Printf("Error parsing addresses JSON for company %s: %v\n", externalID, err)
			// Set empty array if parsing fails
			addresses = []interface{}{}
		}
	} else {
		addresses = []interface{}{}
	}

	// Parse products JSON
	var products []custom_models.Product
	if companyDetails.Products.Bytes != nil {
		products, err = helpers.ParseJSONB[custom_models.Product](companyDetails.Products.Bytes)
		if err != nil {
			log.Printf("Error parsing products JSON for company %s: %v\n", externalID, err)
			// Set empty array if parsing fails
			products = []custom_models.Product{}
		}
	} else {
		products = []custom_models.Product{}
	}

	// Build response
	response := GetCompanyDetailsResponse{
		ID:               companyDetails.ID,
		Name:             companyDetails.Name,
		Cnpj:             companyDetails.Cnpj,
		Bio:              companyDetails.Bio,
		Picture:          companyDetails.Picture,
		PhoneNumbers:     companyDetails.PhoneNumbers,
		PixKey:           companyDetails.PixKey,
		SubscriptionID:   subscriptionID,
		Rating:           companyDetails.Rating,
		ShippingFee:      companyDetails.ShippingFee,
		AffiliateBalance: companyDetails.AffiliateBalance,
		DeliveryModes:    companyDetails.DeliveryModes,
		ExternalID:       companyDetails.ExternalID,
		IsActive:         companyDetails.IsActive,
		CommissionRate:   companyDetails.CommissionRate,
		CashbackRate:     companyDetails.CashbackRate,
		CreatedAt:        companyDetails.CreatedAt.Format(time.RFC3339),
		UpdatedAt:        companyDetails.UpdatedAt.Format(time.RFC3339),
		Owner:            owner,
		Addresses:        addresses,
		Products:         products,
	}

	log.Printf("Successfully retrieved company details for %s (owner: %v)\n", externalID, owner != nil)

	common.RespondSuccess(w, response, http.StatusOK)
}

// ProcessWithdrawCallbackConfirmed godoc
// @Summary Process withdrawal confirmation callback
// @Description Process OPENPIX:MOVEMENT_CONFIRMED webhook events for partner withdrawal confirmations
// @Tags Company
// @Accept json
// @Produce json
// @Param body body MovementEvent true "Movement Event"
// @Success 200 {object} nil
// @Failure 400 {object} common.ErrorResponse
// @Failure 500 {object} common.ErrorResponse
// @Router /v1/company/withdraw-callback-confirmed [post]
func (h *CompanyHandler) ProcessWithdrawCallbackConfirmed(w http.ResponseWriter, r *http.Request) {
	var payload MovementEvent

	if r.Body != nil {
		defer r.Body.Close()
	}

	err := json.NewDecoder(r.Body).Decode(&payload)
	if err != nil {
		log.Printf("Error decoding request body: %v", err)
		common.RespondError(w, err)
		return
	}

	// Handle test webhook events
	if payload.Evento == "teste_webhook" {
		log.Println("Test webhook event received")
		common.RespondSuccess[interface{}](w, nil, http.StatusOK)
		return
	}

	// Validate event type
	if payload.Event != "OPENPIX:MOVEMENT_CONFIRMED" {
		log.Printf("Invalid event type: %s", payload.Event)
		common.RespondError(w, fmt.Errorf("invalid event type: %s", payload.Event))
		return
	}

	// Validate correlation ID
	if payload.Transaction.CorrelationID == "" {
		log.Println("Missing correlation ID in movement event")
		common.RespondError(w, fmt.Errorf("missing correlation ID"))
		return
	}

	// Begin database transaction
	tx, err := h.pool.Begin(r.Context())
	if err != nil {
		log.Printf("Error beginning transaction: %v", err)
		common.RespondError(w, err)
		return
	}
	defer tx.Rollback(r.Context())

	qtx := h.queries.WithTx(tx)

	fmt.Printf("Updating withdrawal status to %s for correlation ID: %s\n", payload.Payment.Status, payload.Transaction.CorrelationID)

	// Update withdrawal status directly - this will fail if withdrawal doesn't exist
	correlationID := sql.NullString{String: payload.Transaction.CorrelationID, Valid: true}
	rowsAffected, err := qtx.UpdateWithdrawalStatusByCorrelationID(r.Context(), postgres.UpdateWithdrawalStatusByCorrelationIDParams{
		Status:        payload.Payment.Status,
		CorrelationID: correlationID,
	})
	if err != nil {
		log.Printf("Error updating withdrawal status: %v", err)
		common.RespondError(w, err)
		return
	}

	// Check if any rows were affected (withdrawal exists)
	if rowsAffected == 0 {
		log.Printf("Withdrawal not found for correlation ID: %s", payload.Transaction.CorrelationID)
		common.RespondError(w, fmt.Errorf("withdrawal not found"))
		return
	}

	// Commit transaction
	if err := tx.Commit(r.Context()); err != nil {
		log.Printf("Failed to commit transaction: %v", err)
		common.RespondError(w, err)
		return
	}

	log.Printf("Successfully updated withdrawal status to %s for correlation ID: %s", payload.Payment.Status, payload.Transaction.CorrelationID)
	common.RespondSuccess[interface{}](w, nil, http.StatusOK)
}
