definitions:
  common.ErrorResponse:
    properties:
      code:
        example: "001"
        type: string
      details: {}
      message:
        type: string
    type: object
  common.SuccessResponse-array_handlers_SearchResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/handlers.SearchResponse'
        type: array
    type: object
  custom_models.AddressParams:
    properties:
      city:
        example: São Paulo
        type: string
      complement:
        example: Apto 101
        type: string
      external_id:
        example: "123456"
        type: string
      is_default:
        example: true
        type: boolean
      location:
        $ref: '#/definitions/custom_models.Location'
      name:
        example: Home
        type: string
      neighborhood:
        example: Downtown
        type: string
      number:
        example: "123"
        type: string
      state:
        example: SP
        type: string
      street:
        example: Rua das Flores
        type: string
      zip_code:
        example: "12345678"
        type: string
    type: object
  custom_models.Category:
    properties:
      external_id:
        example: "123456"
        type: string
      image:
        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
        type: string
      name:
        example: Bebidas
        type: string
    type: object
  custom_models.Location:
    properties:
      latitude:
        example: -5.9015168
        type: number
      longitude:
        example: -35.2485376
        type: number
    type: object
  custom_models.Product:
    properties:
      brand:
        example: Taeq
        type: string
      categories:
        items:
          $ref: '#/definitions/custom_models.Category'
        type: array
      description:
        example: água de coco integral taeq 200ml - SEM GLÚTEN - 100% NATURAL
        type: string
      discount:
        example: 10
        type: integer
      ean:
        example: "7895000292035"
        type: string
      external_id:
        example: "123456"
        type: string
      id:
        example: 1
        type: integer
      image:
        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
        type: string
      is_18_plus:
        example: false
        type: boolean
      is_active:
        example: true
        type: boolean
      name:
        example: água De Coco Taeq
        type: string
      price:
        example: 1000
        type: integer
      quantity:
        example: 1
        type: integer
      stock:
        example: 10
        type: integer
    type: object
  custom_models.ProductList:
    properties:
      external_id:
        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
        type: string
      quantity:
        example: 1
        type: integer
    type: object
  handlers.ActivateCompanyResponse:
    properties:
      external_id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
    type: object
  handlers.ActivateCompanySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.ActivateCompanyResponse'
    type: object
  handlers.AddProductsToCompanyRequest:
    properties:
      discount:
        example: 10
        type: integer
      price:
        example: 1000
        type: integer
      product_external_id:
        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
        type: string
      stock:
        example: 10
        type: integer
    type: object
  handlers.ApplyCouponRequest:
    properties:
      company_external_id:
        type: string
      coupon_code:
        type: string
      order_value:
        type: integer
    required:
    - company_external_id
    - coupon_code
    - order_value
    type: object
  handlers.ApplyCouponResponse:
    properties:
      discount_value:
        type: integer
      final_value:
        type: integer
      message:
        type: string
    type: object
  handlers.AssignRoleToUserRequest:
    properties:
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
      role:
        enum:
        - admin
        - partner
        - user
        example: admin
        type: string
    required:
    - external_id
    - role
    type: object
  handlers.AssignRoleToUserSuccessResponse:
    type: object
  handlers.ChargeEvent:
    properties:
      account:
        properties:
          environment:
            type: string
        type: object
      charge:
        properties:
          additionalInfo:
            items: {}
            type: array
          brCode:
            type: string
          comment:
            type: string
          correlationID:
            type: string
          createdAt:
            type: string
          customer:
            properties:
              correlationID:
                type: string
              email:
                type: string
              name:
                type: string
            type: object
          discount:
            type: integer
          expiresDate:
            type: string
          expiresIn:
            type: integer
          fee:
            type: integer
          globalID:
            type: string
          identifier:
            type: string
          paidAt:
            type: string
          payer:
            properties:
              address:
                properties:
                  city:
                    type: string
                  complement:
                    type: string
                  neighborhood:
                    type: string
                  number:
                    type: string
                  state:
                    type: string
                  street:
                    type: string
                  zipcode:
                    type: string
                type: object
              correlationID:
                type: string
              email:
                type: string
              name:
                type: string
              phone:
                type: string
              taxID:
                properties:
                  taxID:
                    type: string
                  type:
                    type: string
                type: object
            type: object
          paymentLinkID:
            type: string
          paymentLinkUrl:
            type: string
          paymentMethods:
            properties:
              pix:
                properties:
                  brCode:
                    type: string
                  fee:
                    type: integer
                  identifier:
                    type: string
                  method:
                    type: string
                  qrCodeImage:
                    type: string
                  status:
                    type: string
                  transactionID:
                    type: string
                  txId:
                    type: string
                  value:
                    type: integer
                type: object
            type: object
          pixKey:
            type: string
          qrCodeImage:
            type: string
          splits:
            items:
              properties:
                pixKey:
                  type: string
                pixKeyType:
                  type: string
                sourceAccount:
                  type: string
                splitType:
                  type: string
                value:
                  type: integer
              type: object
            type: array
          status:
            type: string
          transactionID:
            type: string
          type:
            type: string
          updatedAt:
            type: string
          value:
            type: integer
          valueWithDiscount:
            type: integer
        type: object
      company:
        properties:
          id:
            type: string
          name:
            type: string
          taxID:
            type: string
        type: object
      event:
        type: string
      evento:
        example: teste_webhook
        type: string
      pix:
        properties:
          charge:
            properties:
              additionalInfo:
                items: {}
                type: array
              brCode:
                type: string
              comment:
                type: string
              correlationID:
                type: string
              createdAt:
                type: string
              customer:
                properties:
                  correlationID:
                    type: string
                  email:
                    type: string
                  name:
                    type: string
                type: object
              discount:
                type: integer
              expiresDate:
                type: string
              expiresIn:
                type: integer
              fee:
                type: integer
              globalID:
                type: string
              identifier:
                type: string
              paidAt:
                type: string
              payer:
                properties:
                  address:
                    properties:
                      city:
                        type: string
                      complement:
                        type: string
                      neighborhood:
                        type: string
                      number:
                        type: string
                      state:
                        type: string
                      street:
                        type: string
                      zipcode:
                        type: string
                    type: object
                  correlationID:
                    type: string
                  email:
                    type: string
                  name:
                    type: string
                  phone:
                    type: string
                  taxID:
                    properties:
                      taxID:
                        type: string
                      type:
                        type: string
                    type: object
                type: object
              paymentLinkID:
                type: string
              paymentLinkUrl:
                type: string
              pixKey:
                type: string
              qrCodeImage:
                type: string
              splits:
                items:
                  properties:
                    pixKey:
                      type: string
                    pixKeyType:
                      type: string
                    sourceAccount:
                      type: string
                    splitType:
                      type: string
                    value:
                      type: integer
                  type: object
                type: array
              status:
                type: string
              transactionID:
                type: string
              type:
                type: string
              updatedAt:
                type: string
              value:
                type: integer
              valueWithDiscount:
                type: integer
            type: object
          createdAt:
            type: string
          customer:
            properties:
              correlationID:
                type: string
              email:
                type: string
              name:
                type: string
            type: object
          endToEndId:
            type: string
          globalID:
            type: string
          infoPagador:
            type: string
          payer:
            properties:
              address:
                properties:
                  _id:
                    type: string
                  city:
                    type: string
                  complement:
                    type: string
                  country:
                    type: string
                  location:
                    properties:
                      coordinates:
                        items: {}
                        type: array
                    type: object
                  neighborhood:
                    type: string
                  number:
                    type: string
                  state:
                    type: string
                  street:
                    type: string
                  zipcode:
                    type: string
                type: object
              correlationID:
                type: string
              email:
                type: string
              name:
                type: string
              phone:
                type: string
              taxID:
                properties:
                  taxID:
                    type: string
                  type:
                    type: string
                type: object
            type: object
          time:
            type: string
          transactionID:
            type: string
          type:
            type: string
          value:
            type: integer
        type: object
    type: object
  handlers.CheckIfEmailExistsRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  handlers.CheckIfEmailExistsSuccessResponse:
    properties:
      data:
        type: boolean
    type: object
  handlers.CheckoutRequest:
    properties:
      address_external_id:
        type: string
      company:
        type: string
      coupon_code:
        type: string
      credit_card:
        $ref: '#/definitions/handlers.CreditCard'
      delivery_mode:
        description: |-
          TODO: adicionar validação de delivery mode
          DeliveryMode      string             `json:"delivery_mode" validate:"required,oneof=delivery pickup"`
        type: string
      payment_method:
        enum:
        - pix
        - credit_card
        type: string
      products:
        items:
          $ref: '#/definitions/handlers.ProductsCheckout'
        type: array
    required:
    - address_external_id
    - company
    - payment_method
    - products
    type: object
  handlers.CheckoutSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/woovi.PixSuccessResponse'
    type: object
  handlers.Coupon:
    properties:
      code:
        type: string
      created_at:
        type: string
      expires_at:
        type: string
      external_id:
        type: string
      is_active:
        type: boolean
      min_order_value:
        type: integer
      owner_external_id:
        type: string
      owner_type:
        type: string
      quantity:
        type: integer
      type:
        type: string
      updated_at:
        type: string
      value:
        type: integer
    type: object
  handlers.CreateCategoryResponse:
    properties:
      external_id:
        example: "123456"
        type: string
    type: object
  handlers.CreateCategorySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.CreateCategoryResponse'
    type: object
  handlers.CreateCompanyResponse:
    properties:
      external_id:
        type: string
    type: object
  handlers.CreateCompanySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.CreateCompanyResponse'
    type: object
  handlers.CreateCouponRequest:
    properties:
      code:
        type: string
      expires_at:
        type: string
      min_order_value:
        type: number
      owner_type:
        enum:
        - company
        - admin
        type: string
      quantity:
        type: integer
      type:
        enum:
        - percentage
        - fixed
        type: string
      value:
        type: number
    required:
    - code
    - expires_at
    - min_order_value
    - owner_type
    - quantity
    - type
    - value
    type: object
  handlers.CreateProductSuccessResponse:
    properties:
      data:
        type: string
    type: object
  handlers.CreateProductsListRequest:
    properties:
      icon_url:
        example: https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png
        type: string
      is_public:
        example: false
        type: boolean
      name:
        example: Compras do mês
        type: string
      products:
        items:
          $ref: '#/definitions/custom_models.ProductList'
        type: array
    required:
    - icon_url
    - is_public
    - name
    type: object
  handlers.CreateProductsListResponse:
    properties:
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
    type: object
  handlers.CreateProductsListSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.CreateProductsListResponse'
    type: object
  handlers.CreateSubAccountRequest:
    properties:
      name:
        type: string
      pix_key:
        type: string
    required:
    - name
    - pix_key
    type: object
  handlers.CreateUserAddressResponse:
    properties:
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
    type: object
  handlers.CreateUserAddressSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.CreateUserAddressResponse'
    type: object
  handlers.CreateUserRequest:
    properties:
      cpf:
        example: "***********"
        type: string
      email:
        example: <EMAIL>
        type: string
      name:
        example: Vini
        type: string
      phone_numbers:
        example:
        - "***********"
        items:
          type: string
        type: array
    required:
    - cpf
    - email
    - name
    - phone_numbers
    type: object
  handlers.CreateUserResponse:
    properties:
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
    type: object
  handlers.CreateUserSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.CreateUserResponse'
    type: object
  handlers.CreditCard:
    properties:
      billing_address:
        $ref: '#/definitions/custom_models.AddressParams'
      card_holder_birth:
        type: string
      card_holder_cpf:
        type: string
      card_holder_name:
        type: string
      card_number:
        type: string
      cvv:
        type: string
      expiration_date:
        type: string
      installments:
        type: integer
    type: object
  handlers.EmptySuccessResponse:
    type: object
  handlers.GetActiveCategoriesPaginatedResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/handlers.GetActiveCategoriesResponse'
        type: array
      limit:
        type: integer
      pageNumber:
        type: integer
      totalItems:
        type: integer
      totalPages:
        type: integer
    type: object
  handlers.GetActiveCategoriesResponse:
    properties:
      external_id:
        example: "123456"
        type: string
      image:
        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
        type: string
      name:
        example: Bebidas
        type: string
    type: object
  handlers.GetActiveCategoriesSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetActiveCategoriesPaginatedResponse'
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.GetActiveCompaniesResponse:
    properties:
      address:
        $ref: '#/definitions/custom_models.AddressParams'
      bio:
        type: string
      cnpj:
        type: string
      created_at:
        type: string
      delivery_modes:
        items:
          type: string
        type: array
      external_id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
      phone_numbers:
        items:
          type: string
        type: array
      picture:
        type: string
      pix_key:
        type: string
      products:
        items:
          $ref: '#/definitions/custom_models.Product'
        type: array
      rating:
        type: number
      shipping_fee:
        type: integer
      updated_at:
        type: string
    type: object
  handlers.GetActiveCompaniesSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetActiveCompaniesResponse'
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.GetActiveCompanySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetActiveCompaniesResponse'
    type: object
  handlers.GetActiveProductsGroupedByCategorySuccessPaginatedResponse:
    type: object
  handlers.GetActiveProductsResponse:
    properties:
      brand:
        example: Taeq
        type: string
      categories:
        items:
          $ref: '#/definitions/custom_models.Category'
        type: array
      ean:
        example: "7895000292035"
        type: string
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
      image:
        example: https://images.openfoodfacts.org/images/products/789/500/029/2035/1.400.jpg
        type: string
      is_18_plus:
        example: false
        type: boolean
      is_active:
        example: true
        type: boolean
      is_reviewed:
        example: false
        type: boolean
      name:
        example: água De Coco Taeq
        type: string
    type: object
  handlers.GetActiveProductsSuccessPaginatedResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/handlers.GetActiveProductsResponse'
        type: array
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.GetActiveProductsSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetActiveProductsResponse'
    type: object
  handlers.GetCompanyProductsByCategoryResponse:
    properties:
      CategoryExternalID:
        type: string
      CategoryImage:
        type: string
      CategoryName:
        type: string
      CompanyExternalID:
        type: string
      CompanyName:
        type: string
      Products:
        items:
          $ref: '#/definitions/custom_models.Product'
        type: array
    type: object
  handlers.GetCompanyProductsByCategorySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetCompanyProductsByCategoryResponse'
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.GetDefaultPriceComparisonRequest:
    properties:
      latitude:
        example: -5.9015168
        type: number
      list_external_id:
        example: list_123
        type: string
      longitude:
        example: -35.2485376
        type: number
    required:
    - latitude
    - list_external_id
    - longitude
    type: object
  handlers.GetDefaultPriceComparisonResponse:
    properties:
      delivery_modes:
        example:
        - '["delivery"'
        - '"pickup"]'
        items:
          type: string
        type: array
      distance_km:
        example: 1.5
        type: number
      external_id:
        example: 01JV0K5T2ZGTBJT3RB6CMK2D0P
        type: string
      matched_list:
        items:
          $ref: '#/definitions/handlers.MatchedProduct'
        type: array
      matched_products_count:
        example: 5
        type: integer
      name:
        example: Supermercado Duas Irmãs
        type: string
      picture:
        example: https://example.com/picture.jpg
        type: string
      rating:
        example: 4.5
        type: number
      shipping_fee:
        example: 50
        type: integer
      total_price:
        example: 1390
        type: integer
      unmatched_product_count:
        example: 2
        type: integer
      unmatched_products:
        items:
          $ref: '#/definitions/handlers.UnmatchedProductInfo'
        type: array
    type: object
  handlers.GetInvoicesSuccessResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/handlers.InvoiceResponse'
        type: array
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.GetMeResponse:
    properties:
      addresses:
        items:
          $ref: '#/definitions/custom_models.AddressParams'
        type: array
      cashback_value:
        example: 0
        type: integer
      cpf:
        example: "***********"
        type: string
      email:
        example: <EMAIL>
        type: string
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
        type: string
      name:
        example: Vini
        type: string
      phone_numbers:
        example:
        - "11999999999"
        items:
          type: string
        type: array
      subscription_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
        type: string
    type: object
  handlers.GetMeSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetMeResponse'
    type: object
  handlers.GetMyCompaniesResponse:
    properties:
      company_external_ids:
        items:
          type: string
        type: array
      dashboard_url:
        type: string
      owner_external_id:
        type: string
    type: object
  handlers.GetMyCompaniesSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetMyCompaniesResponse'
    type: object
  handlers.GetProductsListResponse:
    properties:
      created_at:
        example: "2021-09-01T00:00:00Z"
        type: string
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
      icon_url:
        example: https://images.izymercado.com.br/014KG56DC01GG4TEB01ZEX7WFJ.png
        type: string
      is_public:
        example: false
        type: boolean
      name:
        example: Compras do mês
        type: string
      products:
        items:
          $ref: '#/definitions/custom_models.Product'
        type: array
      updated_at:
        example: "2021-09-01T00:00:00Z"
        type: string
    type: object
  handlers.GetProductsListSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.GetProductsListResponse'
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.InvoiceProductResponse:
    properties:
      discount:
        example: 50
        type: integer
      product_brand:
        example: Brand Name
        type: string
      product_ean:
        example: "1234*********"
        type: string
      product_external_id:
        example: prod_abc123
        type: string
      product_image:
        example: https://example.com/image.jpg
        type: string
      product_name:
        example: Product Name
        type: string
      quantity:
        example: 2
        type: integer
      unit_price:
        example: 750
        type: integer
    type: object
  handlers.InvoiceResponse:
    properties:
      amount:
        example: 1500
        type: integer
      company_bio:
        example: Store description
        type: string
      company_cnpj:
        example: "12345678000100"
        type: string
      company_external_id:
        example: comp_abc123
        type: string
      company_name:
        example: Store Name
        type: string
      company_phone_numbers:
        example:
        - '["+5511999999999"]'
        items:
          type: string
        type: array
      company_picture:
        example: https://example.com/store.jpg
        type: string
      company_pix_key:
        example: "12345678000100"
        type: string
      coupon:
        example: SAVE10
        type: string
      created_at:
        example: "2024-01-01T10:00:00Z"
        type: string
      delivery_mode:
        example: delivery
        type: string
      discount:
        example: 100
        type: integer
      finished_at:
        example: "2024-01-01T15:30:00Z"
        type: string
      info:
        example: Additional information
        type: string
      order_id:
        example: order_abc123
        type: string
      payment_method:
        example: pix
        type: string
      products:
        items:
          $ref: '#/definitions/handlers.InvoiceProductResponse'
        type: array
      shipping_fee:
        example: 500
        type: integer
      status:
        example: processing
        type: string
      status_description:
        example: Pagamento aprovado - Aguardando aceite do supermercado
        type: string
      updated_at:
        example: "2024-01-01T10:30:00Z"
        type: string
      user_address:
        example: Rua Example, 123
        type: string
      user_cpf:
        example: "***********"
        type: string
      user_email:
        example: <EMAIL>
        type: string
      user_name:
        example: Customer Name
        type: string
      user_phone_number:
        example: "+5511999999999"
        type: string
    type: object
  handlers.LinkUserToCompanyRequest:
    properties:
      user_external_id:
        example: 01JR0X8RNYFJECMV1NBNVK9921
        type: string
    required:
    - user_external_id
    type: object
  handlers.LinkUserToCompanyResponse:
    properties:
      company_external_id:
        type: string
      company_name:
        type: string
      user_external_id:
        type: string
      user_name:
        type: string
    type: object
  handlers.LinkUserToCompanySuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.LinkUserToCompanyResponse'
    type: object
  handlers.ListCouponsSuccessResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/handlers.Coupon'
        type: array
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.ListOneCouponSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.Coupon'
    type: object
  handlers.LoginRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
      login_code:
        example: "12345"
        type: string
    required:
    - email
    - login_code
    type: object
  handlers.LoginResponse:
    properties:
      access_token:
        type: string
      refresh_token:
        type: string
    type: object
  handlers.LoginSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.LoginResponse'
    type: object
  handlers.MatchedProduct:
    properties:
      brand:
        example: Marca
        type: string
      categories:
        items:
          $ref: '#/definitions/custom_models.Category'
        type: array
      description:
        example: Descrição do produto
        type: string
      discount:
        example: 10
        type: integer
      ean:
        example: "7891234567890"
        type: string
      external_id:
        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
        type: string
      image:
        example: https://example.com/image.jpg
        type: string
      is_18_plus:
        example: false
        type: boolean
      name:
        example: Produto Disponível
        type: string
      price:
        example: 1250
        type: integer
      quantity:
        example: 2
        type: integer
      stock:
        example: 50
        type: integer
    type: object
  handlers.MissingProduct:
    properties:
      external_id:
        example: 01JSF54A7XXP82H87M64C26AC8
        type: string
      image:
        example: https://example.com/image.jpg
        type: string
      name:
        example: Produto Original
        type: string
      quantity:
        example: 2
        type: integer
    type: object
  handlers.ProductsCheckout:
    properties:
      external_id:
        type: string
      quantity:
        type: integer
    required:
    - external_id
    - quantity
    type: object
  handlers.RefreshRequest:
    properties:
      refresh_token:
        type: string
    required:
    - refresh_token
    type: object
  handlers.RefreshResponse:
    properties:
      access_token:
        type: string
    type: object
  handlers.RefreshSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.RefreshResponse'
    type: object
  handlers.SearchResponse:
    properties:
      category_external_id:
        type: string
      category_image:
        type: string
      category_name:
        type: string
      company_external_id:
        type: string
      company_name:
        type: string
      discount:
        type: integer
      price:
        type: integer
      product_brand:
        type: string
      product_external_id:
        type: string
      product_image:
        type: string
      product_is_18_plus:
        type: boolean
      product_name:
        type: string
      search_rank:
        type: number
      stock:
        type: integer
    type: object
  handlers.SearchSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.SearchResponse'
      limit:
        example: 10
        type: integer
      pageNumber:
        example: 1
        type: integer
      totalItems:
        example: 100
        type: integer
      totalPages:
        example: 10
        type: integer
    type: object
  handlers.SendLoginCodeRequest:
    properties:
      email:
        example: <EMAIL>
        type: string
    required:
    - email
    type: object
  handlers.SimilarProducts:
    properties:
      brand:
        example: Taeq
        type: string
      external_id:
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZQ
        type: string
      name:
        example: água De Coco Taeq
        type: string
    type: object
  handlers.SubstituteProduct:
    properties:
      brand:
        example: Marca
        type: string
      categories:
        items:
          $ref: '#/definitions/custom_models.Category'
        type: array
      description:
        example: Descrição do produto
        type: string
      discount:
        example: 10
        type: integer
      ean:
        example: "7891234567890"
        type: string
      external_id:
        example: 01JSFHYVFQEF8R335QNFDDJ6ZZ
        type: string
      image:
        example: https://example.com/image.jpg
        type: string
      is_18_plus:
        example: false
        type: boolean
      match_reason:
        example: same_category
        type: string
      name:
        example: Produto Substituto
        type: string
      price:
        example: 1250
        type: integer
      stock:
        example: 50
        type: integer
    type: object
  handlers.UnmatchedProductInfo:
    properties:
      missing_product:
        $ref: '#/definitions/handlers.MissingProduct'
      substitutes:
        items:
          $ref: '#/definitions/handlers.SubstituteProduct'
        type: array
    type: object
  handlers.UpdateCompanyDataRequest:
    properties:
      address:
        $ref: '#/definitions/custom_models.AddressParams'
      bio:
        example: Mercadinho Caicó é o melhor mercadinho da região
        type: string
      delivery_modes:
        example:
        - delivery
        - pickup
        items:
          type: string
        type: array
      phone_numbers:
        example:
        - "***********"
        items:
          type: string
        type: array
      shipping_fee:
        example: 500
        type: integer
    required:
    - address
    - bio
    - delivery_modes
    - phone_numbers
    type: object
  handlers.UpdateInvoiceStatusResponse:
    properties:
      message:
        example: Invoice status updated successfully
        type: string
      order_id:
        example: order_abc123
        type: string
      status:
        example: processing
        type: string
      status_description:
        example: Pagamento aprovado - Aguardando aceite do supermercado
        type: string
    type: object
  handlers.UpdateInvoiceStatusSuccessResponse:
    properties:
      data:
        $ref: '#/definitions/handlers.UpdateInvoiceStatusResponse'
    type: object
  handlers.UpdateProductDataRequest:
    properties:
      brand:
        example: Brand XYZ
        type: string
      categories:
        items:
          $ref: '#/definitions/custom_models.Category'
        maxItems: 1
        type: array
      ean:
        example: "1234*********"
        type: string
      is_18_plus:
        example: false
        type: boolean
      is_active:
        example: true
        type: boolean
      is_reviewed:
        example: false
        type: boolean
      name:
        example: Product Name
        type: string
    required:
    - brand
    - categories
    - ean
    - name
    type: object
  handlers.UpdateStatusRequest:
    properties:
      reason:
        example: Order accepted by store
        type: string
      status:
        example: processing
        type: string
    type: object
  handlers.UpdateUserNameRequest:
    properties:
      name:
        example: Vini
        type: string
    required:
    - name
    type: object
  woovi.PixSuccessResponse:
    properties:
      charge:
        properties:
          additionalInfo:
            items:
              properties:
                key:
                  type: string
                value:
                  type: string
              type: object
            type: array
          brCode:
            type: string
          comment:
            type: string
          correlationID:
            type: string
          createdAt:
            type: string
          customer:
            properties:
              email:
                type: string
              name:
                type: string
              phone:
                type: string
              taxID:
                properties:
                  taxID:
                    type: string
                  type:
                    type: string
                type: object
            type: object
          expiresDate:
            type: string
          expiresIn:
            type: integer
          paymentLinkID:
            type: string
          paymentLinkUrl:
            type: string
          paymentMethods:
            properties:
              pix:
                properties:
                  additionalInfo:
                    items: {}
                    type: array
                  brCode:
                    type: string
                  fee:
                    type: integer
                  identifier:
                    type: string
                  method:
                    type: string
                  qrCodeImage:
                    type: string
                  status:
                    type: string
                  transactionID:
                    type: string
                  txId:
                    type: string
                  value:
                    type: integer
                type: object
            type: object
          qrCodeImage:
            type: string
          status:
            type: string
          updatedAt:
            type: string
          value:
            type: integer
        type: object
    type: object
host: '{{.Host}}'
info:
  contact:
    email: <EMAIL>
    name: Developer Support
    url: https://www.izymercado.com.br
  description: '### API Documentation'
  title: Izy Mercado API
  version: "1.0"
paths:
  /v1/auth/login:
    post:
      consumes:
      - application/json
      description: Login
      parameters:
      - description: Login payload
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.LoginSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Login
      tags:
      - Auth
  /v1/auth/logout:
    post:
      consumes:
      - application/json
      description: Logout
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Logout
      tags:
      - Auth
  /v1/auth/refresh:
    post:
      consumes:
      - application/json
      description: Refresh token
      parameters:
      - description: Refresh token payload
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.RefreshRequest'
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.RefreshSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Refresh token
      tags:
      - Auth
  /v1/auth/send-login-code:
    post:
      consumes:
      - application/json
      description: Send login code
      parameters:
      - description: Send login code payload
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.SendLoginCodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Send login code
      tags:
      - Auth
  /v1/category:
    get:
      consumes:
      - application/json
      description: Get active categories
      parameters:
      - description: Page
        in: query
        name: page
        type: string
      - description: Limit
        in: query
        name: limit
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of categories
          schema:
            $ref: '#/definitions/handlers.GetActiveCategoriesSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get active categories
      tags:
      - Category
    post:
      consumes:
      - multipart/form-data
      description: Create a new category
      parameters:
      - description: Category name
        in: formData
        name: name
        required: true
        type: string
      - description: Category image
        in: formData
        name: image
        required: true
        type: file
      produces:
      - application/json
      responses:
        "201":
          description: Category created
          schema:
            $ref: '#/definitions/handlers.CreateCategorySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Create a new category
      tags:
      - Category
  /v1/company:
    get:
      consumes:
      - application/json
      description: Get active companies
      parameters:
      - description: Page
        in: query
        name: page
        type: string
      - description: Limit
        in: query
        name: limit
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of companies
          schema:
            $ref: '#/definitions/handlers.GetActiveCompaniesSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get active companies
      tags:
      - Company
    post:
      consumes:
      - miltipart/form-data
      description: Create a new company
      parameters:
      - description: Company name
        in: formData
        name: name
        required: true
        type: string
      - description: Company CNPJ
        in: formData
        name: cnpj
        required: true
        type: string
      - description: Company bio
        in: formData
        name: bio
        required: true
        type: string
      - description: Company image
        in: formData
        name: image
        required: true
        type: file
      - description: Pix key
        in: formData
        name: pix_key
        required: true
        type: string
      - description: Delivery modes
        in: formData
        name: delivery_modes
        required: true
        type: string
      - description: Shipping fee
        in: formData
        name: shipping_fee
        required: true
        type: integer
      - description: Commission rate
        in: formData
        name: commission_rate
        required: true
        type: integer
      - description: Cashback rate
        in: formData
        name: cashback_rate
        required: true
        type: integer
      - description: Phone numbers
        in: formData
        name: phone_numbers
        required: true
        type: string
      - description: Address name
        in: formData
        name: address_name
        required: true
        type: string
      - description: Address street
        in: formData
        name: address_street
        required: true
        type: string
      - description: Address number
        in: formData
        name: address_number
        required: true
        type: string
      - description: Address neighborhood
        in: formData
        name: address_neighborhood
        required: true
        type: string
      - description: Address city
        in: formData
        name: address_city
        required: true
        type: string
      - description: Address state
        in: formData
        name: address_state
        required: true
        type: string
      - description: Address zip code
        in: formData
        name: address_zip_code
        required: true
        type: string
      - description: Address location latitude
        in: formData
        name: address_location_latitude
        required: true
        type: number
      - description: Address location longitude
        in: formData
        name: address_location_longitude
        required: true
        type: number
      - description: Address complement
        in: formData
        name: address_complement
        type: string
      - description: Owner external ID
        in: formData
        name: owner_external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "201":
          description: Company created
          schema:
            $ref: '#/definitions/handlers.CreateCompanySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Create a new company
      tags:
      - Company
  /v1/company/{company_external_id}/category/{category_external_id}:
    get:
      consumes:
      - application/json
      description: Get company products by category
      parameters:
      - description: Company external ID
        in: path
        name: company_external_id
        required: true
        type: string
      - description: Category external ID
        in: path
        name: category_external_id
        required: true
        type: string
      - description: Page
        in: query
        name: page
        type: string
      - description: Limit
        in: query
        name: limit
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Company products
          schema:
            $ref: '#/definitions/handlers.GetCompanyProductsByCategorySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get company products by category
      tags:
      - Company
  /v1/company/{externalID}:
    get:
      consumes:
      - application/json
      description: Get one company
      parameters:
      - description: External ID
        in: path
        name: externalID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Company
          schema:
            $ref: '#/definitions/handlers.GetActiveCompanySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get one company
      tags:
      - Company
    patch:
      consumes:
      - miltipart/form-data
      description: Update company image
      parameters:
      - description: External ID
        in: path
        name: externalID
        required: true
        type: string
      - description: Company image
        in: formData
        name: image
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: Company image updated
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update company image
      tags:
      - Company
    put:
      consumes:
      - application/json
      description: Update company data
      parameters:
      - description: External ID
        in: path
        name: externalID
        required: true
        type: string
      - description: Company data
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateCompanyDataRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Company data updated
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update company data
      tags:
      - Company
  /v1/company/{externalID}/activate:
    put:
      consumes:
      - application/json
      description: Activate a company by setting is_active to true. Requires admin
        role and validates company has valid owner.
      parameters:
      - description: Company External ID
        in: path
        name: externalID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Company activated successfully
          schema:
            $ref: '#/definitions/handlers.ActivateCompanySuccessResponse'
        "400":
          description: Bad request - Company cannot be activated without valid owner
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "403":
          description: Forbidden - Admin role required
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Company not found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Activate a company
      tags:
      - Company
  /v1/company/{externalID}/owner:
    put:
      consumes:
      - application/json
      description: Link an active user to a company by setting the user as the company
        owner. Requires admin role and validates user is active.
      parameters:
      - description: Company External ID
        in: path
        name: externalID
        required: true
        type: string
      - description: User external ID to link as owner
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.LinkUserToCompanyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: User linked to company successfully
          schema:
            $ref: '#/definitions/handlers.LinkUserToCompanySuccessResponse'
        "400":
          description: Bad request - User not found or not active
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "403":
          description: Forbidden - Admin role required
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Company not found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Link a user to company as owner
      tags:
      - Company
  /v1/company/{externalID}/products:
    delete:
      consumes:
      - application/json
      description: Remove products from a company
      parameters:
      - description: External ID
        in: path
        name: externalID
        required: true
        type: string
      - description: Products data
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.AddProductsToCompanyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Products removed successfully
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Remove products from a company
      tags:
      - Company
    put:
      consumes:
      - application/json
      description: Add products to a company
      parameters:
      - description: External ID
        in: path
        name: externalID
        required: true
        type: string
      - description: Products data
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.AddProductsToCompanyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Products added successfully
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Add products to a company
      tags:
      - Company
  /v1/company/invoice:
    get:
      consumes:
      - application/json
      description: Get all invoices for companies owned by the authenticated user
        with user information and products
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Company invoices retrieved successfully
          schema:
            $ref: '#/definitions/handlers.GetInvoicesSuccessResponse'
        "400":
          description: Bad request - invalid pagination parameters
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get company invoices
      tags:
      - Company
  /v1/company/invoice/{order_id}/status:
    put:
      consumes:
      - application/json
      description: Update the status of an invoice for a company owned by the authenticated
        user (allows cancellation in specific states)
      parameters:
      - description: Order ID
        in: path
        name: order_id
        required: true
        type: string
      - description: Status update payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Invoice status updated successfully
          schema:
            $ref: '#/definitions/handlers.UpdateInvoiceStatusSuccessResponse'
        "400":
          description: Bad request - invalid status or transition
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "403":
          description: Forbidden - user doesn't own company or invalid cancellation
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Invoice not found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update company invoice status
      tags:
      - Company
  /v1/company/my-companies:
    get:
      consumes:
      - application/json
      description: Get all company external IDs that the authenticated user owns
      produces:
      - application/json
      responses:
        "200":
          description: List of company external IDs owned by the user
          schema:
            $ref: '#/definitions/handlers.GetMyCompaniesSuccessResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get my companies
      tags:
      - Company
  /v1/company/subaccount:
    post:
      consumes:
      - application/json
      description: Create a new subaccount
      parameters:
      - description: Subaccount data
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateSubAccountRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Subaccount created
          schema:
            type: string
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Create a new subaccount
      tags:
      - Company
  /v1/coupon:
    get:
      consumes:
      - application/json
      description: List all coupons with pagination
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ListCouponsSuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: List all coupons
      tags:
      - Coupons
    post:
      consumes:
      - application/json
      description: Cria um novo cupom de desconto
      parameters:
      - description: Dados do cupom
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateCouponRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Cupom criado com sucesso
          schema:
            type: string
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Cria um novo cupom
      tags:
      - Coupons
  /v1/coupon/{external_id}:
    get:
      consumes:
      - application/json
      description: Get a specific coupon by its ExternalID
      parameters:
      - description: Coupon ExternalID
        in: path
        name: external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ListOneCouponSuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get a coupon by ExternalID
      tags:
      - Coupons
  /v1/coupon/{external_id}/status:
    patch:
      consumes:
      - application/json
      description: Ativa ou desativa um cupom através do parâmetro status
      parameters:
      - description: Coupon ExternalID
        in: path
        name: external_id
        required: true
        type: string
      - description: true para ativar, false para desativar
        in: query
        name: status
        required: true
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Not Found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Atualiza o status de um cupom
      tags:
      - Coupons
  /v1/coupon/apply:
    post:
      consumes:
      - application/json
      description: Valida e aplica um cupom de desconto ao pedido
      parameters:
      - description: Dados para aplicar o cupom
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.ApplyCouponRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.ApplyCouponResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Aplica um cupom de desconto
      tags:
      - Coupons
  /v1/payment/checkout:
    post:
      consumes:
      - application/json
      description: Checkout
      parameters:
      - description: Checkout Request
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CheckoutRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.CheckoutSuccessResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Checkout
      tags:
      - Payment
  /v1/payment/process-expired-charge-event-callback:
    post:
      consumes:
      - application/json
      description: Process expired charge event callback
      parameters:
      - description: Charge Event
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.ChargeEvent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Process expired charge event callback
      tags:
      - Payment
  /v1/payment/process-paid-charge-event-callback:
    post:
      consumes:
      - application/json
      description: Transfer between sub accounts callback
      parameters:
      - description: Charge Event
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/handlers.ChargeEvent'
      produces:
      - application/json
      responses:
        "200":
          description: OK
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            type: string
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - ApiKeyAuth: []
      summary: Transfer between sub accounts callback
      tags:
      - Payment
  /v1/price-comparison/default:
    post:
      consumes:
      - application/json
      description: Get default price comparison
      parameters:
      - description: Get default price comparison request
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.GetDefaultPriceComparisonRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/handlers.GetDefaultPriceComparisonResponse'
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get default price comparison
      tags:
      - PriceComparison
  /v1/price-comparison/recommendations/{list_external_id}:
    get:
      consumes:
      - application/json
      description: Get price comparison recommendations
      parameters:
      - description: List External ID
        example: '"list_123"'
        in: path
        name: list_external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            items:
              $ref: '#/definitions/handlers.GetDefaultPriceComparisonResponse'
            type: array
        "400":
          description: Bad Request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal Server Error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get price comparison recommendations
      tags:
      - PriceComparison
  /v1/product:
    get:
      consumes:
      - application/json
      description: Get active products
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Paginated list of products
          schema:
            $ref: '#/definitions/handlers.GetActiveProductsSuccessPaginatedResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get active products
      tags:
      - Product
    post:
      consumes:
      - multipart/form-data
      description: Create a new product
      parameters:
      - description: Product EAN
        example: "1234*********"
        in: formData
        name: ean
        required: true
        type: string
      - description: Product name
        example: Product Name
        in: formData
        name: name
        required: true
        type: string
      - description: Product image file
        in: formData
        name: image
        required: true
        type: file
      - description: Product categories
        in: formData
        name: categories
        required: true
        type: string
      - description: Product brand
        example: Brand XYZ
        in: formData
        name: brand
        required: true
        type: string
      - description: Is 18+ product
        example: false
        in: formData
        name: is_18_plus
        type: boolean
      produces:
      - application/json
      responses:
        "201":
          description: Product created
          schema:
            $ref: '#/definitions/handlers.CreateProductSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Create a new product
      tags:
      - Product
  /v1/product/{ean}:
    get:
      consumes:
      - application/json
      description: Get product by EAN
      parameters:
      - description: Product EAN
        example: "1234*********"
        in: path
        name: ean
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Product data
          schema:
            $ref: '#/definitions/handlers.GetActiveProductsSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get product by EAN
      tags:
      - Product
  /v1/product/{externalID}:
    patch:
      consumes:
      - multipart/form-data
      description: Update product image
      parameters:
      - description: Product image file
        in: formData
        name: image
        required: true
        type: file
      - description: External ID
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
        in: path
        name: externalID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Product image updated
          schema:
            type: object
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update product image
      tags:
      - Product
    put:
      consumes:
      - application/json
      description: Update a product
      parameters:
      - description: Product data
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateProductDataRequest'
      - description: External ID
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
        in: path
        name: externalID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Product updated
          schema:
            type: object
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update a product
      tags:
      - Product
  /v1/product/category/{externalID}:
    get:
      consumes:
      - application/json
      description: Get products grouped by category
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      - description: Category External ID
        example: 01F9ZQZQZQZQZQZQZQZQZQZQZ
        in: path
        name: externalID
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Paginated list of products
          schema:
            $ref: '#/definitions/handlers.GetActiveProductsGroupedByCategorySuccessPaginatedResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get products grouped by category
      tags:
      - Product
  /v1/search/{query}:
    get:
      consumes:
      - application/json
      deprecated: true
      description: Perform a full-text search (legacy endpoint)
      parameters:
      - description: Search query
        in: path
        name: query
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Search results
          schema:
            $ref: '#/definitions/common.SuccessResponse-array_handlers_SearchResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Legacy Search (deprecated)
      tags:
      - Search
  /v1/search/category/{category_id}/{query}:
    get:
      consumes:
      - application/json
      description: Search products within a specific category
      parameters:
      - description: Category external ID
        in: path
        name: category_id
        required: true
        type: string
      - description: Search query
        in: path
        name: query
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Search results with pagination
          schema:
            $ref: '#/definitions/handlers.SearchSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Category Product Search
      tags:
      - Search
  /v1/search/company/{company_id}/{query}:
    get:
      consumes:
      - application/json
      description: Search products within a specific company
      parameters:
      - description: Company external ID
        in: path
        name: company_id
        required: true
        type: string
      - description: Search query
        in: path
        name: query
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Search results with pagination
          schema:
            $ref: '#/definitions/handlers.SearchSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Company Product Search
      tags:
      - Search
  /v1/search/company/{company_id}/category/{category_id}/{query}:
    get:
      consumes:
      - application/json
      description: Search products within a specific company and category
      parameters:
      - description: Company external ID
        in: path
        name: company_id
        required: true
        type: string
      - description: Category external ID
        in: path
        name: category_id
        required: true
        type: string
      - description: Search query
        in: path
        name: query
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Search results with pagination
          schema:
            $ref: '#/definitions/handlers.SearchSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Company Category Product Search
      tags:
      - Search
  /v1/search/global/{query}:
    get:
      consumes:
      - application/json
      description: Search across all products in the system
      parameters:
      - description: Search query
        in: path
        name: query
        required: true
        type: string
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Search results with pagination
          schema:
            $ref: '#/definitions/handlers.SearchSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Global Product Search
      tags:
      - Search
  /v1/user:
    post:
      consumes:
      - application/json
      description: Create a new user
      parameters:
      - description: User payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateUserRequest'
      produces:
      - application/json
      responses:
        "201":
          description: ok
          schema:
            $ref: '#/definitions/handlers.CreateUserSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Create a new user
      tags:
      - Users
  /v1/user/address:
    post:
      consumes:
      - application/json
      description: Create or Update user address
      parameters:
      - description: User address payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/custom_models.AddressParams'
      produces:
      - application/json
      responses:
        "201":
          description: ok
          schema:
            $ref: '#/definitions/handlers.CreateUserAddressSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Upsert user address
      tags:
      - Users
  /v1/user/address/{address_external_id}:
    delete:
      consumes:
      - application/json
      description: Delete user address
      parameters:
      - description: Address external id
        in: path
        name: address_external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Delete user address
      tags:
      - Users
  /v1/user/exists/email:
    post:
      consumes:
      - application/json
      description: Check if email exists
      parameters:
      - description: User payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CheckIfEmailExistsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.CheckIfEmailExistsSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Check if email exists
      tags:
      - Users
  /v1/user/invoice:
    get:
      consumes:
      - application/json
      description: Get all invoices for the authenticated user with company information
        and products
      parameters:
      - description: 'Page number (default: 1)'
        in: query
        name: page
        type: integer
      - description: 'Items per page (default: 10)'
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: User invoices retrieved successfully
          schema:
            $ref: '#/definitions/handlers.GetInvoicesSuccessResponse'
        "400":
          description: Bad request - invalid pagination parameters
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get user invoices
      tags:
      - Users
  /v1/user/invoice/{order_id}/status:
    put:
      consumes:
      - application/json
      description: Update the status of a user's invoice (allows cancellation in specific
        states)
      parameters:
      - description: Order ID
        in: path
        name: order_id
        required: true
        type: string
      - description: Status update payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateStatusRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Invoice status updated successfully
          schema:
            $ref: '#/definitions/handlers.UpdateInvoiceStatusSuccessResponse'
        "400":
          description: Bad request - invalid status or transition
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "403":
          description: Forbidden - user doesn't own invoice or invalid cancellation
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "404":
          description: Invoice not found
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update user invoice status
      tags:
      - Users
  /v1/user/list:
    get:
      consumes:
      - application/json
      description: Get user products list
      parameters:
      - description: Page
        in: query
        name: page
        type: string
      - description: Limit
        in: query
        name: limit
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get user products list
      tags:
      - Users
    post:
      consumes:
      - application/json
      description: Create or Update a products list
      parameters:
      - description: Products list payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.CreateProductsListRequest'
      produces:
      - application/json
      responses:
        "201":
          description: ok
          schema:
            $ref: '#/definitions/handlers.CreateProductsListSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Upsert a products list
      tags:
      - Users
  /v1/user/list/{list_external_id}:
    delete:
      consumes:
      - application/json
      description: Delete user products list
      parameters:
      - description: List external id
        in: path
        name: list_external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Delete user products list
      tags:
      - Users
    get:
      consumes:
      - application/json
      description: Get user products list by external id
      parameters:
      - description: List external id
        in: path
        name: list_external_id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get user products list by external id
      tags:
      - Users
  /v1/user/list/icons:
    get:
      consumes:
      - application/json
      description: Get user products list icons
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            additionalProperties:
              type: string
            type: object
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get user products list icons
      tags:
      - Users
  /v1/user/list/template:
    get:
      consumes:
      - application/json
      description: Get random template products list
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.GetProductsListSuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      summary: Get random template products list
      tags:
      - Users
  /v1/user/me:
    delete:
      consumes:
      - application/json
      description: Delete user
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Delete user
      tags:
      - Users
    get:
      consumes:
      - application/json
      description: Get user info
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.GetMeSuccessResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Get user info
      tags:
      - Users
  /v1/user/name:
    patch:
      consumes:
      - application/json
      description: Update name
      parameters:
      - description: User name payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.UpdateUserNameRequest'
      produces:
      - application/json
      responses:
        "200":
          description: ok
          schema:
            $ref: '#/definitions/handlers.EmptySuccessResponse'
        "400":
          description: bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Update name
      tags:
      - Users
  /v1/user/role:
    post:
      consumes:
      - application/json
      description: Assign a role to a user
      parameters:
      - description: Assign role payload
        in: body
        name: payload
        required: true
        schema:
          $ref: '#/definitions/handlers.AssignRoleToUserRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Role assigned successfully
          schema:
            $ref: '#/definitions/handlers.AssignRoleToUserSuccessResponse'
        "400":
          description: Bad request
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "401":
          description: Unauthorized
          schema:
            $ref: '#/definitions/common.ErrorResponse'
        "500":
          description: Internal server error
          schema:
            $ref: '#/definitions/common.ErrorResponse'
      security:
      - Bearer: []
      summary: Assign role to user
      tags:
      - Users
securityDefinitions:
  ApiKeyAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
  Bearer:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
