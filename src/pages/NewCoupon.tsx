import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { couponService } from "@/services/api";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tag } from "lucide-react";
import { toast } from "sonner";

const formSchema = z.object({
  code: z.string()
    .min(1, "Código é obrigatório")
    .max(20, "Código deve ter no máximo 20 caracteres")
    .regex(/^[A-Z0-9]+$/, "Código deve conter apenas letras maiúsculas e números"),
  type: z.enum(["percentage", "fixed"], {
    required_error: "Tipo é obrigatório",
  }),
  value: z.string()
    .min(1, "Valor é obrigatório")
    .refine((val): val is string => {
      const num = parseFloat(val);
      return !isNaN(num) && num > 0;
    }, "Valor deve ser maior que zero"),
  min_order_value: z.string()
    .min(1, "Valor mínimo é obrigatório")
    .refine((val): val is string => {
      const num = parseFloat(val);
      return !isNaN(num) && num >= 50;
    }, "Valor mínimo deve ser pelo menos R$ 50,00"),
  quantity: z.string()
    .min(1, "Quantidade é obrigatória")
    .refine((val): val is string => {
      const num = parseInt(val);
      return !isNaN(num) && num > 0 && num <= 1000;
    }, "Quantidade deve estar entre 1 e 1000"),
  expires_at: z.string()
    .min(1, "Data de expiração é obrigatória")
    .refine((val): val is string => {
      const date = new Date(val);
      return date > new Date();
    }, "Data de expiração deve ser futura"),
  owner_type: z.enum(["company", "admin"], {
    required_error: "Tipo de proprietário é obrigatório",
  }),
});

type FormValues = z.infer<typeof formSchema>;

export function NewCoupon() {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      code: "",
      type: "percentage",
      value: "",
      min_order_value: "",
      quantity: "",
      expires_at: "",
      owner_type: "admin",
    },
  });

  const createCouponMutation = useMutation({
    mutationFn: async (values: FormValues) => {
      const formattedDate = new Date(values.expires_at).toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }).replace(',', '');

      // Convert values based on type - no multiplication needed
      const value = parseFloat(values.value);

      return couponService.createCoupon({
        code: values.code,
        type: values.type,
        value: value,
        min_order_value: parseFloat(values.min_order_value),
        quantity: parseInt(values.quantity),
        expires_at: formattedDate,
        owner_type: values.owner_type
      });
    },
    onSuccess: async () => {
      try {
        // First invalidate the queries
        await queryClient.invalidateQueries({ queryKey: ["coupons"] });
        // Then show success message
        toast.success("Cupom criado com sucesso!", {
          dismissible: true,
        });
        // Finally navigate
        navigate("/admin/coupons", { replace: true });
      } catch (error) {
        console.error("Error during success handling:", error);
        toast.error("Erro ao finalizar criação do cupom", {
          dismissible: true,
        });
      }
    },
    onError: (error: Error) => {
      toast.error("Erro ao criar cupom. Por favor, tente novamente.", {
        dismissible: true,
      });
    },
    onSettled: () => {
      setIsSubmitting(false);
    }
  });

  const onSubmit = (values: FormValues) => {
    setIsSubmitting(true);
    createCouponMutation.mutate(values);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold tracking-tight">Novo Cupom</h2>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            <div className="flex items-center">
              <Tag className="mr-2" size={20} />
              Criar Cupom de Desconto
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Código</FormLabel>
                    <FormControl>
                      <Input placeholder="Ex: SUMMER2024" {...field} />
                    </FormControl>
                    <FormDescription>
                      Use apenas letras maiúsculas e números. Ex: SUMMER2024, BLACKFRIDAY
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Desconto</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="percentage">Porcentagem</SelectItem>
                        <SelectItem value="fixed">Valor Fixo</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Porcentagem: desconto baseado em % do valor total
                      <br />
                      Valor Fixo: desconto de valor específico
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      {form.watch("type") === "percentage"
                        ? "Porcentagem de Desconto"
                        : "Valor do Desconto"}
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder={
                          form.watch("type") === "percentage"
                            ? "Ex: 15"
                            : "Ex: 50.00"
                        }
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      {form.watch("type") === "percentage"
                        ? "Valor entre 1% e 100%"
                        : "Valor em reais (R$)"}
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="min_order_value"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Valor Mínimo do Pedido</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        placeholder="Ex: 100.00"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Valor mínimo em reais (R$) para aplicar o cupom. Mínimo: R$ 50,00
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantidade Disponível</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Ex: 100"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      Número de vezes que o cupom pode ser utilizado. Máximo: 1000
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="expires_at"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Data de Expiração</FormLabel>
                    <FormControl>
                      <Input type="datetime-local" {...field} />
                    </FormControl>
                    <FormDescription>
                      Data e hora em que o cupom expirará. Deve ser uma data futura.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="owner_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tipo de Proprietário</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o tipo" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="admin">Administrador</SelectItem>
                        <SelectItem value="company">Empresa</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Admin: cupom disponível para todas as empresas
                      <br />
                      Empresa: cupom disponível apenas para a empresa específica
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => navigate("/admin/coupons")}
                >
                  Cancelar
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Criando..." : "Criar Cupom"}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}

export default NewCoupon; 