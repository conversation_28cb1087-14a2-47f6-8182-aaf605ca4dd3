import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { CurrencyInput } from "@/components/ui/currency-input";
import { toast } from "sonner";
import { Loader2, DollarSign, CreditCard, AlertCircle } from "lucide-react";
import { companyService } from "@/services/api";

import { formatCurrency } from "@/utils/formatters";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

// Form validation schema
const withdrawalFormSchema = z.object({
  amount: z.number()
    .min(50000, "Valor mínimo para saque é R$ 500,00")
    .max(99999999, "Valor máximo excedido"),
});

type WithdrawalFormValues = z.infer<typeof withdrawalFormSchema>;

interface WithdrawalModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type ModalStep = 'amount' | 'confirmation';

const WithdrawalModal: React.FC<WithdrawalModalProps> = ({ open, onOpenChange }) => {
  const [currentStep, setCurrentStep] = useState<ModalStep>('amount');
  const [withdrawalAmount, setWithdrawalAmount] = useState<number>(0);
  const queryClient = useQueryClient();

  const { handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<WithdrawalFormValues>({
    resolver: zodResolver(withdrawalFormSchema),
    defaultValues: {
      amount: 0,
    },
  });

  const watchedAmount = watch('amount');

  // Fetch balance data
  const { data: balanceData, isLoading: isLoadingBalance, error: balanceError } = useQuery({
    queryKey: ["partner-balance"],
    queryFn: async () => {
      const response = await companyService.getBalance();
      return response.data;
    },
    enabled: open, // Only fetch when modal is open
    staleTime: 30000, // 30 seconds
  });

  // Withdrawal mutation
  const withdrawalMutation = useMutation({
    mutationFn: async (amount: number) => {
      return await companyService.withdraw({ amount });
    },
    onSuccess: (response) => {
      const successMessage = response.data?.message || "Saque realizado com sucesso! O valor será processado em até 24 horas.";
      toast.success(successMessage, {
        dismissible: true,
        duration: 5000,
      });
      // Invalidate balance query to refresh data
      queryClient.invalidateQueries({ queryKey: ["partner-balance"] });
      handleCloseModal();
    },
    onError: (error: any) => {
      console.error("Withdrawal error:", error);
      let errorMessage = "Erro ao processar saque. Tente novamente.";

      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.status === 400) {
        errorMessage = "Dados inválidos. Verifique o valor do saque.";
      } else if (error.response?.status === 403) {
        errorMessage = "Saldo insuficiente ou operação não autorizada.";
      } else if (error.response?.status >= 500) {
        errorMessage = "Erro no servidor. Tente novamente mais tarde.";
      }

      toast.error(errorMessage, {
        dismissible: true,
        duration: 5000,
      });
    },
  });

  // Handle modal close and reset
  const handleCloseModal = () => {
    setCurrentStep('amount');
    setWithdrawalAmount(0);
    reset();
    onOpenChange(false);
  };

  // Handle amount form submission
  const onAmountSubmit = (data: WithdrawalFormValues) => {
    setWithdrawalAmount(data.amount);
    setCurrentStep('confirmation');
  };

  // Handle withdrawal confirmation
  const handleConfirmWithdrawal = () => {
    withdrawalMutation.mutate(withdrawalAmount);
  };

  // Handle "Sacar Total" button
  const handleWithdrawAll = () => {
    if (balanceData && balanceData.balance >= 50000) {
      setValue('amount', balanceData.balance);
    }
  };

  const balance = balanceData?.balance || 0;
  const partnerName = balanceData?.name || '';
  const pixKey = balanceData?.pix_key || '';
  const canWithdraw = balance >= 50000;

  // Validation helpers
  const isAmountValid = watchedAmount >= 50000 && watchedAmount <= balance;
  const isAmountTooLow = watchedAmount > 0 && watchedAmount < 50000;
  const isAmountTooHigh = watchedAmount > balance;

  // Reset modal state when closed
  useEffect(() => {
    if (!open) {
      setCurrentStep('amount');
      setWithdrawalAmount(0);
      reset();
    }
  }, [open, reset]);

  // Loading state
  if (isLoadingBalance) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] mx-4">
          <div className="flex items-center justify-center py-8">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-sm sm:text-base">Carregando dados do saldo...</p>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // Error state
  if (balanceError) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[425px] mx-4">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-sm sm:text-base">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Erro ao Carregar Saldo
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground">
              Não foi possível carregar os dados do saldo. Tente novamente mais tarde.
            </p>
          </div>
          <DialogFooter>
            <Button onClick={handleCloseModal} className="w-full sm:w-auto">Fechar</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] mx-4 max-h-[90vh] overflow-y-auto">
        {currentStep === 'amount' && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Sacar Saldo
              </DialogTitle>
              <DialogDescription>
                Informe o valor que deseja sacar do seu saldo disponível.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Partner Info */}
              <div className="bg-gray-50 p-3 sm:p-4 rounded-lg space-y-2">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="text-sm font-medium text-gray-600">Parceiro:</span>
                  <span className="text-sm font-semibold break-words">{partnerName}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="text-sm font-medium text-gray-600">Chave PIX:</span>
                  <span className="text-sm font-mono break-all">{pixKey}</span>
                </div>
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span className="text-sm font-medium text-gray-600">Saldo Disponível:</span>
                  <span className="text-lg font-bold text-green-600">
                    {formatCurrency(balance / 100)}
                  </span>
                </div>
              </div>

              {!canWithdraw && (
                <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <p className="text-sm text-yellow-800">
                      Saldo insuficiente. Valor mínimo para saque: R$ 500,00
                    </p>
                  </div>
                </div>
              )}

              {canWithdraw && (
                <form onSubmit={handleSubmit(onAmountSubmit)} className="space-y-4">
                  <div className="space-y-2">
                    <label htmlFor="amount" className="text-sm font-medium">
                      Valor do Saque
                    </label>
                    <CurrencyInput
                      id="amount"
                      value={watchedAmount}
                      onValueChange={(value) => setValue('amount', value)}
                      placeholder="R$ 0,00"
                      className={errors.amount || isAmountTooLow || isAmountTooHigh ? "border-red-500" : isAmountValid ? "border-green-500" : ""}
                    />
                    {errors.amount && (
                      <p className="text-xs text-red-500">{errors.amount.message}</p>
                    )}
                    {!errors.amount && isAmountTooLow && (
                      <p className="text-xs text-red-500">Valor mínimo para saque é R$ 500,00</p>
                    )}
                    {!errors.amount && isAmountTooHigh && (
                      <p className="text-xs text-red-500">Valor não pode ser maior que o saldo disponível</p>
                    )}
                    {!errors.amount && isAmountValid && (
                      <p className="text-xs text-green-600">Valor válido para saque</p>
                    )}
                  </div>

                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleWithdrawAll}
                      className="flex-1 text-sm"
                    >
                      Sacar Total
                    </Button>
                  </div>

                  <DialogFooter className="flex-col sm:flex-row gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleCloseModal}
                      className="w-full sm:w-auto order-2 sm:order-1"
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={!isAmountValid}
                      className="w-full sm:w-auto order-1 sm:order-2"
                    >
                      Continuar
                    </Button>
                  </DialogFooter>
                </form>
              )}

              {!canWithdraw && (
                <DialogFooter>
                  <Button onClick={handleCloseModal}>Fechar</Button>
                </DialogFooter>
              )}
            </div>
          </>
        )}

        {currentStep === 'confirmation' && (
          <>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Confirmar Saque
              </DialogTitle>
              <DialogDescription>
                Confirme os dados do seu saque antes de prosseguir.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              {/* Confirmation Details */}
              <div className="bg-blue-50 p-3 sm:p-4 rounded-lg space-y-3">
                <h3 className="font-semibold text-blue-900 text-sm sm:text-base">Detalhes do Saque</h3>
                <div className="space-y-2">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="text-sm text-blue-700">Parceiro:</span>
                    <span className="text-sm font-semibold text-blue-900 break-words">{partnerName}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="text-sm text-blue-700">Destino (PIX):</span>
                    <span className="text-sm font-mono text-blue-900 break-all">{pixKey}</span>
                  </div>
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span className="text-sm text-blue-700">Valor do Saque:</span>
                    <span className="text-lg font-bold text-blue-900">
                      {formatCurrency(withdrawalAmount / 100)}
                    </span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">
                  <strong>Importante:</strong> O valor será transferido via PIX para a chave cadastrada. 
                  O processamento pode levar até 24 horas úteis.
                </p>
              </div>

              <DialogFooter className="flex-col sm:flex-row gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setCurrentStep('amount')}
                  disabled={withdrawalMutation.isPending}
                  className="w-full sm:w-auto order-2 sm:order-1"
                >
                  Voltar
                </Button>
                <Button
                  onClick={handleConfirmWithdrawal}
                  disabled={withdrawalMutation.isPending}
                  className="w-full sm:w-auto order-1 sm:order-2"
                >
                  {withdrawalMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Confirmar Saque
                </Button>
              </DialogFooter>
            </div>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default WithdrawalModal;
