// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package postgres

import (
	"database/sql"
	"time"

	custom_models "github.com/izy-mercado/backend/pkg/storage/postgres/custom_models"
	"github.com/jackc/pgtype"
)

type Campaign struct {
	ID         int32
	Name       string
	Duration   int32
	ExternalID string
	IsActive   bool
	AdminOnly  bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type Category struct {
	ID         int32
	Name       string
	Image      string
	ExternalID string
	IsActive   bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type Company struct {
	ID               int32
	Name             string
	Cnpj             string
	Bio              string
	Picture          string
	PhoneNumbers     []string
	PixKey           string
	CommissionRate   int32
	CashbackRate     int32
	SubscriptionID   sql.NullInt32
	ShippingFee      int32
	Rating           float64
	OwnerID          sql.NullInt32
	AffiliateBalance int32
	ExternalID       string
	IsActive         bool
	DeliveryModes    []string
	CreatedAt        time.Time
	UpdatedAt        time.Time
}

type CompanyActiveCampaign struct {
	CompanyID  int32
	CampaignID int32
}

type CompanyAddress struct {
	ID           int32
	CompanyID    int32
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
	Latitude     sql.NullFloat64
	Longitude    sql.NullFloat64
	Location     interface{}
	IsDefault    bool
	ExternalID   string
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

type CompanyAvailableBalance struct {
	CompanyID            int32
	AvailableBalance     interface{}
	AvailablePayoutCount int64
	LatestAvailableDate  interface{}
}

type CompanyProduct struct {
	CompanyID int32
	ProductID int32
	Price     pgtype.Numeric
	Discount  pgtype.Numeric
	Stock     int32
	CreatedAt time.Time
}

// Tracks all withdrawal requests made by partner companies through Woovi sub-accounts
type CompanyWithdrawal struct {
	ID        int64
	CompanyID int64
	// Withdrawal amount in cents (Brazilian centavos)
	Amount int32
	// Woovi API correlation ID for tracking the withdrawal request
	CorrelationID sql.NullString
	// PIX key of the destination account
	DestinationPixKey sql.NullString
	Comment           sql.NullString
	EndToEndID        sql.NullString
	// Complete Woovi API response stored as JSONB for audit purposes
	WooviResponse pgtype.JSONB
	// Current status of the withdrawal: CREATED (initial), CONFIRMED (confirmed by webhook), FAILED (error occurred)
	Status string
	// Timestamp when the withdrawal was completed or failed, set by webhook callback
	FinishedAt sql.NullTime
	CreatedAt  time.Time
}

type Coupon struct {
	ID   int32
	Code string
	Type string
	// Coupon value in cents (e.g., 250 = R$ 2.50)
	Value     int32
	Quantity  int32
	IsActive  bool
	ExpiresAt time.Time
	// Minimum order value in cents (e.g., 1500 = R$ 15.00)
	MinOrderValue int32
	OwnerType     string
	OwnerID       int32
	ExternalID    string
	CreatedAt     sql.NullTime
	UpdatedAt     sql.NullTime
}

type Invoice struct {
	ID        int64
	UserID    int32
	CompanyID int32
	// Invoice status: pending (awaiting payment), failed (payment/system failure), expired (payment timeout), processing (payment approved), preparing (order being prepared), ready (ready for pickup/delivery), delivering (out for delivery), completed (finished), cancelled (intentionally cancelled by user or company)
	Status        string
	OrderID       string
	PaymentMethod string
	Amount        int32
	Discount      int32
	// Shipping fee at time of invoice creation in cents (e.g., 500 = R$ 5.00)
	ShippingFee     int32
	DeliveryMode    string
	Coupon          sql.NullString
	UserAddress     sql.NullString
	UserPhoneNumber sql.NullString
	Info            sql.NullString
	InfoDetails     pgtype.JSONB
	FinishedAt      sql.NullTime
	CreatedAt       time.Time
	UpdatedAt       time.Time
	// Amount received by the partner/company after checkout (Woovi split) in cents (e.g., 1000 = R$ 10.00)
	CompanyAmount int32
}

// Tracks individual invoice payouts for partner withdrawal control with 3-day holding period
type InvoicePayout struct {
	ID        int64
	InvoiceID int64
	CompanyID int32
	// Payout status: pending (waiting for completion + 3 days), available (ready for withdrawal), withdrawn (used in withdrawal), blocked (administrative hold)
	Status string
	// Net payout amount in cents after platform commission deduction
	Amount int32
	// Timestamp when the related invoice was completed (status = completed)
	FinalizedAt sql.NullTime
	// Timestamp when funds become available for withdrawal (finalized_at + 3 days)
	AvailableAfter sql.NullTime
	CreatedAt      time.Time
	UpdatedAt      time.Time
}

// Normalized invoice line items replacing the JSONB products column
type InvoiceProduct struct {
	ID        int64
	InvoiceID int64
	ProductID int32
	Quantity  int32
	// Product price at time of purchase in cents (e.g., 1000 = R$ 10.00)
	UnitPrice int32
	// Individual product discount in cents (e.g., 250 = R$ 2.50)
	Discount int32
	// Product name stored at time of purchase for historical accuracy
	ProductName string
	// Product EAN stored at time of purchase for historical accuracy
	ProductEan string
	// Product external_id stored at time of purchase for historical accuracy
	ProductExternalID string
	ProductBrand      sql.NullString
	ProductImage      sql.NullString
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

type Product struct {
	ID          int32
	Name        string
	Ean         string
	Description sql.NullString
	Image       sql.NullString
	Brand       sql.NullString
	IsReviewed  bool
	IsActive    bool
	Is18Plus    bool
	ExternalID  string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type ProductsCategory struct {
	ProductID  int32
	CategoryID int32
}

// Secure push tokens generated by backend for device authentication
type PushToken struct {
	ID     int32
	UserID int32
	// Secure random token generated by backend (base64 encoded)
	PushToken string
	// Unique device identifier provided by client
	DeviceID string
	// Device platform: android, ios, or web
	Platform string
	// Firebase Cloud Messaging token for the device
	FcmToken string
	// Version of the mobile application
	AppVersion string
	// Device model information (optional)
	DeviceModel sql.NullString
	// Operating system version (optional)
	OsVersion sql.NullString
	// Token expiration timestamp (90 days from creation)
	ExpiresAt time.Time
	// Last time this token was used for authentication
	LastUsedAt sql.NullTime
	CreatedAt  time.Time
	UpdatedAt  time.Time
	// Application type: mobile or partner-web. Allows same device to have different FCM tokens for different applications
	ApplicationType string
}

type Role struct {
	ID          int32
	Name        string
	Description string
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

type Session struct {
	ID           int32
	UserID       int32
	AccessToken  string
	RefreshToken string
	IsActive     bool
	Device       string
	Ip           string
	// Application type: mobile or partner-web. Each user can have only one active session per application type
	ApplicationType string
	CreatedAt       time.Time
	EndedAt         time.Time
}

type Subscription struct {
	ID         int32
	Name       string
	Price      int32
	Discount   int32
	Picture    string
	IsActive   bool
	ExternalID string
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type TemplatesProductsList struct {
	ID         int32
	Name       string
	IsActive   bool
	ExternalID string
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type TemplatesProductsListsItem struct {
	TemplateID int32
	ProductID  int32
	Quantity   int32
}

type User struct {
	ID                int32
	Name              string
	Email             string
	LoginCode         string
	Cpf               string
	PhoneNumbers      []string
	CashbackValue     int32
	SubscriptionID    sql.NullInt32
	IsActive          bool
	IsDeleted         bool
	ExternalID        string
	FcmToken          sql.NullString
	FcmTokenUpdatedAt sql.NullTime
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

type UserAddress struct {
	ID           int32
	UserID       int32
	Name         string
	Street       string
	Number       string
	Complement   sql.NullString
	Neighborhood string
	City         string
	State        string
	ZipCode      string
	Latitude     sql.NullFloat64
	Longitude    sql.NullFloat64
	Location     custom_models.Location
	IsDefault    bool
	ExternalID   string
	CreatedAt    time.Time
	UpdatedAt    time.Time
}

type UserProductsList struct {
	ID         int32
	UserID     int32
	Name       string
	IconUrl    string
	ExternalID string
	IsPublic   bool
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type UserProductsListsItem struct {
	ListID    int32
	ProductID int32
	Quantity  int32
}

type UserRole struct {
	UserID    int32
	RoleID    int32
	CreatedAt time.Time
}

type UsersCoupon struct {
	ID        int64
	UserID    int32
	CouponID  int32
	UsedAt    sql.NullTime
	CreatedAt sql.NullTime
	UpdatedAt sql.NullTime
}

// Junction table tracking which invoice payouts were used in each company withdrawal
type WithdrawalPayout struct {
	ID           int64
	WithdrawalID int64
	PayoutID     int64
	CreatedAt    time.Time
}
