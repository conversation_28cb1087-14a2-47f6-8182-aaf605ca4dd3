-- Create invoice_payouts table for tracking partner withdrawal control
CREATE TABLE IF NOT EXISTS invoice_payouts (
    id BIGSERIAL PRIMARY KEY,
    invoice_id BIGINT NOT NULL REFERENCES invoices(id) ON DELETE CASCADE,
    company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'available', 'withdrawn', 'blocked')),
    amount INTEGER NOT NULL CHECK (amount > 0), -- Net amount in cents after platform commission
    finalized_at TIMESTAMP, -- When invoice was completed (invoices.finished_at)
    available_after TIMESTAMP, -- finalized_at + 3 days (when funds become available)
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Ensure one payout per invoice
    UNIQUE(invoice_id)
);

-- Create junction table for tracking which payouts were used in each withdrawal
CREATE TABLE IF NOT EXISTS withdrawal_payouts (
    id BIGSERIAL PRIMARY KEY,
    withdrawal_id BIGINT NOT NULL REFERENCES company_withdrawals(id) ON DELETE CASCADE,
    payout_id BIGINT NOT NULL REFERENCES invoice_payouts(id) ON DELETE CASCADE,
    created_at TIMESTAMP NOT NULL DEFAULT NOW(),
    
    -- Ensure each payout can only be used once per withdrawal
    UNIQUE(withdrawal_id, payout_id)
);

-- Add performance indexes for invoice_payouts
-- Composite index for balance calculations (most frequent query)
CREATE INDEX idx_invoice_payouts_company_status_available ON invoice_payouts(company_id, status, available_after) 
WHERE status IN ('available', 'pending');

-- Index for status updates and filtering
CREATE INDEX idx_invoice_payouts_status ON invoice_payouts(status);

-- Index for time-based queries (available_after for batch processing)
CREATE INDEX idx_invoice_payouts_available_after ON invoice_payouts(available_after) 
WHERE status = 'pending';

-- Index for invoice lookups
CREATE INDEX idx_invoice_payouts_invoice_id ON invoice_payouts(invoice_id);

-- Index for company-specific queries
CREATE INDEX idx_invoice_payouts_company_id ON invoice_payouts(company_id);

-- Add performance indexes for withdrawal_payouts
-- Index for withdrawal lookup (audit trail)
CREATE INDEX idx_withdrawal_payouts_withdrawal_id ON withdrawal_payouts(withdrawal_id);

-- Index for payout lookup (prevent double usage)
CREATE INDEX idx_withdrawal_payouts_payout_id ON withdrawal_payouts(payout_id);

-- Add comments for documentation
COMMENT ON TABLE invoice_payouts IS 'Tracks individual invoice payouts for partner withdrawal control with 3-day holding period';
COMMENT ON COLUMN invoice_payouts.amount IS 'Net payout amount in cents after platform commission deduction';
COMMENT ON COLUMN invoice_payouts.status IS 'Payout status: pending (waiting for completion + 3 days), available (ready for withdrawal), withdrawn (used in withdrawal), blocked (administrative hold)';
COMMENT ON COLUMN invoice_payouts.finalized_at IS 'Timestamp when the related invoice was completed (status = completed)';
COMMENT ON COLUMN invoice_payouts.available_after IS 'Timestamp when funds become available for withdrawal (finalized_at + 3 days)';

COMMENT ON TABLE withdrawal_payouts IS 'Junction table tracking which invoice payouts were used in each company withdrawal';

-- Create materialized view for fast balance calculations
CREATE MATERIALIZED VIEW company_available_balances AS
SELECT 
    company_id,
    COALESCE(SUM(amount), 0) as available_balance,
    COUNT(*) as available_payout_count,
    MAX(available_after) as latest_available_date
FROM invoice_payouts 
WHERE status = 'available' 
  AND available_after <= NOW()
GROUP BY company_id;

-- Add index to materialized view
CREATE UNIQUE INDEX idx_company_available_balances_company_id ON company_available_balances(company_id);

-- Add comment for materialized view
COMMENT ON MATERIALIZED VIEW company_available_balances IS 'Materialized view for fast company balance lookups - refresh periodically via CRON';

-- Create function to automatically create payout records when invoices are completed
CREATE OR REPLACE FUNCTION create_invoice_payout()
RETURNS TRIGGER AS $$
BEGIN
    -- Only create payout when invoice status changes to 'completed'
    IF NEW.status = 'completed' AND (OLD.status IS NULL OR OLD.status != 'completed') THEN
        -- Calculate net amount (invoice amount - platform commission)
        -- Platform commission is already handled by Woovi, so we use the full amount
        INSERT INTO invoice_payouts (
            invoice_id,
            company_id,
            amount,
            finalized_at,
            available_after,
            status
        ) VALUES (
            NEW.id,
            NEW.company_id,
            NEW.amount, -- Net amount (commission already deducted by Woovi)
            NEW.finished_at,
            NEW.finished_at + INTERVAL '3 days',
            'pending'
        )
        ON CONFLICT (invoice_id) DO NOTHING; -- Prevent duplicates
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically create payouts
CREATE TRIGGER trigger_create_invoice_payout
    AFTER UPDATE ON invoices
    FOR EACH ROW
    EXECUTE FUNCTION create_invoice_payout();

-- Create function to update payout status from pending to available
CREATE OR REPLACE FUNCTION update_available_payouts()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE invoice_payouts
    SET status = 'available', updated_at = NOW()
    WHERE status = 'pending'
      AND available_after <= NOW();

    GET DIAGNOSTICS updated_count = ROW_COUNT;
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- Add comment for the function
COMMENT ON FUNCTION update_available_payouts() IS 'Batch function to update pending payouts to available status - call via CRON job';
