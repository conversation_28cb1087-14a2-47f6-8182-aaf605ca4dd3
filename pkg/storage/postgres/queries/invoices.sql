-- name: CreateInvoice :one
INSERT INTO invoices (user_id, company_id, coupon, status, order_id, info, info_details, payment_method, amount, discount, user_address, user_phone_number, shipping_fee, delivery_mode)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
RETURNING id;

-- name: CreateInvoiceProduct :exec
INSERT INTO invoice_products (invoice_id, product_id, quantity, unit_price, discount, product_name, product_ean, product_external_id, product_brand, product_image)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10);

-- name: UpdateInvoiceStatusByOrderID :one
UPDATE invoices
SET status = $2, info = $3, updated_at = now(), finished_at = $4
WHERE order_id = $1
RETURNING id, status, order_id, user_id, company_id;


-- name: GetUserInvoices :many
SELECT
  i.id,
  i.user_id,
  i.company_id,
  i.status,
  i.order_id,
  i.payment_method,
  i.amount,
  i.discount,
  i.coupon,
  i.shipping_fee,
  i.delivery_mode,
  i.user_address,
  i.user_phone_number,
  i.info,
  i.info_details,
  i.finished_at,
  i.created_at,
  i.updated_at,
  c.name as company_name,
  c.cnpj as company_cnpj,
  c.bio as company_bio,
  c.picture as company_picture,
  c.phone_numbers as company_phone_numbers,
  c.pix_key as company_pix_key,
  c.external_id as company_external_id,
  COALESCE(
    jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', COALESCE(ca.latitude, 0),
        'longitude', COALESCE(ca.longitude, 0)
      )
    ),
    jsonb_build_object(
      'external_id', '',
      'name', '',
      'street', '',
      'number', '',
      'complement', '',
      'neighborhood', '',
      'city', '',
      'state', '',
      'zip_code', '',
      'location', jsonb_build_object(
        'latitude', 0,
        'longitude', 0
      )
    )
  )::jsonb as company_address,
  COALESCE(
    json_agg(
      json_build_object(
        'id', ip.id,
        'product_id', ip.product_id,
        'quantity', ip.quantity,
        'unit_price', ip.unit_price,
        'discount', ip.discount,
        'product_name', ip.product_name,
        'product_ean', ip.product_ean,
        'product_external_id', ip.product_external_id,
        'product_brand', ip.product_brand,
        'product_image', ip.product_image,
        'product_categories', (
          SELECT json_agg(DISTINCT jsonb_build_object(
            'name', cat.name,
            'image', cat.image,
            'external_id', cat.external_id
          ))
          FROM products_categories pc
          JOIN categories cat ON cat.id = pc.category_id
          WHERE pc.product_id = ip.product_id
        )
      )
    ) FILTER (WHERE ip.id IS NOT NULL),
    '[]'::json
  ) AS products,
  COUNT(*) OVER() as total_count
FROM invoices i
LEFT JOIN invoice_products ip ON i.id = ip.invoice_id
LEFT JOIN companies c ON i.company_id = c.id
LEFT JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
WHERE i.user_id = $1 AND i.created_at >= NOW() - INTERVAL '90 days'
GROUP BY i.id, c.name, c.cnpj, c.bio, c.picture, c.phone_numbers, c.pix_key, c.external_id, ca.external_id, ca.name, ca.street, ca.number, ca.complement, ca.neighborhood, ca.city, ca.state, ca.zip_code, ca.latitude, ca.longitude
ORDER BY
  CASE i.status
    WHEN 'pending' THEN 1
    WHEN 'processing' THEN 2
    WHEN 'preparing' THEN 3
    WHEN 'ready' THEN 4
    WHEN 'delivering' THEN 5
    WHEN 'expired' THEN 6
    WHEN 'failed' THEN 7
    WHEN 'completed' THEN 8
    WHEN 'cancelled' THEN 9
    ELSE 10
  END ASC,
  i.created_at DESC
LIMIT $2 OFFSET $3;

-- name: GetCompanyInvoices :many
SELECT
  i.id,
  i.user_id,
  i.company_id,
  i.status,
  i.order_id,
  i.payment_method,
  u.name as user_name,
  u.email as user_email,
  u.cpf as user_cpf,
  i.user_address,
  i.user_phone_number,
  i.amount,
  i.discount,
  i.coupon,
  i.shipping_fee,
  i.delivery_mode,
  i.info,
  i.info_details,
  i.finished_at,
  i.created_at,
  i.updated_at,
  c.external_id as company_external_id,
  COALESCE(
    json_agg(
      json_build_object(
        'id', ip.id,
        'product_id', ip.product_id,
        'quantity', ip.quantity,
        'unit_price', ip.unit_price,
        'discount', ip.discount,
        'product_name', ip.product_name,
        'product_ean', ip.product_ean,
        'product_external_id', ip.product_external_id,
        'product_brand', ip.product_brand,
        'product_image', ip.product_image
      )
    ) FILTER (WHERE ip.id IS NOT NULL),
    '[]'::json
  ) AS products,
  COUNT(*) OVER() as total_count
FROM invoices i
LEFT JOIN invoice_products ip ON i.id = ip.invoice_id
LEFT JOIN users u ON i.user_id = u.id
INNER JOIN companies c ON i.company_id = c.id
WHERE c.owner_id = $1 AND i.status != 'pending' AND i.status != 'failed' AND i.status != 'expired' AND i.created_at >= NOW() - INTERVAL '90 days'
GROUP BY  u.cpf, u.name, u.email, i.id, c.external_id
ORDER BY
  CASE i.status
    WHEN 'pending' THEN 1
    WHEN 'processing' THEN 2
    WHEN 'preparing' THEN 3
    WHEN 'ready' THEN 4
    WHEN 'delivering' THEN 5
    WHEN 'expired' THEN 6
    WHEN 'failed' THEN 7
    WHEN 'completed' THEN 8
    WHEN 'cancelled' THEN 9
    ELSE 10
  END ASC,
  i.created_at DESC
LIMIT $2 OFFSET $3;

-- name: GetInvoiceByOrderID :one
SELECT
  i.id,
  i.user_id,
  i.company_id,
  i.status,
  i.order_id,
  i.payment_method,
  i.amount,
  i.discount,
  i.coupon,
  i.shipping_fee,
  i.user_address,
  i.user_phone_number,
  i.info,
  i.info_details,
  i.finished_at,
  i.created_at,
  i.updated_at,
  c.name as company_name,
  c.cnpj as company_cnpj,
  c.bio as company_bio,
  c.picture as company_picture,
  c.phone_numbers as company_phone_numbers,
  c.pix_key as company_pix_key,
  c.external_id as company_external_id
FROM invoices i
LEFT JOIN companies c ON i.company_id = c.id
WHERE i.order_id = $1;

-- name: GetUserInvoiceByOrderID :one
SELECT
  i.id,
  i.user_id,
  i.company_id,
  i.status,
  i.order_id,
  i.payment_method,
  i.amount,
  i.discount,
  i.coupon,
  i.shipping_fee,
  i.delivery_mode,
  i.user_address,
  i.user_phone_number,
  i.info,
  i.info_details,
  i.finished_at,
  i.created_at,
  i.updated_at,
  c.name as company_name,
  c.cnpj as company_cnpj,
  c.bio as company_bio,
  c.picture as company_picture,
  c.phone_numbers as company_phone_numbers,
  c.pix_key as company_pix_key,
  c.external_id as company_external_id,
  COALESCE(
    jsonb_build_object(
      'external_id', ca.external_id,
      'name', ca.name,
      'street', ca.street,
      'number', ca.number,
      'complement', ca.complement,
      'neighborhood', ca.neighborhood,
      'city', ca.city,
      'state', ca.state,
      'zip_code', ca.zip_code,
      'location', jsonb_build_object(
        'latitude', COALESCE(ca.latitude, 0),
        'longitude', COALESCE(ca.longitude, 0)
      )
    ),
    jsonb_build_object(
      'external_id', '',
      'name', '',
      'street', '',
      'number', '',
      'complement', '',
      'neighborhood', '',
      'city', '',
      'state', '',
      'zip_code', '',
      'location', jsonb_build_object(
        'latitude', 0,
        'longitude', 0
      )
    )
  )::jsonb as company_address,
  COALESCE(
    json_agg(
      json_build_object(
        'id', ip.id,
        'product_id', ip.product_id,
        'quantity', ip.quantity,
        'unit_price', ip.unit_price,
        'discount', ip.discount,
        'product_name', ip.product_name,
        'product_ean', ip.product_ean,
        'product_external_id', ip.product_external_id,
        'product_brand', ip.product_brand,
        'product_image', ip.product_image,
        'product_categories', (
          SELECT json_agg(DISTINCT jsonb_build_object(
            'name', cat.name,
            'image', cat.image,
            'external_id', cat.external_id
          ))
          FROM products_categories pc
          JOIN categories cat ON cat.id = pc.category_id
          WHERE pc.product_id = ip.product_id
        )
      )
    ) FILTER (WHERE ip.id IS NOT NULL),
    '[]'::json
  ) AS products,
  COUNT(*) OVER() as total_count
FROM invoices i
LEFT JOIN invoice_products ip ON i.id = ip.invoice_id
LEFT JOIN companies c ON i.company_id = c.id
LEFT JOIN company_addresses ca ON c.id = ca.company_id AND ca.is_default = true
WHERE i.order_id = $1 AND i.user_id = $2
GROUP BY i.id, c.name, c.cnpj, c.bio, c.picture, c.phone_numbers, c.pix_key, c.external_id, ca.external_id, ca.name, ca.street, ca.number, ca.complement, ca.neighborhood, ca.city, ca.state, ca.zip_code, ca.latitude, ca.longitude;